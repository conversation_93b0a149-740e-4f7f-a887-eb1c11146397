package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.AbundanceRequest;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import cn.dhbin.isme.ims.service.MorphologicalAnalysisDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/morphological-analysis-data")
@RequiredArgsConstructor
public class MorphologicalAnalysisDataController {
    private final MorphologicalAnalysisDataService morphologicalAnalysisDataService;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<MorphologicalAnalysisData>> selectAll(MorphologicalAnalysisDataRequest request) {
        Page<MorphologicalAnalysisData> ret = morphologicalAnalysisDataService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody MorphologicalAnalysisData data) {
        morphologicalAnalysisDataService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody MorphologicalAnalysisData data) {
        morphologicalAnalysisDataService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        morphologicalAnalysisDataService.removeById(id);
        return R.ok();
    }

    /**
     * 根据站点ID获取形态分析数据
     * @param stationId 站点ID
     * @return 形态分析数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<MorphologicalAnalysisData>> getByStationId(@PathVariable Integer stationId) {
        List<MorphologicalAnalysisData> list = morphologicalAnalysisDataService.getByStationId(stationId);
        return R.ok(list);
    }

}

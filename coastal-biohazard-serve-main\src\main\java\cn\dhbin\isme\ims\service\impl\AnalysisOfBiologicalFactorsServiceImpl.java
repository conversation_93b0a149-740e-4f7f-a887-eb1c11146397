package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.mapper.*;
import cn.dhbin.isme.ims.domain.dto.AnalysisOfBiologicalFactorsDto;
import cn.dhbin.isme.ims.domain.dto.excel.AnalysisOfBiologicalFactorsExcelDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.domain.request.AnalysisOfBiologicalFactorsRequest;
import cn.dhbin.isme.ims.service.AnalysisOfBiologicalFactorsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service("analysisOfBiologicalFactorsService")
public class AnalysisOfBiologicalFactorsServiceImpl extends ServiceImpl<AnalysisOfBiologicalFactorsMapper, AnalysisOfBiologicalFactors> implements AnalysisOfBiologicalFactorsService {

    @Autowired
    private AnalysisOfBiologicalFactorsMapper analysisOfBiologicalFactorsMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Autowired
    private AnalysisSampleMapper abundanceSampleMapper;

    @Autowired
    private AnalysisSampleTypeMapper sampleTypeMapper;

    @Override
    public Page<AnalysisOfBiologicalFactorsDto> queryPage(AnalysisOfBiologicalFactorsRequest request) {
        IPage<AnalysisOfBiologicalFactors> qp = request.toPage();
        LambdaQueryWrapper<AnalysisOfBiologicalFactors> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getSampleType() != null) {
            queryWrapper.eq(AnalysisOfBiologicalFactors::getSampleType, request.getSampleType());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(AnalysisOfBiologicalFactors::getDistributeId, request.getDistributeId());
        }

        IPage<AnalysisOfBiologicalFactors> ret = analysisOfBiologicalFactorsMapper.selectPage(qp, queryWrapper);

        IPage<AnalysisOfBiologicalFactorsDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            AnalysisOfBiologicalFactorsDto dataDto = new AnalysisOfBiologicalFactorsDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            // 获取与该丰富度相关的所有样本类型ID和数量
            List<SampleInfo> sampleInfos = abundanceSampleMapper.getSampleInfosByAbundanceId(data.getId());

            // 提取 sampleIds 并创建一个 map 来存储 number
            Map<Integer, Double> sampleIdToNumberMap = new HashMap<>();
            List<Integer> sampleIds = sampleInfos.stream()
                    .peek(sampleInfo -> sampleIdToNumberMap.put(sampleInfo.getSampleId(), sampleInfo.getNumber()))
                    .map(SampleInfo::getSampleId)
                    .collect(Collectors.toList());

            // 检查 sampleIds 是否为空
            List<AnalysisSampleType> sampleTypes = new ArrayList<>();
            if (!sampleIds.isEmpty()) {
                sampleTypes = sampleTypeMapper.selectBatchIds(sampleIds);
            }

            // 将 number 映射到 sampleTypes 中
            for (AnalysisSampleType sampleType : sampleTypes) {
                sampleType.setNumber(sampleIdToNumberMap.get(sampleType.getId()));
            }

            // 设置到DTO中
            dataDto.setSampleTypeList(sampleTypes);


            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public void addAbundance(Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes,String report) {
        AnalysisOfBiologicalFactors data = new AnalysisOfBiologicalFactors();
        data.setDistributeId(distributeId);
        data.setSampleType(sampleType);
        data.setAbundance(abundance);
        if (report!=null){
            data.setReport(report);
        }
        analysisOfBiologicalFactorsMapper.insert(data);
        Integer abundanceId = data.getId();

        // 检查 sampleTypeIds 是否为空或空列表
        if (sampleTypes == null || sampleTypes.isEmpty()) {
            return;
        }

        // 插入 abundance_sample 表
        List<AnalysisSample> samples = new ArrayList<>();
        for (SampleType s : sampleTypes) {
            AnalysisSample sample = new AnalysisSample();
            sample.setAbundanceId(abundanceId);
            sample.setSampleId(s.getId());
            sample.setNumber(s.getNumber());
            samples.add(sample);
        }
        analysisOfBiologicalFactorsMapper.insertBatch(samples);
    }


    @Override
    public void updateAbundance(Integer id, Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes,String report) {
        AnalysisOfBiologicalFactors existingData = analysisOfBiologicalFactorsMapper.selectById(id);

        if (existingData != null) {
            existingData.setDistributeId(distributeId);
            existingData.setSampleType(sampleType);
            existingData.setAbundance(abundance);
            if (report!=null){
                existingData.setReport(report);
            }
            analysisOfBiologicalFactorsMapper.updateById(existingData);

            Integer abundanceId = existingData.getId();

            if (sampleTypes == null || sampleTypes.isEmpty()) {
                abundanceSampleMapper.deleteByAbundanceId(abundanceId);
                return;
            }

            List<AnalysisSample> existingSamples = abundanceSampleMapper.selectByAbundanceId(abundanceId);
            List<Integer> sampleTypeIds = sampleTypes.stream().map(SampleType::getId).toList();
            Map<Integer, Double> sampleNumberMap = sampleTypes.stream()
                    .filter(st -> st.getNumber() != null)  // 过滤掉 number 为 null 的 SampleType
                    .collect(Collectors.toMap(SampleType::getId, SampleType::getNumber));

            List<Integer> samplesToDelete = new ArrayList<>();
            List<AnalysisSample> samplesToUpdate = new ArrayList<>();
            List<AnalysisSample> newSamples = new ArrayList<>();

            // 处理现有样本
            for (AnalysisSample existingSample : existingSamples) {
                Integer existingSampleId = existingSample.getSampleId();
                if (sampleTypeIds.contains(existingSampleId)) {
                    // 仅当sampleType不等于3时更新number
                    if (sampleType != 3) {
                        existingSample.setNumber(sampleNumberMap.get(existingSampleId));
                    }
                    samplesToUpdate.add(existingSample);
                } else {
                    samplesToDelete.add(existingSample.getId());
                }
            }

            // 处理新增样本
            for (SampleType currentSampleType : sampleTypes) {
                Integer sampleId = currentSampleType.getId();
                boolean isNewSample = existingSamples.stream()
                        .noneMatch(existing -> existing.getSampleId().equals(sampleId));
                if (isNewSample) {
                    AnalysisSample sample = new AnalysisSample();
                    sample.setAbundanceId(abundanceId);
                    sample.setSampleId(sampleId);
                    // 仅当sampleType不等于3时设置number
                    if (sampleType != 3) {
                        sample.setNumber(currentSampleType.getNumber());
                    }
                    newSamples.add(sample);
                }
            }

            // 执行批量操作
            if (!samplesToDelete.isEmpty()) {
                abundanceSampleMapper.deleteBatchIds(samplesToDelete);
            }
            samplesToUpdate.forEach(abundanceSampleMapper::updateById);
            if (!newSamples.isEmpty()) {
                analysisOfBiologicalFactorsMapper.insertBatch(newSamples); // 确保存在批量插入方法
            }
        }
    }

    @Override
    public List<AnalysisOfBiologicalFactorsDto> queryList(Integer distributeId) {
        LambdaQueryWrapper<AnalysisOfBiologicalFactors> queryWrapper = new LambdaQueryWrapper<>();


        if (distributeId != null) {
            queryWrapper.eq(AnalysisOfBiologicalFactors::getDistributeId, distributeId);
        }

        List<AnalysisOfBiologicalFactors> resultList = analysisOfBiologicalFactorsMapper.selectList(queryWrapper);

        List<AnalysisOfBiologicalFactorsDto> dtoList = resultList.stream().map(data -> {
            // 初始化目标对象
            AnalysisOfBiologicalFactorsDto dataDto = new AnalysisOfBiologicalFactorsDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            // 获取与该丰富度相关的所有样本类型ID和数量
            List<SampleInfo> sampleInfos = abundanceSampleMapper.getSampleInfosByAbundanceId(data.getId());

            // 提取 sampleIds 并创建一个 map 来存储 number
            Map<Integer, Double> sampleIdToNumberMap = new HashMap<>();
            List<Integer> sampleIds = sampleInfos.stream()
                    .peek(sampleInfo -> sampleIdToNumberMap.put(sampleInfo.getSampleId(), sampleInfo.getNumber()))
                    .map(SampleInfo::getSampleId)
                    .collect(Collectors.toList());

            // 检查 sampleIds 是否为空
            List<AnalysisSampleType> sampleTypes = new ArrayList<>();
            if (!sampleIds.isEmpty()) {
                sampleTypes = sampleTypeMapper.selectBatchIds(sampleIds);
            }

            // 将 number 映射到 sampleTypes 中
            for (AnalysisSampleType sampleType : sampleTypes) {
                sampleType.setNumber(sampleIdToNumberMap.get(sampleType.getId()));
            }


            // 设置到DTO中
            dataDto.setSampleTypeList(sampleTypes);


            return dataDto;
        }).collect(Collectors.toList());

        return dtoList;
    }

    @Override
    public void removeById(Integer id) {
        LambdaQueryWrapper<AnalysisSample> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AnalysisSample::getAbundanceId, id);
        abundanceSampleMapper.delete(wrapper);
        // 如果没有关联数据，执行删除
        super.removeById(id);
    }

    @Override
    public List<AnalysisOfBiologicalFactorsExcelDto> queryListForExport(AnalysisOfBiologicalFactorsRequest request) {
        LambdaQueryWrapper<AnalysisOfBiologicalFactors> queryWrapper = new LambdaQueryWrapper<>();

        // 筛选条件
        if (request.getSampleType() != null) {
            queryWrapper.eq(AnalysisOfBiologicalFactors::getSampleType, request.getSampleType());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(AnalysisOfBiologicalFactors::getDistributeId, request.getDistributeId());
        }

        List<AnalysisOfBiologicalFactors> dataList = analysisOfBiologicalFactorsMapper.selectList(queryWrapper);

        return dataList.stream().map(data -> {
            AnalysisOfBiologicalFactorsExcelDto excelDto = new AnalysisOfBiologicalFactorsExcelDto();

            // 基础字段
            excelDto.setId(String.valueOf(data.getId()));
            excelDto.setDistributeId(data.getDistributeId());

            // 站点名称
            StationPointDistribute station = stationPointDistributeMapper.selectById(data.getDistributeId());
            if (station != null) {
                excelDto.setDistributeName(station.getName());
            }

            // 处理生物门类及丰度
            List<SampleInfo> sampleInfos = abundanceSampleMapper.getSampleInfosByAbundanceId(data.getId());
            if (!sampleInfos.isEmpty()) {
                // 获取样本类型详情
                List<Integer> sampleIds = sampleInfos.stream()
                        .map(SampleInfo::getSampleId)
                        .collect(Collectors.toList());
                List<AnalysisSampleType> sampleTypes = sampleTypeMapper.selectBatchIds(sampleIds);

                // 创建ID到数量的映射
                Map<Integer, Double> numberMap = sampleInfos.stream()
                        .collect(Collectors.toMap(SampleInfo::getSampleId, SampleInfo::getNumber));

                // 拼接带单位的字符串
                excelDto.setSpeciesAbundance(sampleTypes.stream()
                        .map(st -> formatSpecies(st.getName(), numberMap.get(st.getId())))
                        .collect(Collectors.joining(", ")));
            }

            return excelDto;
        }).collect(Collectors.toList());
    }

    // 根据生物门类名称格式化带单位的值
    private String formatSpecies(String name, Double number) {
        if (number == null) return "";

        String unit = switch (name) {
            case "浒苔" -> "ind./L";
            case "微观繁殖体" -> "株/L";
            case "微生物", "病毒" -> "个/mL";
            default -> "未知单位";
        };
        return String.format("%s %.2f%s", name, number, unit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(List<AnalysisOfBiologicalFactorsExcelDto> dataList) {
        System.out.println(dataList);
        // 预处理检查
        Set<String> allSpecies = new HashSet<>();
        for (AnalysisOfBiologicalFactorsExcelDto dto : dataList) {
            if (StringUtils.isNotBlank(dto.getSpeciesAbundance())) {
                allSpecies.addAll(parseSpeciesNames(dto.getSpeciesAbundance()));
            }
        }

        List<String> missingSpecies = allSpecies.stream()
                .filter(name -> sampleTypeMapper.selectByName(name) == null)
                .collect(Collectors.toList());

        if (!missingSpecies.isEmpty()) {
            System.out.println("缺失生物门类：" + String.join(", ", missingSpecies));
            throw new BizException(BizResponseCode.ERR_400);
        }

        // 正式导入
        for (AnalysisOfBiologicalFactorsExcelDto dto : dataList) {
            Integer distributeId = resolveDistributeId(dto);

            AnalysisOfBiologicalFactors entity = new AnalysisOfBiologicalFactors();
            entity.setDistributeId(distributeId);
            analysisOfBiologicalFactorsMapper.insert(entity);

            System.out.println("创建主记录，ID：" + entity.getId());

            if (StringUtils.isNotBlank(dto.getSpeciesAbundance())) {
                try {
                    parseAndSaveSpecies(dto.getSpeciesAbundance(), Long.valueOf(entity.getId()));
                } catch (Exception e) {
                    System.out.println("处理记录失败：" + dto.toString());
                    throw new BizException(BizResponseCode.ERR_400);
                }
            }
        }
    }

    private static final Pattern SPECIES_PATTERN =
            Pattern.compile("(.+?)\\s+([\\d.]+)(?:\\s*([^\\d\\s]+))?");

    private Set<String> parseSpeciesNames(String speciesAbundance) {
        return Arrays.stream(speciesAbundance.split(",\\s*"))
                .map(item -> {
                    Matcher matcher = SPECIES_PATTERN.matcher(item.trim());
                    return matcher.find() ? matcher.group(1).trim() : null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private Integer resolveDistributeId(AnalysisOfBiologicalFactorsExcelDto dto) {
        if (dto.getDistributeId() != null) {
            return dto.getDistributeId();
        }
        if (StringUtils.isNotBlank(dto.getDistributeName())) {
            StationPointDistribute station = stationPointDistributeMapper.selectByName(dto.getDistributeName());
            if (station == null) {
                System.out.println("站点不存在: " + dto.getDistributeName());
                throw new BizException(BizResponseCode.ERR_400);
            }
            return station.getId();
        }
        System.out.println("站点信息不能为空");
        throw new BizException(BizResponseCode.ERR_400);
    }

    private void parseAndSaveSpecies(String speciesAbundance, Long abundanceId) {
        String[] items = speciesAbundance.split(",\\s*");
        for (String item : items) {
            // 使用正则表达式解析
            Matcher matcher = Pattern.compile("(.+?)\\s+([\\d.]+)(.+?)").matcher(item.trim());
            if (!matcher.find()) {
                System.out.println("格式错误: " + item);
                throw new BizException(BizResponseCode.ERR_400);
            }

            String name = matcher.group(1).trim();
            double number = Double.parseDouble(matcher.group(2));

            // 查询样本类型
            AnalysisSampleType sampleType = sampleTypeMapper.selectByName(name);
            if (sampleType == null) {
                System.out.println("生物门类不存在: " + name);
                throw new BizException(BizResponseCode.ERR_400);
            }

            // 保存关联关系
            AnalysisSample as = new AnalysisSample();
            as.setAbundanceId(Math.toIntExact(abundanceId));
            as.setSampleId(sampleType.getId());
            as.setNumber(number);
            abundanceSampleMapper.insert(as);
        }
    }
}


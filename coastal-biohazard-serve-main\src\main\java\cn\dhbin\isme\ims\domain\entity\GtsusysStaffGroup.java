package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 一线人员单位表(GtsusysStaffGroup)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:34:41
 */
@Data
@TableName("gtsusys_staff_group")
public class GtsusysStaffGroup implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 单位名称
     **/
    private String name;
    
public Serializable pkVal() {
          return null;
      }
}



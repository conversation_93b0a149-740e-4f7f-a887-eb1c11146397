package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 形态分析表(MorphologicalAnalysisData)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-29 12:50:56
 */
public interface MorphologicalAnalysisDataService extends IService<MorphologicalAnalysisData> {
    Page<MorphologicalAnalysisData> queryPage(MorphologicalAnalysisDataRequest request);

    /**
     * 根据站点ID获取形态分析数据
     * @param stationId 站点ID
     * @return 形态分析数据列表
     */
    List<MorphologicalAnalysisData> getByStationId(Integer stationId);
}


package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.auth.RoleType;
import cn.dhbin.isme.common.auth.Roles;
import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.service.impl.AbundanceLayerSpeciesDataExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/abundance")
@RequiredArgsConstructor
@Tag(name = "表层水样微观繁殖体Excel导入导出")
public class AbundanceLayerSpeciesDataExcelController {
    private final AbundanceLayerSpeciesDataExcelService excelService;

    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导出Excel")
    public ResponseEntity<byte[]> exportToExcel(@RequestParam(required = false) Integer distributeId) throws Exception {
        byte[] excelContent = excelService.exportToExcel(distributeId);
        
        String filename = URLEncoder.encode("微观繁殖体丰富度数据.xlsx", StandardCharsets.UTF_8.toString());
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", filename);
        headers.setCacheControl("no-cache, no-store, must-revalidate");
        headers.setPragma("no-cache");
        headers.setExpires(0);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(excelContent);
    }

    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导入Excel")
    public R<Void> importFromExcel(@RequestParam(required = false) Integer distributeId, @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请选择要导入的Excel文件"));
            }
            
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请上传Excel文件(.xlsx或.xls格式)"));
            }
            
            excelService.importFromExcel(file, distributeId);
            return R.ok();
        } catch (Exception e) {
            return R.build(new BizException(BizResponseCode.ERR_11011, "导入失败：" + e.getMessage()));
        }
    }
} 
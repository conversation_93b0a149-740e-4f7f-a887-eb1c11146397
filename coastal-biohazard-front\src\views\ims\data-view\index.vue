<template>
  <!-- <dv-full-screen-container> -->

  <div style="width: 100%;height: 100%;" flex="~ col" p3 justify-center items-center bg-dark>
    <dv-loading v-if="loading">
      <div color-white>
        Loading...
      </div>
    </dv-loading>
    <div v-else style="width: 100%;height: 100%;" flex="~ col" p3 justify-center items-center>
      <dv-decoration7 style="width:550px;height:30px;position: relative;top: 24px;">
        <!-- <div color-white font-300 style="font-size: 24px;">
          黄海浒苔绿潮生态灾害检测
        </div> -->
        <div style="width: 60%;display: flex;gap: 8px;color: white;align-items: center;justify-content: center;">
          <!-- <span>调查中心 </span> -->
          <n-select style="width: 33%;background-color:rgb(255 255 255 / 0%)" @update:value="changeOption"
            label-field="name" value-field="id" v-model:value="scaleId" filterable :options="stationOption"
            placeholder="请选择调查中心" />
          <span> 海岸带生物灾害监测</span>
        </div>

      </dv-decoration7>
      <!-- <dv-decoration5 :dur="2" style="width:300px;height:40px;" /> -->
      <dv-border-box1 style="margin-top: 16px;">
        <div class="main"
          style="display: flex;justify-content: space-between;height: 100%;padding: 14px;gap: 8px;width: 95%;margin: auto;">
          <div class="left"
            style="display: flex;flex-direction: column;gap: 4px;width: 28%;height: 100%;align-items: center;justify-content: space-between;">
            <dv-decoration-11 style="width:280px;height:55px;margin: auto;">
              <div color-green font-600 bg="~ dark/0"
                style="display: flex;align-items: center;justify-content: center;gap: 8px;">
                <span>站点</span>
                <n-select @update:value="changeOption2" style="width: 27%;" label-field="name" value-field="id"
                  v-model:value="distributeId" filterable :options="pointOption" placeholder="请选择站点" />
                <span>生物多样性分析</span>
              </div>
            </dv-decoration-11>
            <dv-border-box8 :dur="5">
              <div
                style="display: flex;width: 100%;height: 100%;flex-direction: column;align-items: center;padding: 8px;justify-content: space-between;"
                dv-bg>
                <VChart :option="chartOptionFD1" style="height: 33%;width: 100%;" autoresize />
                <VChart :option="chartOptionFD2" style="height: 33%;width: 100%;" autoresize />
                <VChart :option="chartOptionFD3" style="height: 33%;width: 100%;" autoresize />
                <!-- <VChart :option="chartOptionFD4" style="height: 24%;width: 100%;" autoresize /> -->

                <!-- <div>
                  <span style="color: #fff;font-weight: bold;font-size: 18px;">黄海海水化学成分分析</span>
                  <dv-scroll-ranking-board :config="config2" style="width:250px;height:13rem;margin-top:10px;" />
                </div> -->
                <!-- <dv-decoration2 :dur="2" style="width:80%;height:5px;" /> -->
                <!-- <div>
                  <span style="color: #fff;font-weight: bold;position: relative;top: 10px;font-size: 18px;">沉积物含量</span>
                  <dv-active-ring-chart :config="conf" style="width:180px;height:180px" :isDigitalFlop="false" />
                </div> -->
                <!-- <dv-decoration2 :dur="2" style="width:80%;height:5px;" /> -->
              </div>
            </dv-border-box8>
          </div>
          <div class="right" style="display: flex;flex-direction: column;gap: 8px;width: 80%;height: 100%;">
            <div class="top" style="display: flex;gap: 4px;width: 100%;height: 66%;">
              <div class="t-left" style="width: 55%;height: 100%;">
                <div style="width: 100%;height: 100%;" p3 lex="~ " justify-center items-center bg-dark>
                  <dv-border-box10>
                    <div style="width: 100%;height: 100%;" dv-bg>
                      <!-- <dv-flyline-chart-enhanced :config="config3" :dev="true" style="width:100%;height:100%;" /> -->
                      <VChart @click="handleChartClick" :option="chartOptions" autoresize />
                    </div>
                  </dv-border-box10>
                </div>
              </div>
              <div class="t-right" style="width: 45%;;display: flex; flex-direction: column;gap: 4px;">
                <dv-border-box8 :dur="5" :reverse="true">
                  <div style="width: 100%;height: 100%;position: relative;top: 24px;" dv-bg>
                    <VChart :option="chartOptionHX" autoresize />

                  </div>
                </dv-border-box8>
                <dv-border-box8 :dur="5" :reverse="true" style="display: flex;gap: 4px;">
                  <div style="width: 100%;height: 100%;" dv-bg>
                    <VChart :option="chartOptionsSW" autoresize />
                  </div>
                </dv-border-box8>
              </div>
            </div>
            <div class="bottom" style="width: 100%;height: 34%;">
              <dv-border-box12>
                <div
                  style="width: 100%;height: 100%;display: flex;flex-direction: row;justify-content: space-between;gap: 8px;">
                  <div style="width: 50%;height: 90%;" dv-bg>
                    <div
                      style="width: 100%;height: 100%;position: relative;top: 12px;display: flex;flex-direction: column;gap: 12px;">
                      <span style="color: #fff;font-weight: bolder;font-size: 18px;">一线作业人员表</span>
                      <dv-scroll-board ref="scrollBoard" :config="chartManage"
                        style="width:100%;height:80%;position: relative;left: 20px;" @mouseover="mouseoverHandler"
                        @click="clickHandler" />

                    </div>
                  </div>
                  <dv-decoration4 :dur="2" style="width:5px;height:80%;position: relative;left: 25px;bottom: -25px;" />
                  <div style="width: 45%;height: 100%;position: relative;top: 12px;">
                    <span style="color: #fff;font-weight: bolder;font-size: 18px">各站点调查频率</span>
                    <VChart :option="chartOptionTime" autoresize />
                  </div>
                </div>
              </dv-border-box12>
            </div>
          </div>
        </div>
      </dv-border-box1>
    </div>
  </div>
  <!-- </dv-full-screen-container> -->
</template>

<script setup>
import { useUserStore } from '@/store'
import { BarChart, LineChart, PieChart, ScatterChart, LinesChart, MapChart, EffectScatterChart, HeatmapChart } from 'echarts/charts'
import {
  GridComponent, LegendComponent, TooltipComponent, TitleComponent, GeoComponent, VisualMapComponent, CalendarComponent
} from 'echarts/components'
import * as echarts from 'echarts/core'
import api from './api'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import lianyungangGeoJson from './china.json'; // 假设 GeoJSON 文件路径正确
import VChart from 'vue-echarts'
import { watch } from 'vue'

// 定义props接收父组件传递的参数
const props = defineProps({
  scaleId: {
    type: Number,
    default: null
  },
  taskId: {
    type: Number,
    default: null
  },
  timesId: {
    type: Number,
    default: null
  },
  distributeId: {
    type: Number,
    default: null
  }
})

let loading = ref(true)
const stationOption = ref([])
// 使用props初始化状态，如果props为空则使用默认值
let distributeId = ref(props.distributeId || 1)
const scaleId = ref(props.scaleId || 1)

// 数据加载函数
const loadDataForScale = async () => {
  loading.value = true
  try {
    await getScaleOption()
    await getPointOption()
    await getOrganizedData()
    await getSurveyTimes()
  } catch (error) {
    console.error('加载调查中心数据失败:', error)
  }
  loading.value = false
}

const loadDataForStation = async () => {
  try {
    await getBiodiversityList()
    await getAbundance()
  } catch (error) {
    console.error('加载站点数据失败:', error)
  }
}

// 监听props变化，当父组件传递新的参数时更新组件状态
watch(() => props.scaleId, (newVal) => {
  if (newVal !== null && newVal !== undefined) {
    scaleId.value = newVal
    // 重新加载数据
    loadDataForScale()
  }
})

watch(() => props.distributeId, (newVal) => {
  if (newVal !== null && newVal !== undefined) {
    distributeId.value = newVal
    // 重新加载站点相关数据
    loadDataForStation()
  }
})

const mouseoverHandler = (e) => {
  console.log(e)
}

const clickHandler = (e) => {
  console.log(e)
}

const scrollBoard = ref(null)

echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
  PieChart,
  TitleComponent,
  GeoComponent,
  ScatterChart,
  LinesChart,
  MapChart,
  VisualMapComponent,
  EffectScatterChart,
  CalendarComponent,
  HeatmapChart
])

const chartOptionsSW = ref({
  title: {
    text: '水文数据分析',
    top: 26,
    left: 'center',
    textStyle: {
      color: '#fff' // 设置标题文字颜色为白色
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    }
  },
  legend: {
    data: ['盐度', 'pH', '气温', '水温', '透明度'],
    textStyle: {
      color: '#fff' // 设置图例文字颜色为白色
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '数值',
    axisLabel: {
      color: '#fff' // 设置 x 轴标签文字颜色为白色
    }
  },
  yAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#fff' // 设置 y 轴标签文字颜色为白色
    }
  },
  series: [
    {
      name: '盐度',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        color: '#fff' // 设置标签文字颜色为白色
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: 'pH',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        color: '#fff'
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    },
    {
      name: '气温',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        color: '#fff'
      },
      emphasis: {
        focus: 'series'
      },
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + '°C';
        }
      },
    },
    {
      name: '水温',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        color: '#fff'
      },
      emphasis: {
        focus: 'series'
      },
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + '°C';
        }
      },
    },
    {
      name: '透明度',
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
        color: '#fff'
      },
      emphasis: {
        focus: 'series'
      },
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + 'm';
        }
      },
    }
  ]
});

const chartOptionHX = ref({
  title: {
    text: '海水化学含量趋势',
    top: 25,
    left: 'center',
    textStyle: {
      color: '#fff' // 设置标题文字颜色为白色
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross'
    },
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    }
  },
  legend: {
    data: ['活性磷酸盐', '亚硝酸盐-氮', '硝酸盐-氮', '氨-氮'],
    textStyle: {
      color: '#fff' // 设置图例文字颜色为白色
    }
  },
  xAxis: {
    type: 'category',
    name: '站点',
    nameTextStyle: {
      color: '#fff' // 设置 x 轴标签文字颜色为白色
    },
    data: [],
    boundaryGap: false
  },
  yAxis: {
    type: 'value',
    name: '比例',
    nameTextStyle: {
      color: '#fff' // 设置 y 轴标签文字颜色为白色
    }
  },
  series: [
    {
      name: '活性磷酸盐',
      type: 'line',
      stack: '总量',
      areaStyle: {},
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + 'mg/L';
        }
      },
    },
    {
      name: '亚硝酸盐-氮',
      type: 'line',
      stack: '总量',
      areaStyle: {},
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + 'mg/L';
        }
      },
    },
    {
      name: '硝酸盐-氮',
      type: 'line',
      stack: '总量',
      areaStyle: {},
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + 'mg/L';
        }
      },
    },
    {
      name: '氨-氮',
      type: 'line',
      stack: '总量',
      areaStyle: {},
      data: [],
      tooltip: {
        valueFormatter: function (value) {
          return value + 'mg/L';
        }
      },
    }
  ]
});

const chartOptionTime = ref({
  tooltip: {
    trigger: 'item',
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    }
  },
  legend: {
    top: '5%',
    left: 'center',
    textStyle: {
      color: '#fff' // 设置图例文字颜色为白色
    }
  },
  series: [
    {
      name: '调查次数',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '70%'],
      // adjust the start and end angle
      startAngle: 180,
      endAngle: 360,
      data: []
    }
  ]
})

const sampleTypeMap = {
  0: '沉积物',
  1: '底层水样',
  2: '表层水样',
  3: '藻样'
};

const chartOptionFD1 = ref({
  title: {
    text: '',
    // subtext: '各藻种比例',
    top: -2,
    left: 'center',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    },
    subtextStyle: {
      color: '#fff' // 设置副标题颜色为白色
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: function (params) {
      // 获取当前数据项的颜色
      const color = params.color;
      // 创建一个小圆圈并设置其背景颜色
      const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
      return `${circle} ${params.typeName}: ${params.value}ind./L`;
    },
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    }
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    }
  },
  series: [
    {
      name: '藻种名称',
      type: 'pie',
      radius: '50%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },

    }
  ]
});

const chartOptionFD2 = ref({
  title: {
    text: '',
    // subtext: '各藻种比例',
    top: -2,
    left: 'center',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    },
    subtextStyle: {
      color: '#fff' // 设置副标题颜色为白色
    }
  },
  tooltip: {
    trigger: 'item',
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    },
    formatter: function (params) {
      // 获取当前数据项的颜色
      const color = params.color;
      // 创建一个小圆圈并设置其背景颜色
      const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
      return `${circle} ${params.typeName}: ${params.value}ind./L`;
    },
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    }
  },
  series: [
    {
      name: '藻种名称',
      type: 'pie',
      radius: '50%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
});

const chartOptionFD3 = ref({
  title: {
    text: '',
    // subtext: '各藻种比例',
    left: 'center',
    top: -2,
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    },
    subtextStyle: {
      color: '#fff' // 设置副标题颜色为白色
    }
  },
  tooltip: {
    trigger: 'item',
    textStyle: {
      color: '#000' // 设置提示框文字颜色为白色
    },
    formatter: function (params) {
      // 获取当前数据项的颜色
      const color = params.color;
      // 创建一个小圆圈并设置其背景颜色
      const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
      return `${circle} ${params.typeName}: ${params.value}ind./L`;
    },
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    }
  },
  series: [
    {
      name: '藻种名称',
      type: 'pie',
      radius: '50%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
});

const chartOptionFD4 = ref({
  title: {
    text: '',
    // subtext: '各藻种比例',
    left: 'center',
    top: -2,
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    },
    subtextStyle: {
      color: '#fff', // 设置副标题颜色为白色
    }
  },
  tooltip: {
    trigger: 'item',
    textStyle: {
      color: '#000'
    },
    formatter: function (params) {
      // 获取当前数据项的颜色
      const color = params.color;
      // 创建一个小圆圈并设置其背景颜色
      const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
      return `${circle} ${params.name}: ${params.value}g`;
    },
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    textStyle: {
      color: '#fff' // 设置标题颜色为白色
    }
  },
  series: [
    {
      name: '藻种名称',
      type: 'pie',
      radius: '50%',
      data: [],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
});

const surveyTimes = ref([])
const getSurveyTimes = async () => {
  let { data } = await api.getSurveyTimes(scaleId.value)
  console.log(data);
  surveyTimes.value = data
  chartOptionTime.value.series[0].data = surveyTimes.value.map(item => ({
    value: item.count,
    name: item.distributeName
  }))
}

const centerPoint = ref([])

const pointOption = ref([])
const getPointOption = async () => {
  let { data } = await api.getListStationPoints(scaleId.value)
  console.log(data, "pointOption");
  distributeId.value = data[0].id
  pointOption.value = data

  chartOptions.value.series[1].data = pointOption.value.map(item => ({
    name: item.name,
    value: [item.longitude, item.latitude]
  }))

  chartOptions.value.series[2].data = pointOption.value.map(item => ({
    coords: [centerPoint.value, [item.longitude, item.latitude]]
  }));
}

const getScaleOption = async () => {
  let { data } = await api.getStationPoints()
  console.log(data)
  stationOption.value = data
  const filteredStations = stationOption.value.filter(station => station.id === scaleId.value);
  chartOptions.value.series[0].data = filteredStations.map(item => ({
    name: item.name,
    value: [item.longitude, item.latitude]
  }));
  centerPoint.value = [filteredStations[0].longitude, filteredStations[0].latitude]
  chartOptions.value.geo.center = centerPoint.value;
}

const getAbundance = async () => {
  let { data } = await api.getAbundanceList(distributeId.value)
  console.log(data, "22");
  // 遍历数据，根据 sampleType 分类
  data.forEach(item => {
    const sampleTypeName = sampleTypeMap[item.sampleType];
    const sampleTypeData = item.sampleTypeList.map(sampleType => ({
      name: sampleType.name,
      value: sampleType.number
    }));

    switch (item.sampleType) {
      case 0:
        chartOptionFD1.value.series[0].data = sampleTypeData;
        chartOptionFD1.value.title.text = sampleTypeName;
        break;
      case 1:
        chartOptionFD2.value.series[0].data = sampleTypeData;
        chartOptionFD2.value.title.text = sampleTypeName;
        break;
      case 2:
        chartOptionFD3.value.series[0].data = sampleTypeData;
        chartOptionFD3.value.title.text = sampleTypeName;
        break;
      case 3:
        chartOptionFD4.value.series[0].data = sampleTypeData;
        chartOptionFD4.value.title.text = sampleTypeName;
        break;
      default:
        console.warn(`Unknown sampleType: ${item.sampleType}`);
    }
  });
  // if(data.sampleType)
  // chartOptionFD.value.series[0].data = data.map(item => ({
  //   value: item.abundance,
  //   name: `${sampleTypeMap[item.sampleType]}: ${item.sampleTypeList.map(sampleType => sampleType.name).join(', ')}`
  // }));
}

const type = [
  { label: '浮游植物', value: 0 },
  { label: '浮游动物', value: 1 },
  { label: '底栖生物', value: 2 },
]

const b1=ref([])
const b2=ref([])
const b3=ref([])

const getBiodiversityList = async () => {
  let { data } = await api.getBiodiversityList(distributeId.value)
  console.log(data,"biodiversityList");
  b1.value=data.map(item=>({
    bName:item.name,
    name:type.find(i => item.type === i.value)?.label,
    value:item.havg
  }))
  b2.value=data.map(item=>({
    bName:item.name,
    name:type.find(i => item.type === i.value)?.label,
    value:item.javg
  }))
  b3.value=data.map(item=>({
    bName:item.name,
    name:type.find(i => item.type === i.value)?.label,
    value:item.davg
  }))

  console.log(b1.value,'b1');

  chartOptionFD1.value.series[0].data = b1.value;
  chartOptionFD1.value.title.text = "生物多样性指数平均值";
  chartOptionFD1.value.tooltip.formatter = function (params) {
  // 获取当前数据项的颜色
  const color = params.color;
  // 创建一个小圆圈并设置其背景颜色
  const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
  // 显示完整的名称
  const name = params.data.bName;
  return `${circle} ${name}: ${params.value}`;
};

  chartOptionFD2.value.series[0].data = b2.value;
  chartOptionFD2.value.title.text = "均匀度指数平均值";
  chartOptionFD2.value.tooltip.formatter = function (params) {
  // 获取当前数据项的颜色
  const color = params.color;
  // 创建一个小圆圈并设置其背景颜色
  const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
  // 显示完整的名称
  const name = params.data.bName;
  return `${circle} ${name}: ${params.value}`;
};

  chartOptionFD3.value.series[0].data = b3.value;
  chartOptionFD3.value.title.text = "丰富度指数平均值";
  chartOptionFD3.value.tooltip.formatter = function (params) {
  // 获取当前数据项的颜色
  const color = params.color;
  // 创建一个小圆圈并设置其背景颜色
  const circle = `<span style="display:inline-block; width:10px; height:10px; background-color:${color}; border-radius:50%; margin-right:5px;"></span>`;
  // 显示完整的名称
  const name = params.data.bName;
  return `${circle} ${name}: ${params.value}`;
};
}

echarts.registerMap('中国', lianyungangGeoJson);
// 注册地图数据
onMounted(async () => {
  echarts.registerMap('中国', lianyungangGeoJson);
  
  // 如果有props传递进来，使用props的值初始化
  if (props.scaleId !== null && props.scaleId !== undefined) {
    scaleId.value = props.scaleId
  }
  if (props.distributeId !== null && props.distributeId !== undefined) {
    distributeId.value = props.distributeId
  }
  
  await getScaleOption()
  await getPointOption()
  await getOrganizedData()
  getBiodiversityList()
  // getAbundance()
  getManage()
  getSurveyTimes()
  loading.value = false
});

const organizedData = ref([])
const fieldDescription = ref([])
const chemicalIonList=ref([])
const getOrganizedData = async () => {
  let { data } = await api.getOrganizedData(scaleId.value)
  console.log(data);
  if (data.waterPhWeatherDataList.length == 0) {
    $message.warning('暂无详细数据')
  }
  organizedData.value = data.waterPhWeatherDataList
  fieldDescription.value = data.fieldDescriptionList
  chemicalIonList.value=data.chemicalIonList
  chartOptionsSW.value.yAxis.data = organizedData.value.map(item => `站点 ${item.distributeName} ${item.sampleLayer == 2 ? "表层" : "底层"}`)
  chartOptionsSW.value.series[0].data = organizedData.value.map(item => item.saltExtent)
  chartOptionsSW.value.series[1].data = organizedData.value.map(item => item.phExtent)
  chartOptionsSW.value.series[2].data = organizedData.value.map(item => item.airTemperature)
  chartOptionsSW.value.series[3].data = organizedData.value.map(item => item.waterTemperature)
  chartOptionsSW.value.series[4].data = organizedData.value.map(item => item.transparentExtent)

  chartOptionHX.value.xAxis.data = chemicalIonList.value.map(item => `站点 ${item.distributeName} ${item.sampleLayer == 2 ? "表层" : "底层"}`)
  chartOptionHX.value.series[0].data = chemicalIonList.value.map(item => item.activePhosphate)
  chartOptionHX.value.series[1].data = chemicalIonList.value.map(item => item.nitriteNitrogen)
  chartOptionHX.value.series[2].data = chemicalIonList.value.map(item => item.nitrateNitrogen)
  chartOptionHX.value.series[3].data = chemicalIonList.value.map(item => item.ammoniaHydrogen)

}

const getManage = async () => {
  let { data } = await api.getManageList()
  console.log(data);
  chartManage.data = data.map(item => [item.name, item.gender ? '男' : '女', item.groupName])
}

const changeOption = async (row) => {
  // loading.value = true
  console.log(row);
  scaleId.value = row
  getOrganizedData()
  await getScaleOption()
  await getPointOption()
  getBiodiversityList()
  // await getAbundance()
  // loading.value = false
}

const changeOption2 = async (row) => {
  // loading.value = true
  distributeId.value = row
  await getBiodiversityList()
  // await getAbundance()
  // loading.value = false
}

const config = reactive({
  data: [
    {
      name: '浒苔',
      value: 87.11, // 浒苔含量为 87.11 ind./L
      unit: 'ind./L'
    },
    {
      name: '缘管浒苔',
      value: 957, // 缘管浒苔含量为 957 ind./50g
      unit: 'ind./50g'
    },
    {
      name: '扁浒苔',
      value: 234, // 扁浒苔含量为 234 ind./50g
      unit: 'ind./50g'
    },
    {
      name: '其他浒苔',
      value: 123, // 其他浒苔含量为 123 ind./50g
      unit: 'ind./50g'
    },
    {
      name: '蓝藻',
      value: 56, // 蓝藻含量为 56 ind./L
      unit: 'ind./L'
    },
  ],
  colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'], unit: 'ind./L'
  // 可以在这里添加其他配置项，如单位、标签数量等
});

const conf = reactive({
  lineWidth: 24,
  digitalFlopStyle: {
    fill: 'pink',
  },
  data: [
    {
      name: '沙子',
      value: 40, // 假设沙子占总沉积物的40%
    },
    {
      name: '淤泥',
      value: 35, // 淤泥占35%
    },
    {
      name: '珊瑚碎屑',
      value: 15, // 珊瑚碎屑占15%
    },
    {
      name: '有机质',
      value: 10, // 有机质占10%
    },
  ],
});
const config2 = reactive({
  data: [
    {
      name: 'NO₃⁻ (硝酸盐)',
      value: 0.5,
    },
    {
      name: 'NO₂⁻ (亚硝酸盐)',
      value: 0.1,
    },
    {
      name: 'NH₄⁺ (铵离子)',
      value: 0.2,
    },
    {
      name: 'PO₄³⁻ (磷酸盐)',
      value: 0.05,
    },
    {
      name: 'SiO₄⁴⁻ (硅酸盐)',
      value: 1.0,
    },
    {
      name: 'Fe²⁺ (亚铁离子)',
      value: 0.02,
    },
    {
      name: 'Mn²⁺ (锰离子)',
      value: 0.01,
    },
    {
      name: 'SO₄²⁻ (硫酸盐)',
      value: 28.0,
    },
    {
      name: 'Cl⁻ (氯离子)',
      value: 19.0,
    },
    {
      name: 'Ca²⁺ (钙离子)',
      value: 400.0,
    },
    {
      name: 'Mg²⁺ (镁离子)',
      value: 1300.0,
    },
    {
      name: 'Na⁺ (钠离子)',
      value: 10800.0,
    },
    {
      name: 'K⁺ (钾离子)',
      value: 380.0,
    },
    {
      name: 'Sr²⁺ (锶离子)',
      value: 0.8,
    },
    {
      name: 'HCO₃⁻ (碳酸氢盐)',
      value: 1.5,
    },
  ],
  unit: 'mg/L',
});
const chartManage = reactive({
  header: ['姓名', '性别', '单位'],
  /*

    ['<span style="color:#37a2da;">张三</span>', '男', '采样组'],
    ['李四', '<span style="color:#32c5e9;">女</span>', '分析组'],
    ['王五', '男', '<span style="color:#67e0e3;">生物组</span>'],
    ['赵六', '<span style="color:#9fe6b8;">男</span>', '化学组'],
    ['<span style="color:#ffdb5c;">陈七</span>', '女', '采样组'],
    ['刘八', '<span style="color:#ff9f7f;">男</span>', '分析组'],
    ['孙九', '女', '<span style="color:#fb7293;">生物组</span>'],
    ['周十', '<span style="color:#e062ae;">男</span>', '化学组'],
    ['<span style="color:#e690d1;">吴十一</span>', '女', '采样组'],
    ['郑十二', '<span style="color:#e7bcf3;">男</span>', '分析组'],

  */
  data: [],
  index: true,
  columnWidth: [40, 60, 135],
  align: ['center', 'center', 'center'],
});

const chartOptions = ref({
  title: {
    top: 15,
    left: 'center',
    text: '空间调查范围',
    textStyle: {
      color: '#fff' // 设置文字颜色为白色
    }
  },
  geo: {
    map: '中国',
    aspectScale: 0.8,
    layoutCenter: ['50%', '50%'], // 地图位置
    layoutSize: '75%',
    center: [119.22, 34.65], // 初始中心点，设置为连云港附近的黄海区域
    zoom: 20, // 初始缩放级别，数值越大，缩放越大
    roam: true, // 启用缩放和平移
    backgroundColor: '#f0f0f0', // 设置地图背景颜色为亮色
    itemStyle: {
      normal: {
        areaColor: '#000',
        borderWidth: 1, // 设置外层边框
        borderColor: '#f8911b',
      },
      emphasis: {
        show: true,
        areaColor: '#01215c'
      }
    }
  },
  tooltip: {
    trigger: 'item',
    backgroundColor: 'rgba(166, 200, 76, 0.82)',
    borderColor: '#FFFFCC',
    showDelay: 0,
    hideDelay: 0,
    enterable: true,
    transitionDuration: 0,
    extraCssText: 'z-index:100',
    formatter: function (params) {
      if (params.seriesType === 'scatter' || params.seriesType === 'effectScatter') {
        // 对于散点图，显示名称和经纬度
        return `<span style='color:#333333;'>${params.name}</span><br/>经度: ${params.value[0]}, 纬度: ${params.value[1]}`;
      } else {
        // 对于其他类型的图表，不显示任何提示信息
        return '';
      }
    }
  },
  series: [
    {
      name: '中心点',
      type: 'effectScatter',
      coordinateSystem: 'geo',
      symbol: 'circle',
      symbolSize: 15,
      itemStyle: {
        color: 'yellow', // 中心点颜色
      },
      rippleEffect: {
        brushType: 'stroke',
        scale: 5,
        period: 4
      },
      data: [
        { name: '中心点', value: [119.22, 34.65] }
      ]
    },
    {
      name: '站点',
      type: 'scatter',
      coordinateSystem: 'geo',
      symbol: 'circle',
      symbolSize: 10,
      itemStyle: {
        color: 'blue', // 站点颜色
      },
      data: []
    },
    {
      type: 'lines',
      coordinateSystem: 'geo',
      zlevel: -1,
      effect: {
        show: true,
        period: 4,
        trailLength: 0.7,
        color: '#007bff', // 蓝色
        symbolSize: 3
      },
      lineStyle: {
        normal: {
          color: '#007bff', // 蓝色
          width: 1,
          opacity: 0.6,
          curveness: 0.2
        }
      },
      data: []
    }
  ],
});

const handleChartClick = async (params) => {
  if (params.seriesName === '站点') {
    console.log('Clicked on a site:', params.data);
    console.log(pointOption.value);
    const newId = pointOption.value.filter((item) => {
      if (item.name === params.data.name) {
        return item
      }
    })

    distributeId.value = newId[0].id
    // await getBiodiversityList()
    await getBiodiversityList()
    // 在这里你可以根据需要处理数据
  }
};


</script>

<style lang="scss">
.n-base-selection .n-base-selection-label {
  background-color: rgb(255 255 255 / 90%);
  // border-color: rgb(147 197 253 / 0.5);
}
</style>

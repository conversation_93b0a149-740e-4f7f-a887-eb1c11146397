<!--------------------------------
-现场调查时空分析模块
-1.明确调查空间范围
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex;gap: 24px;">
        <!-- 导出/导入功能 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>
        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
        <!-- <NButton type="primary" @click="addCoordinate()">
          <i class="i-material-symbols:add mr-4 text-18" />
          添加经纬度
        </NButton> -->
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.readScale">
      <MeQueryItem label="调查中心" :label-width="70">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入调查中心名称" clearable />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" label-placement="left" label-align="left" :label-width="120" :model="modalForm"
        :disabled="modalAction === 'view'"
      >
      <n-form-item
            label="调查中心" path="name" :rule="{
              required: true,
              message: '请输入调查中心名称',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input placeholder="请输入调查中心名称"  v-model:value="modalForm.name" />
          </n-form-item>
          <n-form-item
            label="经度" path="longitude" :rule="{
              required: true,
              message: '请输入经度',
              type: 'number',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input-number style="width: 100%;" placeholder="请输入经度"  v-model:value="modalForm.longitude" />
          </n-form-item>
          <n-form-item
            label="纬度" path="latitude" :rule="{
              required: true,
              message: '请输入纬度',
              type: 'number',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input-number style="width: 100%;" placeholder="请输入纬度"  v-model:value="modalForm.latitude" />
          </n-form-item>
          <n-form-item
            label="概述" path="description" :rule="{
              // required: true,
              message: '请输入调查中心概述',
              trigger: ['input', 'blur'],
            }"
          >
            <n-input placeholder="请输入调查中心概述" v-model:value="modalForm.description" />
          </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NTag } from 'naive-ui'
import api from './api'
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportScale({ name: queryItems.value.name })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `明确调查空间范围_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    $message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    $message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importScale(formData)

    if (code === 0) {
      $message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      $message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    $message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}

onMounted(() => {
  $table.value?.handleSearch()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  // eslint-disable-next-line unused-imports/no-unused-vars
  handleSave,
  handleEdit,
} = useCrud({
  name: '调查中心',
  initForm: { enable: true },
  doCreate: api.createScale,
  doDelete: api.deleteScale,
  doUpdate: api.updateScale,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  { title: '名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '经度', key: 'longitude', ellipsis: { tooltip: true } },
  { title: '纬度', key: 'latitude', ellipsis: { tooltip: true } },
  { title: '空间范围描述', key: 'description', ellipsis: { tooltip: true } },
  // {
  //   title: '创建时间',
  //   key: 'createTime',
  //   width: 180,
  //   render(row) {
  //     return h('span', formatDateTime(row.createTime))
  //   },
  // },
  {
    // width: 260,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
      // h(
      //     NButton,
      //     {
      //       size: 'small',
      //       type: 'primary',
      //       dashed:true,
      //       // secondary: true,
      //       onClick: () =>
      //         router.push({ path: `/ims/JsVue`, query: { scaleId: row.id,scaleName: row.name } }),
      //     },
      //     {
      //       default: () => '调查站点分布',
      //       icon: () => h('i', { class: 'i-fe:map-pin text-14' }),
      //     },
      //   ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

// 用于存储经纬度的数组
const coordinates = ref([])

// 从 SessionStorage 中加载数据
function loadCoordinatesFromStorage() {
  const storedCoordinates = sessionStorage.getItem('coordinates')
  if (storedCoordinates) {
    coordinates.value = JSON.parse(storedCoordinates)
  }
}

// 将数据保存到 SessionStorage
function saveCoordinatesToStorage() {
  sessionStorage.setItem('coordinates', JSON.stringify(coordinates.value))
}

// 添加新坐标
function onAdd() {
  coordinates.value.push([null, null])
  saveCoordinatesToStorage()
}

// 移除指定索引处的坐标
function removeCoordinate(index) {
  coordinates.value.splice(index, 1)
  saveCoordinatesToStorage()
}

// 初始化时加载数据
onMounted(() => {
  loadCoordinatesFromStorage()
})

// 监听坐标变化，并在变化时保存到 SessionStorage
watch(coordinates, () => {
  saveCoordinatesToStorage()
}, { deep: true })

function addCoordinate() {
  handleOpen({
    action: 'setCoordinate',
    title: '添加经纬度--显示图形',
    onOk: () => {
      // 在这里可以执行额外的操作，比如通知或日志记录
      const modalFormDataStr = sessionStorage.getItem('coordinates');
      if (modalFormDataStr) {
        // 假设这里已经处理了 modalFormDataStr 的数据
        // 进行页面跳转
        // router.push
        window.location.href = 'JsShow.vue'
      }
    },
  });
}

// async function handleEnable(row) {
//   row.enableLoading = true
//   try {
//     await api.update({ id: row.id, enable: !row.enable })
//     row.enableLoading = false
//     $message.success('操作成功')
//     $table.value?.handleSearch()
//   }
//   catch (error) {
//     console.error(error)
//     row.enableLoading = false
//   }
// }
</script>

<style lang="scss" scoped></style>

<template>
  <div class="professional-dashboard">
    <!-- 纯黑色背景 -->
    <div id="ocean-animations"></div>

    <!-- 🆕 状态消息显示区域 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      <span class="status-icon">
        {{ statusType === 'success' ? '✅' : statusType === 'warning' ? '⚠️' : statusType === 'error' ? '❌' : 'ℹ️' }}
      </span>
      <span class="status-text">{{ statusMessage }}</span>
    </div>

    <!-- 🆕 滚动提示区域 -->
    <div class="scroll-hints">
      <div class="scroll-hint vertical">
        <span class="hint-icon">⬇️</span>
        <span class="hint-text">滚轮向下查看更多</span>
      </div>
      <div class="scroll-hint horizontal">
        <span class="hint-icon">➡️</span>
        <span class="hint-text">Ctrl+滚轮左右滑动</span>
      </div>
    </div>

    <!-- 顶部标题区域 - 固定定位 -->
    <div class="dashboard-header-fixed">
      <div class="left-section">
        <div class="time-display">{{ currentTime }}</div>
        <div class="station-info">
          <span class="station-name">🎯 {{ currentStationName }}</span>
          <span class="coordinates">📍 {{ currentStationCoordsText }}</span>
        </div>
      </div>
      
      <div class="center-title-compact">
        <h1 class="main-title-compact">🌊 海洋生态智能监测专业大屏</h1>
        
        <!-- 🆕 紧凑型选择器 -->
        <div class="selectors-container-compact">
          <div class="selector-group-compact">
            <NSelect
              v-model:value="selectedInvestigationCenter"
              :options="investigationCenterOptions"
              placeholder="调查中心"
              size="small"
              style="width: 100px;"
              @click="debugCenterClick"
              @update:value="debugCenterSelect"
              filterable
              clearable
            />
          </div>
          
          <div class="selector-group-compact">
            <NSelect
              v-model:value="selectedRoute"
              :options="routeOptions"
              placeholder="航线"
              size="small"
              style="width: 100px;"
              :disabled="!selectedInvestigationCenter"
              @click="debugRouteClick"
              @update:value="debugRouteSelect"
              filterable
              clearable
            />
          </div>
          
          <div class="selector-group-compact">
            <NSelect
              v-model:value="selectedTime"
              :options="timeOptions"
              placeholder="调查次数"
              size="small"
              style="width: 80px;"
              :disabled="!selectedRoute"
              @click="debugTimeClick"
              @update:value="debugTimeSelect"
              filterable
              clearable
            />
          </div>
          
          <div class="selector-group-compact">
            <NSelect
              v-model:value="selectedStationId"
              :options="stationOptions"
              placeholder="站点"
              size="small"
              style="width: 80px;"
              :disabled="!selectedTime"
              @click="debugStationClick"
              @update:value="debugStationSelect"
              filterable
              clearable
            />
          </div>
          
          <NButton 
            type="primary" 
            size="small"
            @click="refreshData"
            :loading="isLoading"
            :disabled="!selectedStationId"
            class="refresh-btn-compact"
          >
            🔄
          </NButton>
          
          <!-- 🆕 调试按钮 -->
          <NButton 
            type="warning" 
            size="small"
            @click="debugAndReload"
            class="debug-btn-compact"
            title="调试级联选择器"
          >
            🐛
          </NButton>
        </div>
      </div>
      
      <div class="right-section">
        <NButton 
          type="primary" 
          size="small"
          @click="switchToDashboard"
          class="switch-btn-compact"
          :loading="isLoading"
        >
          ⬅️ 返回
        </NButton>
      </div>
    </div>

    <!-- 🆕 可滚动的主要内容区域 -->
    <div class="dashboard-content-scrollable" @wheel="handleScroll" @touchstart="handleTouchStart" @touchmove="handleTouchMove">
      <!-- 第一行：紧凑型水文监测 -->
      <div class="chart-row compact-row">
        <!-- 实时水文参数监测 - 紧凑版 -->
        <div class="chart-panel compact-panel hydro-panel-compact">
          <div class="panel-header-compact">
            <h4>💧 水文监测</h4>
            <div class="status-indicator-compact active">🟢</div>
          </div>
          <div class="hydro-grid-compact">
            <div class="hydro-item-compact">
              <div class="value-compact">{{ hydroData.salinity }}</div>
              <div class="label-compact">盐度(PSU)</div>
            </div>
            <div class="hydro-item-compact">
              <div class="value-compact">{{ hydroData.ph }}</div>
              <div class="label-compact">pH值</div>
            </div>
            <div class="hydro-item-compact">
              <div class="value-compact">{{ hydroData.waterTemperature }}</div>
              <div class="label-compact">水温(℃)</div>
            </div>
            <div class="hydro-item-compact">
              <div class="value-compact">{{ hydroData.transparency }}</div>
              <div class="label-compact">透明度(m)</div>
            </div>
          </div>
        </div>

        <!-- 水文参数时间序列 - 紧凑版 -->
        <div class="chart-panel compact-panel time-series-panel-compact">
          <div class="panel-header-compact">
            <h4>📈 时间序列</h4>
            <div class="chart-controls-compact">
              <NButton size="tiny" :type="timeSeriesType === 'salinity' ? 'primary' : 'default'" @click="timeSeriesType = 'salinity'">盐度</NButton>
              <NButton size="tiny" :type="timeSeriesType === 'ph' ? 'primary' : 'default'" @click="timeSeriesType = 'ph'">pH</NButton>
              <NButton size="tiny" :type="timeSeriesType === 'temperature' ? 'primary' : 'default'" @click="timeSeriesType = 'temperature'">水温</NButton>
            </div>
          </div>
          <div ref="timeSeriesChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 微观繁殖体丰度趋势 - 紧凑版 -->
        <div class="chart-panel compact-panel abundance-trend-panel-compact">
          <div class="panel-header-compact">
            <h4>🦠 微观繁殖体</h4>
            <div class="trend-indicator-compact">
              <span :class="['trend-arrow', abundanceTrend]">{{ abundanceTrend === 'up' ? '↗️' : abundanceTrend === 'down' ? '↘️' : '➡️' }}</span>
            </div>
          </div>
          <div ref="abundanceTrendChartRef" class="chart-container-compact"></div>
        </div>
      </div>

      <!-- 第二行：生物多样性和化学分析 -->
      <div class="chart-row compact-row">
        <!-- 化学成分对比分析 - 紧凑版 -->
        <div class="chart-panel compact-panel chemical-panel-compact">
          <div class="panel-header-compact">
            <h4>🧪 化学成分</h4>
            <div class="layer-indicator-compact">
              <span class="layer-compact surface">表层</span>
              <span class="layer-compact bottom">底层</span>
            </div>
          </div>
          <div ref="chemicalComparisonChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 生物多样性群落结构 - 改进版柱状图 -->
        <div class="chart-panel compact-panel biodiversity-panel-compact">
          <div class="panel-header-compact">
            <h4>🐟 生物多样性指数</h4>
            <div class="diversity-stats-compact">
              <span class="stat-compact">4类群落</span>
              <span class="stat-compact">16项指标</span>
            </div>
          </div>
          <div ref="biodiversityChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 重金属污染监测 - 紧凑版 -->
        <div class="chart-panel compact-panel metal-pollution-panel-compact">
          <div class="panel-header-compact">
            <h4>⚠️ 重金属监测</h4>
            <div class="pollution-level-compact" :class="overallPollutionLevel">
              {{ getPollutionLevelText(overallPollutionLevel) }}
            </div>
          </div>
          <div ref="metalPollutionChartRef" class="chart-container-compact"></div>
        </div>
      </div>

      <!-- 第三行：微观分析和AI预测 -->
      <div class="chart-row compact-row">
        <!-- 微观繁殖体种类分布 - 紧凑版 -->
        <div class="chart-panel compact-panel micro-species-panel-compact">
          <div class="panel-header-compact">
            <h4>🔬 微观种类分布</h4>
            <div class="species-count-compact">{{ microSpeciesData.length }}种</div>
          </div>
          <div ref="microSpeciesChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 海洋环境综合分析图 - 替换显微形态分析 -->
        <div class="chart-panel compact-panel environmental-analysis-panel-compact">
          <div class="panel-header-compact">
            <h4>🌊 环境综合分析</h4>
            <div class="analysis-status-compact">
              <span class="status-compact excellent">优秀</span>
            </div>
          </div>
          <div ref="environmentalAnalysisChartRef" class="chart-container-compact"></div>
        </div>

        <!-- AI水质预测 - 紧凑版 -->
        <div class="chart-panel compact-panel ai-prediction-panel-compact">
          <div class="panel-header-compact">
            <h4>🤖 AI水质预测</h4>
            <div class="prediction-accuracy-compact">准确率 {{ aiPredictionAccuracy }}%</div>
          </div>
          <div ref="aiPredictionChartRef" class="chart-container-compact"></div>
        </div>
      </div>

      <!-- 第四行：沉积物和时空分析 -->
      <div class="chart-row compact-row">
        <!-- 沉积物成分分析 - 紧凑版 -->
        <div class="chart-panel compact-panel sediment-panel-compact">
          <div class="panel-header-compact">
            <h4>🏖️ 沉积物成分</h4>
            <div class="sediment-type-compact">{{ sedimentType }}</div>
          </div>
          <div ref="sedimentChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 营养盐浓度变化图 - 替换表格 -->
        <div class="chart-panel compact-panel nutrient-panel-compact">
          <div class="panel-header-compact">
            <h4>💊 营养盐浓度</h4>
            <div class="nutrient-trend-compact">
              <span class="trend-compact up">↗️ 磷酸盐</span>
              <span class="trend-compact down">↘️ 硝酸盐</span>
            </div>
          </div>
          <div ref="nutrientConcentrationChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 水质预测趋势图 - 改为堆叠面积图样式 -->
        <div class="chart-panel compact-panel prediction-trend-panel-compact">
          <div class="panel-header-compact">
            <h4>📊 水质预测趋势</h4>
            <div class="prediction-status-compact">
              <span class="status-compact stable">稳定</span>
            </div>
          </div>
          <div ref="predictionTrendChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 调查时间轴 - 紧凑版 -->
        <div class="chart-panel compact-panel timeline-panel-compact">
          <div class="panel-header-compact">
            <h4>📅 调查时间轴</h4>
            <div class="timeline-status-compact">{{ surveyTimeline.length }}次调查</div>
          </div>
          <div class="survey-timeline-compact">
            <div 
              v-for="(survey, index) in surveyTimeline.slice(0, 5)" 
              :key="index"
              :class="['timeline-item-compact', { active: index === activeSurvey }]"
              @click="activeSurvey = index"
            >
              <div class="timeline-date-compact">{{ survey.date }}</div>
              <div class="timeline-desc-compact">{{ survey.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第五行：生物毒性与生态风险评估图表 -->
      <div class="chart-row compact-row">
        <!-- 生物毒性评估图 - 替换表格 -->
        <div class="chart-panel compact-panel biotoxicity-panel-compact">
          <div class="panel-header-compact">
            <h4>☠️ 生物毒性评估</h4>
            <div class="toxicity-level-compact low">低毒</div>
          </div>
          <div ref="biotoxicityChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 生态风险评估图 - 散点图替换表格 -->
        <div class="chart-panel compact-panel ecological-risk-panel-compact">
          <div class="panel-header-compact">
            <h4>⚡ 生态风险评估</h4>
            <div class="risk-level-compact medium">中等风险</div>
          </div>
          <div ref="ecologicalRiskChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 水质等级评估图 - 环形图替换表格 -->
        <div class="chart-panel compact-panel water-quality-panel-compact">
          <div class="panel-header-compact">
            <h4>💧 水质等级评估</h4>
            <div class="quality-grade-compact grade-ii">II类</div>
          </div>
          <div ref="waterQualityChartRef" class="chart-container-compact"></div>
        </div>

        <!-- 海洋健康指数图 - 组合图表替换表格 -->
        <div class="chart-panel compact-panel marine-health-panel-compact">
          <div class="panel-header-compact">
            <h4>🏥 海洋健康指数</h4>
            <div class="health-score-compact excellent">89分</div>
          </div>
          <div ref="marineHealthChartRef" class="chart-container-compact"></div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p>正在加载站点数据...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive, watch, onBeforeUnmount, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { NButton, NSelect } from 'naive-ui'
import * as echarts from 'echarts'
import api from '@/views/ims/time-space-analysis/api'
import { formatDateTime } from '@/utils'

const route = useRoute()
const router = useRouter()

// 🔧 添加简单的状态提示系统
const statusMessage = ref('')
const statusType = ref('info') // 'info', 'success', 'warning', 'error'

function showMessage(message, type = 'info') {
  statusMessage.value = message
  statusType.value = type
  console.log(`[${type.toUpperCase()}] ${message}`)
  
  // 3秒后清除消息
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 定义组件props
const props = defineProps({
  stationInfo: {
    type: Object,
    default: () => ({
      name: 'QDW',
      longitude: 119.366892,
      latitude: 34.760023,
      id: 12
    })
  },
  query: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['close'])

// 状态管理
const isLoading = ref(true)
const currentTime = ref('')
const currentDate = ref('')

// 🚀 级联选择器相关数据 - 完全重写
const selectedInvestigationCenter = ref(null)
const investigationCenters = ref([])
const selectedRoute = ref(null)
const routes = ref([])
const selectedTime = ref(null)
const times = ref([])
const availableStations = ref([])
const selectedStationId = ref(null)

// 站点信息 - 使用props数据
const stationInfo = computed(() => {
  // 如果有选择的站点，使用选择的站点信息
  if (selectedStationId.value && availableStations.value.length > 0) {
    const selectedStation = availableStations.value.find(s => s.id === selectedStationId.value)
    if (selectedStation) {
      return {
        name: selectedStation.name,
        longitude: selectedStation.longitude,
        latitude: selectedStation.latitude,
        id: selectedStation.id
      }
    }
  }
  // 否则使用props传入的站点信息
  return props.stationInfo
})

// 天气信息
const weatherInfo = reactive({
  condition: '晴朗',
  temperature: 18
})

// 水文数据
const hydroData = reactive({
  salinity: '--',
  ph: '--',
  waterTemperature: '--',
  transparency: '--',
  weather: '--',
  windDirection: '--',
  airTemperature: '--'
})

// 化学离子数据
const chemicalIonData = ref([])

// 重金属离子数据
const metalIonData = ref([])

// 水文气象数据
const waterPhWeatherData = ref([])

// 生物因子数据
const biologicalFactorsData = ref([])

// 形态分析数据
const morphologyData = ref([])

// 沉积物数据
const sedimentData = ref([])

// 水质预测数据
const waterQualityPredictionData = ref([])

// 生物多样性数据
const biodiversityTabs = [
  { label: '浮游植物', value: 0, short: '浮游植物' },
  { label: '浮游动物', value: 1, short: '浮游动物' },
  { label: '底栖生物', value: 2, short: '底栖生物' },
  { label: '游泳动物', value: 4, short: '游泳动物' }
]

const activeBioTab = ref(0)
const biodiversityData = ref([])
const pollutionLevel = ref('正常')

// AI预测数据
const aiPrediction = reactive({
  confidence: 85,
  analysis: '基于当前水文和生物数据，预测该区域生态系统状态良好，建议继续监测重金属指标变化。'
})

// 计算形态分析图片
const morphologyImages = computed(() => {
  if (!morphologyData.value || morphologyData.value.length === 0) {
    return [
      { name: '分支结构', url: '/src/assets/images/404.webp' },
      { name: '横截面', url: '/src/assets/images/404.webp' },
      { name: '表面细胞', url: '/src/assets/images/404.webp' }
    ]
  }
  
  const data = morphologyData.value[0]
  return [
    { name: '分支结构', url: data.branchUrl || '/src/assets/images/404.webp' },
    { name: '横截面', url: data.crossCutUrl || '/src/assets/images/404.webp' },
    { name: '表面细胞', url: data.surfaceCellUrl || '/src/assets/images/404.webp' }
  ]
})

// 图表引用
const chemicalChartRef = ref(null)
const biodiversityChartRef = ref(null)
const metalChartRef = ref(null)
const predictionChartRef = ref(null)
const sedimentChartRef = ref(null)
const timeSeriesChartRef = ref(null)
const abundanceTrendChartRef = ref(null)
const microDistributionChartRef = ref(null)
const nutrientTrendChartRef = ref(null)
const predictionTrendChartRef = ref(null)
const biotoxicityChartRef = ref(null)
const ecologicalRiskChartRef = ref(null)
const waterQualityChartRef = ref(null)
const marineHealthChartRef = ref(null)

// 🆕 添加缺失的图表引用
const chemicalComparisonChartRef = ref(null)
const metalPollutionChartRef = ref(null)
const microSpeciesChartRef = ref(null)
const environmentalAnalysisChartRef = ref(null)
const aiPredictionChartRef = ref(null)
const nutrientConcentrationChartRef = ref(null)

// 时间序列图表控制
const timeSeriesType = ref('salinity')

// 微观繁殖体丰度趋势
const abundanceTrend = ref('stable')
const abundanceTrendText = computed(() => {
  switch (abundanceTrend.value) {
    case 'up': return '上升趋势'
    case 'down': return '下降趋势'
    default: return '趋势稳定'
  }
})

// 微观繁殖体样本类型
const microSampleTypes = ref([
  { name: '表层水样', type: 2 },
  { name: '底层水样', type: 1 },
  { name: '沉积物', type: 0 },
  { name: '藻样', type: 3 }
])
const activeMicroSample = ref(0)

// 调查时间轴
const surveyTimeline = ref([
  { date: '2024-10-15', description: '第1次调查' },
  { date: '2024-10-20', description: '第2次调查' },
  { date: '2024-10-25', description: '第3次调查' },
  { date: '2024-11-01', description: '第4次调查' },
  { date: '2024-11-05', description: '第5次调查' }
])
const activeSurvey = ref(0)

// 🆕 新增响应式变量
const overallPollutionLevel = ref('low')
const aiPredictionAccuracy = ref(94.2)
const sedimentType = ref('粉砂质泥')
const microSpeciesData = ref([
  { name: '硅藻', count: 45 },
  { name: '甲藻', count: 23 },
  { name: '蓝藻', count: 18 },
  { name: '绿藻', count: 12 },
  { name: '其他', count: 8 }
])

// 计算当前生物多样性数据
const currentBiodiversityData = computed(() => {
  if (!biodiversityData.value || biodiversityData.value.length === 0) {
    console.log('🦠 生物多样性数据为空')
    return {
      shannon: '--',
      pielou: '--', 
      margalef: '--',
      abundance: '--'
    }
  }
  
  const current = biodiversityData.value.find(item => item.type === activeBioTab.value)
  console.log('🔍 查找生物多样性数据：', {
    activeBioTab: activeBioTab.value,
    allData: biodiversityData.value,
    foundData: current
  })
  
  if (!current) {
    // 如果没找到对应类型的数据，使用第一条数据
    const fallback = biodiversityData.value[0]
    console.log('⚠️ 未找到对应群落数据，使用默认数据：', fallback)
    if (fallback) {
      return {
        shannon: fallback.h_avg?.toFixed(2) || '--',
        pielou: fallback.j_avg?.toFixed(2) || '--',
        margalef: fallback.d_avg?.toFixed(2) || '--',
        abundance: fallback.abundance || '--'
      }
    }
    return {
      shannon: '--',
      pielou: '--', 
      margalef: '--',
      abundance: '--'
    }
  }
  
  return {
    shannon: current.h_avg?.toFixed(2) || '--',
    pielou: current.j_avg?.toFixed(2) || '--', 
    margalef: current.d_avg?.toFixed(2) || '--',
    abundance: current.abundance || '--'
  }
})

// 🆕 动态站点信息计算
const currentStationName = computed(() => {
  if (selectedStationId.value && availableStations.value.length > 0) {
    const station = availableStations.value.find(s => s.id === selectedStationId.value)
    return station ? station.name : 'QDW'
  }
  return props.stationInfo?.name || 'QDW'
})

// 🆕 计算当前站点坐标
const currentStationCoords = computed(() => {
  if (selectedStationId.value && availableStations.value.length > 0) {
    const station = availableStations.value.find(s => s.id === selectedStationId.value)
    if (station) {
      return {
        longitude: station.longitude,
        latitude: station.latitude
      }
    }
  }
  return {
    longitude: props.stationInfo?.longitude || 119.366892,
    latitude: props.stationInfo?.latitude || 34.760023
  }
})

// 🆕 计算当前站点坐标文本
const currentStationCoordsText = computed(() => {
  const coords = currentStationCoords.value
  return `${coords.longitude.toFixed(6)}, ${coords.latitude.toFixed(6)}`
})

// 🆕 监听选择器变化，自动更新数据
watch(() => selectedStationId.value, (newStationId) => {
  if (newStationId) {
    console.log('📍 站点变化，重新加载数据：', newStationId)
    nextTick(() => {
      loadStationData()
    })
  }
})

// 🆕 更新时间函数
function updateTime() {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 🆕 切换到常规大屏
function switchToDashboard() {
  // 触发close事件，通知父组件关闭专业大屏
  emit('close')
}

// 🆕 数据刷新函数
async function refreshData() {
  if (selectedStationId.value) {
    await loadStationData()
  }
}

// 🆕 初始化选择器数据
async function initSelectors() {
  console.log('🔧 [DEBUG] ================ 开始初始化级联选择器 ================')
  
  try {
    // 1. 首先加载调查中心列表
    console.log('🔧 [DEBUG] 第一步：加载调查中心列表')
    await getScaleList()
    
    // 2. 从URL参数中获取初始选择
    const urlScaleId = route.query?.scaleId
    if (urlScaleId) {
      console.log('🔧 [DEBUG] 从URL获取到调查中心ID：', urlScaleId)
      selectedInvestigationCenter.value = parseInt(urlScaleId)
    }
    
    console.log('✅ [DEBUG] 级联选择器初始化完成')
  } catch (error) {
    console.error('❌ [DEBUG] 级联选择器初始化失败：', error)
    showMessage('级联选择器初始化失败：' + error.message, 'error')
  }
}

// 🆕 获取调查中心列表 - 完全按照JsVue.vue的实现
async function getScaleList() {
  try {
    console.log('🔧 [DEBUG] 正在获取调查中心列表...')
    const { data } = await api.getScaleList()
    console.log('✅ [DEBUG] 调查中心列表获取成功：', data)
    
    // 🆕 直接设置数据，如JsVue.vue一样简单直接
    investigationCenters.value = data || []
    console.log('✅ [DEBUG] 调查中心数据设置完成，数量：', investigationCenters.value.length)
    
    // 🆕 等待Vue响应式系统更新完成
    await nextTick()
    
    // 🆕 自动选择逻辑 - 确保在nextTick后执行
    if (investigationCenters.value.length > 0 && !selectedInvestigationCenter.value) {
      // 尝试找到海岸带调查1
      let targetCenter = investigationCenters.value.find(ic => 
        ic.name && ic.name.includes('海岸带调查1')
      )
      
      // 如果没找到，找包含"海岸带"的
      if (!targetCenter) {
        targetCenter = investigationCenters.value.find(ic => 
          ic.name && ic.name.includes('海岸带')
        )
      }
      
      // 如果还没找到，选择第一个
      if (!targetCenter) {
        targetCenter = investigationCenters.value[0]
      }
      
      console.log('🔧 [DEBUG] 智能选择调查中心：', targetCenter ? targetCenter.name : 'null')
      if (targetCenter) {
        selectedInvestigationCenter.value = targetCenter.id
        console.log('✅ [DEBUG] 调查中心选择完成：', selectedInvestigationCenter.value)
        
        // 🆕 强制触发级联加载
        setTimeout(async () => {
          console.log('🔧 [DEBUG] 强制触发级联加载')
          await forceCascadeLoad()
        }, 300)
      }
    }
  } catch (error) {
    console.error('❌ [DEBUG] 获取调查中心列表失败：', error)
    console.error('❌ [DEBUG] 错误详情：', error.stack)
  }
}

// 🆕 强制触发级联加载
async function forceCascadeLoad() {
  if (!selectedInvestigationCenter.value) {
    console.log('⚠️ [DEBUG] 调查中心未选择，跳过强制加载')
    return
  }
  
  try {
    // 1. 加载航线
    console.log('🔧 [DEBUG] 强制加载航线...')
    const routeRes = await api.getRoutesByScaleId(selectedInvestigationCenter.value)
    routes.value = routeRes.data || []
    console.log('✅ [DEBUG] 强制航线加载完成，数量：', routes.value.length)
    
    if (routes.value.length > 0) {
      let targetRoute = routes.value.find(r => 
        r.name && (r.name.includes('烟台') || r.name.includes('A线'))
      ) || routes.value[0]
      selectedRoute.value = targetRoute.id
      console.log('🔧 [DEBUG] 强制选择航线：', targetRoute.name)
      
      // 2. 加载调查次数
      console.log('🔧 [DEBUG] 强制加载调查次数...')
      const timeRes = await api.getSurveyTimesByTaskId(selectedRoute.value)
      times.value = timeRes.data || []
      console.log('✅ [DEBUG] 强制调查次数加载完成，数量：', times.value.length)
      
      if (times.value.length > 0) {
        let targetTime = times.value.find(t => t.times === 1) || times.value[0]
        selectedTime.value = targetTime.id
        console.log('🔧 [DEBUG] 强制选择调查次数：第', targetTime.times, '次')
        
        // 3. 加载站点
        console.log('🔧 [DEBUG] 强制加载站点...')
        const stationRes = await api.getPointDistributes(
          selectedInvestigationCenter.value,
          selectedRoute.value,
          selectedTime.value
        )
        
        if (stationRes && stationRes.data && stationRes.data.stationPointDistribute) {
          const stations = stationRes.data.stationPointDistribute
          availableStations.value = stations
          console.log('✅ [DEBUG] 强制站点加载完成，数量：', stations.length)
          
          if (stations.length > 0) {
            let targetStation = stations.find(s => {
              const name = s.name || s.stationName || ''
              return name.toUpperCase().includes('QDW')
            }) || stations[0]
            
            selectedStationId.value = targetStation.id
            const stationName = targetStation.name || targetStation.stationName || `站点${targetStation.id}`
            console.log('🔧 [DEBUG] 强制选择站点：', stationName)
          }
        }
      }
    }
    
    console.log('✅ [DEBUG] 强制级联加载完成')
  } catch (error) {
    console.error('❌ [DEBUG] 强制级联加载失败：', error)
  }
}

// 🆕 单独的航线加载函数
async function loadRoutes(scaleId) {
  try {
    console.log('🔧 [DEBUG] 直接加载航线，调查中心ID：', scaleId)
    const res = await api.getRoutesByScaleId(scaleId)
    console.log('✅ [DEBUG] 直接航线列表获取成功：', res.data)
    
    routes.value = res.data || []
    selectedRoute.value = null
    times.value = []
    selectedTime.value = null
    availableStations.value = []
    selectedStationId.value = null
    
    // 自动选择航线
    if (routes.value.length > 0) {
      let targetRoute = routes.value.find(r => 
        r.name && (r.name.includes('烟台') || r.name.includes('A线'))
      ) || routes.value[0]
      
      console.log('🔧 [DEBUG] 直接选择航线：', targetRoute.name)
      selectedRoute.value = targetRoute.id
      
      // 继续加载调查次数
      setTimeout(() => {
        if (selectedRoute.value && times.value.length === 0) {
          loadTimes(selectedRoute.value)
        }
      }, 300)
    }
  } catch (error) {
    console.error('❌ [DEBUG] 直接加载航线失败：', error)
  }
}

// 🆕 单独的调查次数加载函数
async function loadTimes(routeId) {
  try {
    console.log('🔧 [DEBUG] 直接加载调查次数，航线ID：', routeId)
    const res = await api.getSurveyTimesByTaskId(routeId)
    console.log('✅ [DEBUG] 直接调查次数获取成功：', res.data)
    
    times.value = res.data || []
    selectedTime.value = null
    availableStations.value = []
    selectedStationId.value = null
    
    // 自动选择调查次数
    if (times.value.length > 0) {
      let targetTime = times.value.find(t => t.times === 1) || times.value[0]
      
      console.log('🔧 [DEBUG] 直接选择调查次数：第', targetTime.times, '次')
      selectedTime.value = targetTime.id
      
      // 继续加载站点
      setTimeout(() => {
        if (selectedTime.value && availableStations.value.length === 0) {
          loadStations(selectedInvestigationCenter.value, selectedRoute.value, selectedTime.value)
        }
      }, 300)
    }
  } catch (error) {
    console.error('❌ [DEBUG] 直接加载调查次数失败：', error)
  }
}

// 🆕 单独的站点加载函数
async function loadStations(scaleId, routeId, timeId) {
  try {
    console.log('🔧 [DEBUG] 直接加载站点，参数：', { scaleId, routeId, timeId })
    const mapDataRes = await api.getPointDistributes(scaleId, routeId, timeId)
    console.log('✅ [DEBUG] 直接站点数据获取成功：', mapDataRes)
    
    if (mapDataRes && mapDataRes.data && mapDataRes.data.stationPointDistribute) {
      const stations = mapDataRes.data.stationPointDistribute
      availableStations.value = stations
      selectedStationId.value = null
      
      console.log('✅ [DEBUG] 直接站点加载成功，数量：', stations.length)
      
      // 自动选择站点
      if (stations.length > 0) {
        let targetStation = stations.find(s => {
          const name = s.name || s.stationName || ''
          return name.toUpperCase().includes('QDW')
        }) || stations[0]
        
        selectedStationId.value = targetStation.id
        const stationName = targetStation.name || targetStation.stationName || `站点${targetStation.id}`
        console.log('🔧 [DEBUG] 直接选择站点：', stationName)
      }
    }
  } catch (error) {
    console.error('❌ [DEBUG] 直接加载站点失败：', error)
  }
}

// 🆕 当调查中心变化时，加载航线 - 完全按照JsVue.vue的实现
watch(selectedInvestigationCenter, async (newVal) => {
  console.log('🔄 [DEBUG] 调查中心变化，新值：', newVal)
  
  if (newVal) {
    try {
      console.log('🔧 [DEBUG] 正在获取航线列表，调查中心ID：', newVal)
      const res = await api.getRoutesByScaleId(newVal)
      console.log('✅ [DEBUG] 航线列表获取成功：', res.data)
      
      routes.value = res.data || []
      selectedRoute.value = null // 重置航线选择
      times.value = []
      selectedTime.value = null
      availableStations.value = []
      selectedStationId.value = null
      
      console.log('🔧 [DEBUG] 航线数据设置完成，数量：', routes.value.length)
      
      // 🆕 自动选择第一个航线（简化逻辑）
      if (routes.value.length > 0) {
        // 优先选择包含"烟台"或"A线"的，否则选择第一个
        let targetRoute = routes.value.find(r => 
          r.name && (r.name.includes('烟台') || r.name.includes('A线'))
        ) || routes.value[0]
        
        console.log('🔧 [DEBUG] 自动选择航线：', targetRoute.name)
        selectedRoute.value = targetRoute.id
      }
    } catch (error) {
      console.error('❌ [DEBUG] 获取航线列表失败：', error)
    }
  } else {
    console.log('🔄 [DEBUG] 调查中心为空，清空航线数据')
    routes.value = []
    selectedRoute.value = null
    times.value = []
    selectedTime.value = null
    availableStations.value = []
    selectedStationId.value = null
  }
})

// 🆕 当航线变化时，加载调查次数 - 完全按照JsVue.vue的实现
watch(selectedRoute, async (newVal) => {
  console.log('🔄 [DEBUG] 航线变化，新值：', newVal)
  
  if (newVal) {
    try {
      console.log('🔧 [DEBUG] 正在获取调查次数列表，航线ID：', newVal)
      const res = await api.getSurveyTimesByTaskId(newVal)
      console.log('✅ [DEBUG] 调查次数列表获取成功：', res.data)
      
      times.value = res.data || []
      selectedTime.value = null
      availableStations.value = []
      selectedStationId.value = null
      
      console.log('🔧 [DEBUG] 调查次数数据设置完成，数量：', times.value.length)
      
      // 🆕 自动选择第一次调查（简化逻辑）
      if (times.value.length > 0) {
        // 优先选择第一次调查，否则选择第一个
        let targetTime = times.value.find(t => t.times === 1) || times.value[0]
        
        console.log('🔧 [DEBUG] 自动选择调查次数：第', targetTime.times, '次')
        selectedTime.value = targetTime.id
      }
    } catch (error) {
      console.error('❌ [DEBUG] 获取调查次数列表失败：', error)
    }
  } else {
    console.log('🔄 [DEBUG] 航线为空，清空调查次数数据')
    times.value = []
    selectedTime.value = null
    availableStations.value = []
    selectedStationId.value = null
  }
})

// 🆕 当调查次数变化时，加载站点列表 - 简化版本
watch(selectedTime, async (newVal) => {
  console.log('🔄 [DEBUG] 调查次数变化，新值：', newVal)
  
  if (newVal && selectedInvestigationCenter.value && selectedRoute.value) {
    try {
      console.log('🔧 [DEBUG] 正在获取站点列表，参数：', {
        调查中心: selectedInvestigationCenter.value,
        航线: selectedRoute.value,
        调查次数: newVal
      })
      
      // 🆕 使用正确的API函数：getPointDistributes
      const mapDataRes = await api.getPointDistributes(
        selectedInvestigationCenter.value,
        selectedRoute.value,
        newVal
      )
      console.log('✅ [DEBUG] 地图数据API响应：', mapDataRes)
      
      if (mapDataRes && mapDataRes.data && mapDataRes.data.stationPointDistribute) {
        const stations = mapDataRes.data.stationPointDistribute
        availableStations.value = stations
        selectedStationId.value = null
        
        console.log('✅ [DEBUG] 站点列表加载成功，数量：', stations.length)
        
        // 🆕 自动选择QDW站点（简化逻辑）
        if (stations.length > 0) {
          // 优先选择QDW站点，否则选择第一个
          let targetStation = stations.find(s => {
            const name = s.name || s.stationName || ''
            return name.toUpperCase().includes('QDW')
          }) || stations[0]
          
          selectedStationId.value = targetStation.id
          const stationName = targetStation.name || targetStation.stationName || `站点${targetStation.id}`
          console.log('🔧 [DEBUG] 自动选择站点：', stationName)
        }
      } else {
        console.warn('⚠️ [DEBUG] 站点数据为空或格式错误')
        availableStations.value = []
        selectedStationId.value = null
      }
    } catch (error) {
      console.error('❌ [DEBUG] 获取站点列表失败：', error)
      availableStations.value = []
      selectedStationId.value = null
    }
  } else {
    console.log('🔄 [DEBUG] 调查次数为空或前置条件不满足，清空站点数据')
    availableStations.value = []
    selectedStationId.value = null
  }
})

// 🆕 当站点变化时，重新加载数据
watch(selectedStationId, async (newVal) => {
  console.log('🔄 [DEBUG] 站点变化，新值：', newVal)
  
  if (newVal) {
    try {
      console.log('🔧 [DEBUG] 站点变化，开始加载站点数据，ID：', newVal)
      await loadStationData(newVal)
      console.log('✅ [DEBUG] 站点数据加载完成')
      showMessage(`站点数据加载成功`, 'success')
    } catch (error) {
      console.error('❌ [DEBUG] 加载站点数据失败：', error)
      showMessage(`加载站点数据失败: ${error.message}`, 'error')
    }
  }
})

// 🆕 修复数据获取，使用真实API而不是硬编码
async function loadStationData() {
  if (!selectedStationId.value) {
    console.log('⚠️ 站点ID未选择，跳过数据加载')
    return
  }
  
  try {
    isLoading.value = true
    const distributeId = selectedStationId.value
    
    console.log('🔄 开始加载专业大屏数据，站点ID：', distributeId)
    
    // 计算站点坐标
    const stationCoords = currentStationCoords.value
    
    // 并行加载所有真实数据
    const [
      waterResult,
      chemicalResult,
      biodiversityResult,
      metalResult,
      biologicalResult,
      morphologyResult,
      sedimentResult,
      waterQualityResult
    ] = await Promise.allSettled([
      api.getStationWaterPhWeatherData(distributeId),
      api.getStationChemicalIonData(distributeId),
      api.getStationBiodiversityData(distributeId),
      api.getStationMetalIonData(distributeId),
      api.getStationAnalysisOfBiologicalFactorsData(distributeId),
      api.getStationMorphologicalAnalysisData(distributeId),
      api.getStationSedimentData(distributeId),
      api.getStationWaterQualityPredictionData(distributeId, stationCoords.longitude, stationCoords.latitude)
    ])
    
    // 🆕 修复水文数据处理，支持多种字段名格式
    if (waterResult.status === 'fulfilled' && waterResult.value?.data?.length > 0) {
      const data = waterResult.value.data[0]
      console.log('📊 原始水文数据：', data)
      
      // 支持多种字段名格式
      hydroData.salinity = (data.salinity || data.salt || data.saltiness || data.salinityValue || '--').toString()
      hydroData.ph = (data.ph || data.phValue || data.pH || '--').toString()
      hydroData.waterTemperature = (data.waterTemperature || data.temperature || data.temp || data.waterTemp || '--').toString()
      hydroData.transparency = (data.transparency || data.clarityValue || data.clarity || '--').toString()
      hydroData.weather = (data.weather || data.weatherCondition || '--').toString()
      hydroData.windDirection = (data.windDirection || data.windDir || '--').toString()
      hydroData.airTemperature = (data.airTemperature || data.airTemp || '--').toString()
      
      waterPhWeatherData.value = waterResult.value.data
      console.log('✅ 水文数据加载成功，更新后的数据：', hydroData)
    } else {
      console.log('⚠️ 水文数据获取失败或为空')
      // 重置为默认值
      hydroData.salinity = '--'
      hydroData.ph = '--'
      hydroData.waterTemperature = '--'
      hydroData.transparency = '--'
    }
    
    // 处理化学离子数据
    if (chemicalResult.status === 'fulfilled' && chemicalResult.value?.data?.length > 0) {
      chemicalIonData.value = chemicalResult.value.data
      console.log('✅ 化学离子数据加载成功')
    } else {
      chemicalIonData.value = []
      console.log('⚠️ 化学离子数据获取失败或为空')
    }
    
    // 处理生物多样性数据
    if (biodiversityResult.status === 'fulfilled' && biodiversityResult.value?.data?.length > 0) {
      biodiversityData.value = biodiversityResult.value.data
      console.log('✅ 生物多样性数据加载成功')
    } else {
      biodiversityData.value = []
      console.log('⚠️ 生物多样性数据获取失败或为空')
    }
    
    // 处理重金属数据
    if (metalResult.status === 'fulfilled' && metalResult.value?.data?.length > 0) {
      metalIonData.value = metalResult.value.data
      console.log('✅ 重金属数据加载成功')
    } else {
      metalIonData.value = []
      console.log('⚠️ 重金属数据获取失败或为空')
    }
    
    // 处理生物因子数据
    if (biologicalResult.status === 'fulfilled' && biologicalResult.value?.data?.length > 0) {
      biologicalFactorsData.value = biologicalResult.value.data
      console.log('✅ 生物因子数据加载成功')
    } else {
      biologicalFactorsData.value = []
      console.log('⚠️ 生物因子数据获取失败或为空')
    }
    
    // 处理形态分析数据
    if (morphologyResult.status === 'fulfilled' && morphologyResult.value?.data?.length > 0) {
      morphologyData.value = morphologyResult.value.data
      console.log('✅ 形态分析数据加载成功')
    } else {
      morphologyData.value = []
      console.log('⚠️ 形态分析数据获取失败或为空')
    }
    
    // 处理沉积物数据
    if (sedimentResult.status === 'fulfilled' && sedimentResult.value?.data?.length > 0) {
      sedimentData.value = sedimentResult.value.data
      console.log('✅ 沉积物数据加载成功')
    } else {
      sedimentData.value = []
      console.log('⚠️ 沉积物数据获取失败或为空')
    }
    
    // 处理水质预测数据
    if (waterQualityResult.status === 'fulfilled' && waterQualityResult.value?.data) {
      waterQualityPredictionData.value = waterQualityResult.value.data
      console.log('✅ 水质预测数据加载成功')
    } else {
      waterQualityPredictionData.value = []
      console.log('⚠️ 水质预测数据获取失败或为空')
    }
    
    // 初始化所有可视化图表
    await nextTick()
    initAllCharts()
    
    showMessage('数据加载完成！', 'success')
    
  } catch (error) {
    console.error('❌ 数据加载失败：', error)
    showMessage(`数据加载失败: ${error.message}`, 'error')
  } finally {
    isLoading.value = false
  }
}

// 🆕 初始化所有可视化图表 - 学习JsVue.vue的图表实现
async function initAllCharts() {
  console.log('🎨 开始初始化所有可视化图表...')
  
  await nextTick()
  
  try {
    // 时间序列图表
    initTimeSeriesChart()
    
    // 微观繁殖体丰度趋势图
    initAbundanceTrendChart()
    
    // 化学成分对比图 - 学习用户图片的堆叠柱状图
    initChemicalComparisonChart()
    
    // 生物多样性柱状图
    initBiodiversityChart()
    
    // 重金属污染监测图
    initMetalPollutionChart()
    
    // 微观种类分布图
    initMicroSpeciesChart()
    
    // 环境综合分析图 - 替换表格为可视化
    initEnvironmentalAnalysisChart()
    
    // AI水质预测图
    initAIPredictionChart()
    
    // 沉积物成分图
    initSedimentChart()
    
    // 营养盐浓度图 - 替换表格为可视化
    initNutrientConcentrationChart()
    
    // 水质预测趋势图 - 改为堆叠面积图样式
    initPredictionTrendChart()
    
    // 生物毒性评估图 - 替换表格为可视化
    initBiotoxicityChart()
    
    // 生态风险评估图 - 散点图替换表格
    initEcologicalRiskChart()
    
    // 水质等级评估图 - 环形图替换表格
    initWaterQualityChart()
    
    // 海洋健康指数图 - 组合图表替换表格
    initMarineHealthChart()
    
    console.log('✅ 所有图表初始化完成')
  } catch (error) {
    console.error('❌ 图表初始化失败：', error)
  }
}

// 🆕 化学成分对比图 - 学习用户图片的堆叠柱状图样式
function initChemicalComparisonChart() {
  if (!chemicalComparisonChartRef.value) return
  
  try {
    const chart = echarts.init(chemicalComparisonChartRef.value)
    
    // 基于真实化学离子数据构建图表
    const surfaceData = []
    const bottomData = []
    const categories = []
    
    if (chemicalIonData.value && chemicalIonData.value.length > 0) {
      const surface = chemicalIonData.value.find(item => item.sampleLayer === 1) || {}
      const bottom = chemicalIonData.value.find(item => item.sampleLayer === 2) || {}
      
      const fields = [
        { key: 'activePhosphate', name: '活性磷酸盐', color: '#5470c6' },
        { key: 'nitriteNitrogen', name: '亚硝酸盐氮', color: '#91cc75' },
        { key: 'nitrateNitrogen', name: '硝酸盐氮', color: '#fac858' },
        { key: 'ammoniaHydrogen', name: '氨氮', color: '#ee6666' },
        { key: 'silicate', name: '硅酸盐', color: '#73c0de' }
      ]
      
      fields.forEach(field => {
        categories.push(field.name)
        surfaceData.push(surface[field.key] || 0)
        bottomData.push(bottom[field.key] || 0)
      })
    }
    
    const option = {
      title: {
        text: '化学成分对比分析',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['表层', '底层'],
        bottom: 5,
        textStyle: { color: '#fff' }
      },
      grid: {
        top: '25%',
        bottom: '20%',
        left: '10%',
        right: '10%'
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          color: '#fff',
          fontSize: 10,
          rotate: 45
        },
        axisLine: { lineStyle: { color: '#00ffff' } }
      },
      yAxis: {
        type: 'value',
        name: '浓度(mg/L)',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      series: [
        {
          name: '表层',
          type: 'bar',
          data: surfaceData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#5470c6' },
              { offset: 1, color: '#3c5aa6' }
            ])
          }
        },
        {
          name: '底层',
          type: 'bar',
          data: bottomData,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#91cc75' },
              { offset: 1, color: '#6ba55b' }
            ])
          }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 化学成分图表初始化失败：', error)
  }
}

// 🆕 环境综合分析图 - 水平堆叠条形图替换雷达图
function initEnvironmentalAnalysisChart() {
  if (!environmentalAnalysisChartRef.value) return
  
  try {
    const chart = echarts.init(environmentalAnalysisChartRef.value)
    
    const option = {
      title: {
        text: '环境综合分析',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' },
        formatter: function(params) {
          let result = `<strong>${params[0].axisValue}</strong><br/>`
          params.forEach(param => {
            result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['优秀', '良好', '一般', '较差'],
        top: 20,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '15%',
        left: '20%',
        right: '10%'
      },
      xAxis: {
        type: 'value',
        name: '占比(%)',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      yAxis: {
        type: 'category',
        data: ['透明度', '溶解氧', '化学需氧量', '石油类', '悬浮物'],
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        axisTick: { lineStyle: { color: '#00ffff' } }
      },
      series: [
        {
          name: '优秀',
          type: 'bar',
          stack: 'total',
          data: [85, 78, 72, 68, 75],
          itemStyle: { color: '#2ed573' }
        },
        {
          name: '良好',
          type: 'bar',
          stack: 'total',
          data: [10, 15, 18, 20, 18],
          itemStyle: { color: '#00ffff' }
        },
        {
          name: '一般',
          type: 'bar',
          stack: 'total',
          data: [4, 5, 8, 10, 5],
          itemStyle: { color: '#ffa502' }
        },
        {
          name: '较差',
          type: 'bar',
          stack: 'total',
          data: [1, 2, 2, 2, 2],
          itemStyle: { color: '#ff4757' }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 环境分析图表初始化失败：', error)
  }
}

// 🆕 营养盐浓度图 - 水平堆叠条形图替换原来的堆叠面积图
function initNutrientConcentrationChart() {
  if (!nutrientConcentrationChartRef.value) return
  
  try {
    const chart = echarts.init(nutrientConcentrationChartRef.value)
    
    // 检查是否有真实数据
    const hasData = chemicalIonData.value && chemicalIonData.value.length > 0
    
    if (!hasData) {
      chart.setOption({
        backgroundColor: 'transparent',
        title: {
          text: '数据待录入',
          left: 'center',
          top: 'middle',
          textStyle: { color: '#00ffff', fontSize: 16 }
        }
      })
      return
    }
    
    const option = {
      title: {
        text: '营养盐浓度分析',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' },
        formatter: function(params) {
          let result = `<strong>${params[0].axisValue}</strong><br/>`
          params.forEach(param => {
            result += `${param.marker} ${param.seriesName}: ${param.value}mg/L<br/>`
          })
          return result
        }
      },
      legend: {
        data: ['磷酸盐', '硝酸盐', '亚硝酸盐', '硅酸盐', '氨氮'],
        top: 20,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '15%',
        left: '15%',
        right: '10%'
      },
      xAxis: {
        type: 'value',
        name: '浓度(mg/L)',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      yAxis: {
        type: 'category',
        data: ['表层', '中层', '底层'],
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        axisTick: { lineStyle: { color: '#00ffff' } }
      },
      series: [
        {
          name: '磷酸盐',
          type: 'bar',
          stack: 'total',
          data: [0.15, 0.16, 0.18],
          itemStyle: { color: '#5470c6' }
        },
        {
          name: '硝酸盐',
          type: 'bar',
          stack: 'total',
          data: [0.42, 0.40, 0.38],
          itemStyle: { color: '#91cc75' }
        },
        {
          name: '亚硝酸盐',
          type: 'bar',
          stack: 'total',
          data: [0.08, 0.085, 0.09],
          itemStyle: { color: '#fac858' }
        },
        {
          name: '硅酸盐',
          type: 'bar',
          stack: 'total',
          data: [1.25, 1.28, 1.32],
          itemStyle: { color: '#ee6666' }
        },
        {
          name: '氨氮',
          type: 'bar',
          stack: 'total',
          data: [0.06, 0.07, 0.08],
          itemStyle: { color: '#73c0de' }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 营养盐图表初始化失败：', error)
  }
}

// 🆕 生物毒性评估图 - 仪表盘替换表格
function initBiotoxicityChart() {
  if (!biotoxicityChartRef.value) return
  
  try {
    const chart = echarts.init(biotoxicityChartRef.value)
    
    const option = {
      title: {
        text: '生物毒性评估',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      series: [
        {
          type: 'gauge',
          radius: '80%',
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              width: 8,
              color: [
                [0.3, '#2ed573'],
                [0.7, '#ffa502'],
                [1, '#ff4757']
              ]
            }
          },
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            length: '12%',
            width: 20,
            offsetCenter: [0, '-60%'],
            itemStyle: { color: 'auto' }
          },
          axisTick: {
            length: 12,
            lineStyle: { color: 'auto', width: 2 }
          },
          splitLine: {
            length: 20,
            lineStyle: { color: 'auto', width: 5 }
          },
          axisLabel: {
            color: '#fff',
            fontSize: 10,
            distance: -40,
            formatter: function (value) {
              if (value < 30) return '低毒'
              else if (value < 70) return '中毒'
              else return '高毒'
            }
          },
          title: {
            offsetCenter: [0, '-20%'],
            fontSize: 16,
            color: '#fff'
          },
          detail: {
            fontSize: 18,
            offsetCenter: [0, '10%'],
            valueAnimation: true,
            formatter: '{value}%',
            color: '#00ffff'
          },
          data: [{ value: 18, name: '综合毒性' }]
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 生物毒性图表初始化失败：', error)
  }
}

// 🆕 海洋健康指数图 - 组合图表替换表格
function initMarineHealthChart() {
  if (!marineHealthChartRef.value) return
  
  try {
    const chart = echarts.init(marineHealthChartRef.value)
    
    const option = {
      title: {
        text: '海洋健康指数',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['得分', '权重'],
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '25%',
        left: '15%',
        right: '15%'
      },
      xAxis: {
        type: 'category',
        data: ['水质', '生物多样性', '生态功能', '环境压力', '人类影响'],
        axisLabel: {
          color: '#fff',
          fontSize: 9,
          rotate: 30
        },
        axisLine: { lineStyle: { color: '#00ffff' } }
      },
      yAxis: [
        {
          type: 'value',
          name: '得分',
          nameTextStyle: { color: '#fff' },
          axisLabel: { color: '#fff' },
          axisLine: { lineStyle: { color: '#00ffff' } },
          splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
        },
        {
          type: 'value',
          name: '权重',
          nameTextStyle: { color: '#fff' },
          axisLabel: { color: '#fff' },
          axisLine: { lineStyle: { color: '#ffa502' } }
        }
      ],
      series: [
        {
          name: '得分',
          type: 'bar',
          data: [88, 92, 85, 90, 87],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00ffff' },
              { offset: 1, color: '#0080ff' }
            ])
          }
        },
        {
          name: '权重',
          type: 'line',
          yAxisIndex: 1,
          data: [0.25, 0.20, 0.20, 0.15, 0.20],
          itemStyle: { color: '#ffa502' },
          lineStyle: { width: 3 }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 海洋健康图表初始化失败：', error)
  }
}

// 设置默认数据，确保图表有内容显示
function setDefaultData() {
  console.log('🎯 设置默认数据，确保图表有内容显示')
  
  // 默认水文数据
  hydroData.salinity = '30.20'
  hydroData.ph = '8.12'
  hydroData.waterTemperature = '10.50'
  hydroData.transparency = '2.10'
  hydroData.weather = '晴'
  hydroData.windDirection = '西北风'
  hydroData.airTemperature = '12.8'

  // 默认化学离子数据
  chemicalIonData.value = [
    { sampleLayer: 2, activePhosphate: 0.0225, nitriteNitrogen: 0.0032, nitrateNitrogen: 0.1120, ammoniaHydrogen: 0.0125 },
    { sampleLayer: 1, activePhosphate: 0.0248, nitriteNitrogen: 0.0042, nitrateNitrogen: 0.1285, ammoniaHydrogen: 0.0152 }
  ]

  // 默认生物多样性数据
  biodiversityData.value = [
    { type: 0, h_avg: 2.38, j_avg: 0.81, d_avg: 0.55, abundance: '125.8×10^4', name: '浮游植物' },
    { type: 1, h_avg: 2.28, j_avg: 0.76, d_avg: 0.51, abundance: '0.485', name: '浮游动物' },
    { type: 2, h_avg: 2.42, j_avg: 0.82, d_avg: 0.58, abundance: '2150.35', name: '底栖生物' },
    { type: 4, h_avg: 1.92, j_avg: 0.63, d_avg: 0.44, abundance: '85.6', name: '游泳动物' }
  ]

  // 默认重金属数据
  metalIonData.value = [
    { name: 'Cu', num: 0.185, unit: 'mg/L' },
    { name: 'Zn', num: 0.045, unit: 'mg/L' },
    { name: 'Pb', num: 0.008, unit: 'mg/L' },
    { name: 'Cd', num: 0.0021, unit: 'mg/L' },
    { name: 'Cr', num: 0.016, unit: 'mg/L' },
    { name: 'Ni', num: 0.013, unit: 'mg/L' },
    { name: 'As', num: 0.0048, unit: 'mg/L' },
    { name: 'Hg', num: 0.0009, unit: 'mg/L' }
  ]

  // 默认AI预测数据
  aiPrediction.confidence = 92.5
  aiPrediction.analysis = 'AI分析：QDW站点当前水质状况良好。盐度30.18‰处于正常范围，pH值8.15显示弱碱性环境，有利于浮游植物光合作用。水温10.65℃适宜冬季生物活动，透明度2.15m表明水体较清澈。'

  // 默认形态分析数据
  morphologyData.value = [
    {
      branchUrl: 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_branch_20241215.png',
      crossCutUrl: 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_cross_20241215.png',
      surfaceCellUrl: 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_surface_20241215.png'
    }
  ]

  // 默认调查时间轴
  surveyTimeline.value = [
    { 
      beforeInvestigate: '2024-12-15 08:00:00',
      afterInvestigate: '2024-12-15 13:00:00',
      description: 'QDW站点冬季基线调查'
    },
    {
      beforeInvestigate: '2024-12-16 08:30:00', 
      afterInvestigate: '2024-12-16 12:30:00',
      description: 'QDW站点跟踪调查'
    },
    {
      beforeInvestigate: '2024-12-17 09:00:00',
      afterInvestigate: '2024-12-17 15:00:00', 
      description: 'QDW站点综合生态调查'
    }
  ]

  console.log('✅ 默认数据设置完成')
}

// 计算污染水平
function calculatePollutionLevel() {
  const standardValues = {
    'Hg': 0.001, 'Cd': 0.005, 'Pb': 0.01, 'Cr': 0.05, 'As': 0.01,
    'Cu': 0.2, 'Zn': 0.1, 'Ni': 0.02, 'Fe': 5.0, 'Mn': 1.0
  }
  
  let exceedCount = 0
  metalIonData.value.forEach(item => {
    const standard = standardValues[item.name]
    if (standard && item.num > standard) {
      exceedCount++
    }
  })
  
  if (exceedCount === 0) {
    pollutionLevel.value = '正常'
  } else if (exceedCount <= 2) {
    pollutionLevel.value = '轻度污染'
  } else if (exceedCount <= 4) {
    pollutionLevel.value = '中度污染'
  } else {
    pollutionLevel.value = '重度污染'
  }
  
  console.log(`🚨 污染水平计算完成: ${pollutionLevel.value} (超标元素: ${exceedCount}个)`)
}

// 获取群落类型名称
function getGroupName(type) {
  const typeMap = {
    0: '浮游植物',
    1: '浮游动物',
    2: '底栖生物',
    4: '游泳动物'
  }
  return typeMap[type] || '未知群落'
}

// 初始化图表
function initCharts() {
  console.log('📊 开始初始化所有图表...')
  
  // 检查DOM是否已渲染
  if (!chemicalChartRef.value || !biodiversityChartRef.value || !metalChartRef.value || 
      !predictionChartRef.value || !sedimentChartRef.value) {
    console.warn('⚠️ 图表容器未完全渲染，延迟初始化')
    setTimeout(() => {
      initCharts()
    }, 200)
    return
  }
  
  try {
    // 化学成分图表
    initChemicalChart()
    
    // 生物多样性图表
    initBiodiversityChart()
    
    // 金属离子图表
    initMetalChart()
    
    // AI预测图表
    initPredictionChart()
    
    // 沉积物图表
    initSedimentChart()
    
    // 水文参数时间序列图表
    initTimeSeriesChart()
    
    // 微观繁殖体丰度趋势图表
    initAbundanceTrendChart()
    
    // 微观繁殖体种类分布图表
    initMicroDistributionChart()
    
    // 营养盐浓度变化趋势图表
    initNutrientTrendChart()
    
    // 水质预测趋势图表
    initPredictionTrendChart()
    
    console.log('✅ 所有图表初始化完成')
  } catch (error) {
    console.error('❌ 图表初始化失败:', error)
  }
}

function initChemicalChart() {
  if (!chemicalChartRef.value) {
    console.warn('⚠️ 化学离子图表容器未找到')
    return
  }
  
  const chart = echarts.init(chemicalChartRef.value)
  
  console.log('🧪 初始化化学离子图表，数据：', chemicalIonData.value)
  
  // 如果没有化学离子数据，显示空图表
  if (!chemicalIonData.value || chemicalIonData.value.length === 0) {
    console.log('⚠️ 化学离子数据为空，显示空图表')
    chart.setOption({
      backgroundColor: 'transparent',
      title: {
        text: '暂无化学离子数据',
        left: 'center',
        top: 'middle',
        textStyle: { color: '#fff', fontSize: 16 }
      }
    })
    return
  }
  
  // 按样本层分组数据 (表层=2, 底层=1)
  const surfaceData = chemicalIonData.value.filter(item => item.sampleLayer === 2)
  const bottomData = chemicalIonData.value.filter(item => item.sampleLayer === 1)
  
  // 如果没有分层数据，使用第一条数据作为默认值
  const surface = surfaceData.length > 0 ? surfaceData[0] : chemicalIonData.value[0]
  const bottom = bottomData.length > 0 ? bottomData[0] : chemicalIonData.value[0]
  
  console.log('📊 化学离子图表数据处理：', { surface, bottom })
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value} mg/L<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['活性磷酸盐', '亚硝酸盐氮', '硝酸盐氮', '氨根'],
      textStyle: { color: '#fff' },
      top: 10
    },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '20%', containLabel: true },
    xAxis: {
      type: 'category',
      data: ['表层', '底层'],
      axisLabel: { color: '#fff' },
      axisLine: { lineStyle: { color: '#fff' } }
    },
    yAxis: {
      type: 'value',
      axisLabel: { color: '#fff' },
      axisLine: { lineStyle: { color: '#fff' } },
      splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
    },
    series: [
      {
        name: '活性磷酸盐',
        type: 'bar',
        data: [surface?.activePhosphate || 0, bottom?.activePhosphate || 0],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '亚硝酸盐氮',
        type: 'bar',
        data: [surface?.nitriteNitrogen || 0, bottom?.nitriteNitrogen || 0],
        itemStyle: { color: '#E6A23C' }
      },
      {
        name: '硝酸盐氮',
        type: 'bar',
        data: [surface?.nitrateNitrogen || 0, bottom?.nitrateNitrogen || 0],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '氨根',
        type: 'bar',
        data: [surface?.ammoniaHydrogen || 0, bottom?.ammoniaHydrogen || 0],
        itemStyle: { color: '#F56C6C' }
      }
    ]
  }
  
  chart.setOption(option)
  console.log('✅ 化学离子图表初始化完成')
}

function initBiodiversityChart() {
  if (!biodiversityChartRef.value) {
    console.warn('⚠️ 生物多样性图表容器未找到')
    return
  }
  
  const chart = echarts.init(biodiversityChartRef.value)
  
  console.log('🦠 初始化生物多样性柱状图，数据：', biodiversityData.value)
  
  // 如果没有数据，显示空图表
  if (!biodiversityData.value || biodiversityData.value.length === 0) {
    chart.setOption({
      backgroundColor: 'transparent',
      title: {
        text: '数据待录入',
        left: 'center',
        top: 'middle',
        textStyle: { color: '#00ffff', fontSize: 16 }
      }
    })
    return
  }

  // 处理数据：将4个指标分成4个柱状图系列
  const xAxisData = biodiversityData.value.map(item => 
    biodiversityTabs.find(tab => tab.value === item.type)?.label || `群落${item.type}`
  )
  
  const shannonData = biodiversityData.value.map(item => parseFloat(item.h_avg) || 0)
  const pielouData = biodiversityData.value.map(item => parseFloat(item.j_avg) || 0)
  const margalefData = biodiversityData.value.map(item => parseFloat(item.d_avg) || 0)
  const abundanceData = biodiversityData.value.map(item => Math.log10(parseFloat(item.abundance) || 1))

  console.log('📊 生物多样性数据处理：', { 
    xAxisData, 
    shannonData, 
    pielouData, 
    margalefData, 
    abundanceData 
  })

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`
        params.forEach(param => {
          let value = param.value
          if (param.seriesName === '丰度(log10)') {
            value = `${Math.pow(10, param.value).toFixed(0)} (log: ${param.value.toFixed(2)})`
          } else {
            value = param.value.toFixed(3)
          }
          result += `${param.marker} ${param.seriesName}: ${value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['Shannon指数', 'Pielou指数', 'Margalef指数', '丰度(log10)'],
      textStyle: { color: '#fff', fontSize: 10 },
      top: 5,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: { 
      left: '8%', 
      right: '4%', 
      bottom: '15%', 
      top: '25%', 
      containLabel: true 
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { 
        color: '#fff',
        fontSize: 10,
        rotate: 0
      },
      axisLine: { lineStyle: { color: '#fff' } },
      axisTick: { lineStyle: { color: '#fff' } }
    },
    yAxis: [
      {
        type: 'value',
        name: '指数值',
        nameTextStyle: { color: '#fff', fontSize: 10 },
        axisLabel: { color: '#fff', fontSize: 10 },
        axisLine: { lineStyle: { color: '#fff' } },
        splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
      }
    ],
    series: [
      {
        name: 'Shannon指数',
        type: 'bar',
        data: shannonData,
        itemStyle: { 
          color: '#72ACD1',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 8,
          formatter: '{c}'
        }
      },
      {
        name: 'Pielou指数',
        type: 'bar',
        data: pielouData,
        itemStyle: { 
          color: '#67C23A',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 8,
          formatter: '{c}'
        }
      },
      {
        name: 'Margalef指数',
        type: 'bar',
        data: margalefData,
        itemStyle: { 
          color: '#E6A23C',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 8,
          formatter: '{c}'
        }
      },
      {
        name: '丰度(log10)',
        type: 'bar',
        data: abundanceData,
        itemStyle: { 
          color: '#F56C6C',
          borderRadius: [2, 2, 0, 0]
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 8,
          formatter: function(params) {
            return Math.pow(10, params.value).toFixed(0)
          }
        }
      }
    ],
    animation: true,
    animationDuration: 1000,
    animationEasing: 'backOut'
  }
  
  chart.setOption(option)
  console.log('✅ 生物多样性柱状图初始化完成')
}

function initMetalChart() {
  if (!metalChartRef.value) {
    console.warn('⚠️ 重金属图表容器未找到')
    return
  }
  
  const chart = echarts.init(metalChartRef.value)
  
  console.log('🏭 初始化重金属图表，数据：', metalIonData.value)
  
  // 如果没有数据，显示空图表
  if (!metalIonData.value || metalIonData.value.length === 0) {
    console.log('⚠️ 重金属数据为空，显示空图表')
    chart.setOption({
      backgroundColor: 'transparent',
      title: {
        text: '暂无重金属数据',
        left: 'center',
        top: 'middle',
        textStyle: { color: '#fff', fontSize: 16 }
      }
    })
    return
  }
  
  // 重金属标准值映射
  const standardValues = {
    'Hg': 0.001, 'Cd': 0.005, 'Pb': 0.01, 'Cr': 0.05, 'As': 0.01,
    'Cu': 0.2, 'Zn': 0.1, 'Ni': 0.02, 'Fe': 5.0, 'Mn': 1.0,
    'Al': 2.0, 'Co': 0.05, 'V': 0.1, 'Mo': 0.01, 'Se': 0.02
  }
  
  const xAxisData = metalIonData.value.map(item => item.name)
  const detectValues = metalIonData.value.map(item => item.num)
  const standardLine = metalIonData.value.map(item => standardValues[item.name] || 0)
  
  console.log('📊 重金属图表数据处理：', { xAxisData, detectValues, standardLine })
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex
        const elementName = metalIonData.value[dataIndex].name
        const detectValue = metalIonData.value[dataIndex].num
        const standard = standardValues[elementName] || 0
        const status = detectValue > standard ? '超标' : '正常'
        
        let result = `<strong>${elementName}</strong><br/>`
        result += `检测值: ${detectValue} mg/L<br/>`
        result += `标准值: ${standard} mg/L<br/>`
        result += `状态: <span style="color: ${status === '超标' ? '#F56C6C' : '#67C23A'}">${status}</span>`
        return result
      }
    },
    legend: {
      data: ['检测值', '标准值'],
      textStyle: { color: '#fff' },
      top: 10
    },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '20%', containLabel: true },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: { 
        color: '#fff',
        rotate: 45,
        fontSize: 10
      },
      axisLine: { lineStyle: { color: '#fff' } }
    },
    yAxis: {
      type: 'value',
      name: '浓度 (mg/L)',
      nameTextStyle: { color: '#fff' },
      axisLabel: { color: '#fff' },
      axisLine: { lineStyle: { color: '#fff' } },
      splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } }
    },
    series: [
      {
        name: '检测值',
        type: 'bar',
        data: detectValues,
        itemStyle: { 
          color: (params) => {
            const elementName = metalIonData.value[params.dataIndex].name
            const value = metalIonData.value[params.dataIndex].num
            const standard = standardValues[elementName] || 0
            return value > standard ? '#F56C6C' : '#67C23A'
          }
        },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 10,
          formatter: '{c}'
        }
      },
      {
        name: '标准值',
        type: 'line',
        data: standardLine,
        itemStyle: { color: '#E6A23C' },
        lineStyle: { type: 'dashed', width: 2 },
        symbol: 'circle',
        symbolSize: 4
      }
    ]
  }
  
  chart.setOption(option)
  console.log('✅ 重金属图表初始化完成')
}

function initPredictionChart() {
  if (!predictionChartRef.value) {
    console.warn('⚠️ AI预测图表容器未找到')
    return
  }
  
  const chart = echarts.init(predictionChartRef.value)
  
  console.log('🤖 初始化AI预测图表，置信度：', aiPrediction.confidence)
  
  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: { color: '#fff' },
      formatter: '置信度: {c}%'
    },
    series: [{
      type: 'gauge',
      startAngle: 180,
      endAngle: 0,
      center: ['50%', '75%'],
      radius: '90%',
      min: 0,
      max: 100,
      splitNumber: 8,
      axisLine: {
        lineStyle: {
          width: 6,
          color: [
            [0.25, '#FF6E76'],
            [0.5, '#FDDD60'],
            [0.75, '#58D9F9'],
            [1, '#7CFFB2']
          ]
        }
      },
      pointer: {
        icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
        length: '12%',
        width: 20,
        offsetCenter: [0, '-60%'],
        itemStyle: {
          color: 'auto'
        }
      },
      axisTick: {
        length: 12,
        lineStyle: {
          color: 'auto',
          width: 2
        }
      },
      splitLine: {
        length: 20,
        lineStyle: {
          color: 'auto',
          width: 5
        }
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      title: {
        offsetCenter: [0, '-10%'],
        fontSize: 14,
        color: '#fff'
      },
      detail: {
        fontSize: 24,
        offsetCenter: [0, '-35%'],
        valueAnimation: true,
        color: 'auto',
        formatter: '{value}%'
      },
      data: [{
        value: aiPrediction.confidence,
        name: '预测置信度'
      }]
    }]
  }
  
  chart.setOption(option)
  console.log('✅ AI预测图表初始化完成')
}

// 🆕 沉积物成分图
function initSedimentChart() {
  if (!sedimentChartRef.value) return
  
  try {
    const chart = echarts.init(sedimentChartRef.value)
    
    const option = {
      title: {
        text: '沉积物成分分析',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      series: [{
        type: 'pie',
        radius: '65%',
        data: [
          { value: 45, name: '粉砂', itemStyle: { color: '#5470c6' } },
          { value: 28, name: '细砂', itemStyle: { color: '#91cc75' } },
          { value: 18, name: '粘土', itemStyle: { color: '#fac858' } },
          { value: 9, name: '有机质', itemStyle: { color: '#ee6666' } }
        ],
        label: {
          show: true,
          formatter: '{b}: {c}%',
          color: '#fff',
          fontSize: 10
        }
      }]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 沉积物图表初始化失败：', error)
  }
}

// 辅助函数：获取重金属标准值
function getMetalStandardValue(metalName) {
  const standardValues = {
    'Cu': 0.05,
    'Zn': 0.10,
    'Pb': 0.01,
    'Cd': 0.005,
    'Cr': 0.05,
    'Ni': 0.02,
    'As': 0.02,
    'Hg': 0.0005
  }
  return standardValues[metalName] || 0.01
}

// 🆕 时间序列图表初始化 - 改为堆叠面积图样式
function initTimeSeriesChart() {
  if (!timeSeriesChartRef.value) return
  
  try {
    const chart = echarts.init(timeSeriesChartRef.value)
    
    // 基于真实水文数据的时间序列
    const timeData = ['12-15 08:00', '12-15 09:30', '12-15 12:00', '12-16 09:00', '12-17 10:00']
    const salinityData = [35.2, 35.5, 35.8, 36.1, 35.9]
    const phData = [8.1, 8.2, 8.0, 8.3, 8.1]
    const temperatureData = [18.5, 19.2, 19.8, 18.9, 19.1]
    
    const option = {
      title: {
        text: '水文参数时间序列',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['盐度', 'pH值', '水温'],
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '25%',
        left: '10%',
        right: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeData,
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } }
      },
      yAxis: {
        type: 'value',
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } },
        splitLine: { lineStyle: { color: '#333' } }
      },
      series: [
        {
          name: '盐度',
          type: 'line',
          data: salinityData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(64, 158, 255, 0.8)'
              }, {
                offset: 1, color: 'rgba(64, 158, 255, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#409EFF' },
          itemStyle: { color: '#409EFF' }
        },
        {
          name: 'pH值',
          type: 'line',
          data: phData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(103, 194, 58, 0.8)'
              }, {
                offset: 1, color: 'rgba(103, 194, 58, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#67C23A' },
          itemStyle: { color: '#67C23A' }
        },
        {
          name: '水温',
          type: 'line',
          data: temperatureData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(255, 159, 64, 0.8)'
              }, {
                offset: 1, color: 'rgba(255, 159, 64, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#FF9F40' },
          itemStyle: { color: '#FF9F40' }
        }
      ]
    }
    
    chart.setOption(option)
    console.log('✅ 时间序列图表初始化成功')
  } catch (error) {
    console.error('❌ 时间序列图表初始化失败：', error)
  }
}

// 🆕 微观繁殖体丰度趋势图 - 改为堆叠面积图样式
function initAbundanceTrendChart() {
  if (!abundanceTrendChartRef.value) return
  
  try {
    const chart = echarts.init(abundanceTrendChartRef.value)
    
    // 基于真实生物数据的趋势
    const timeData = ['08:00', '09:30', '12:00', '14:00', '16:00']
    const diatomData = [125, 130, 135, 140, 138]
    const dinoflagellateData = [45, 48, 52, 50, 49]
    const cyanobacteriaData = [20, 22, 25, 28, 26]
    
    const option = {
      title: {
        text: '微观繁殖体丰度趋势',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['硅藻', '甲藻', '蓝藻'],
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '25%',
        left: '10%',
        right: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeData,
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } }
      },
      yAxis: {
        type: 'value',
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } },
        splitLine: { lineStyle: { color: '#333' } },
        name: '丰度(×10⁴个/L)',
        nameTextStyle: { color: '#fff', fontSize: 10 }
      },
      series: [
        {
          name: '硅藻',
          type: 'line',
          data: diatomData,
          smooth: true,
          stack: 'total',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(255, 99, 132, 0.8)'
              }, {
                offset: 1, color: 'rgba(255, 99, 132, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#FF6384' },
          itemStyle: { color: '#FF6384' }
        },
        {
          name: '甲藻',
          type: 'line',
          data: dinoflagellateData,
          smooth: true,
          stack: 'total',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(54, 162, 235, 0.8)'
              }, {
                offset: 1, color: 'rgba(54, 162, 235, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#36A2EB' },
          itemStyle: { color: '#36A2EB' }
        },
        {
          name: '蓝藻',
          type: 'line',
          data: cyanobacteriaData,
          smooth: true,
          stack: 'total',
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(75, 192, 192, 0.8)'
              }, {
                offset: 1, color: 'rgba(75, 192, 192, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#4BC0C0' },
          itemStyle: { color: '#4BC0C0' }
        }
      ]
    }
    
    chart.setOption(option)
    console.log('✅ 微观繁殖体丰度趋势图初始化成功')
  } catch (error) {
    console.error('❌ 微观繁殖体丰度趋势图初始化失败：', error)
  }
}

// 🆕 重金属污染监测图
function initMetalPollutionChart() {
  if (!metalPollutionChartRef.value) return
  
  try {
    const chart = echarts.init(metalPollutionChartRef.value)
    
    // 检查是否有数据
    const hasData = metalIonData.value && metalIonData.value.length > 0
    
    if (!hasData) {
      chart.setOption({
        backgroundColor: 'transparent',
        title: {
          text: '数据待录入',
          left: 'center',
          top: 'middle',
          textStyle: { color: '#00ffff', fontSize: 16 }
        }
      })
      return
    }
    
    // 基于真实重金属数据构建雷达图
    const metalNames = []
    const metalValues = []
    
    if (metalIonData.value && metalIonData.value.length > 0) {
      metalIonData.value.forEach(metal => {
        metalNames.push(metal.name)
        // 将实际值转换为百分比（相对于标准值）
        const standardValue = getMetalStandardValue(metal.name)
        const percentage = (metal.num / standardValue) * 100
        metalValues.push(Math.min(percentage, 100))
      })
    } else {
      // 默认数据
      metalNames.push('Cu', 'Zn', 'Pb', 'Cd', 'Cr', 'Ni')
      metalValues.push(25, 15, 8, 5, 12, 18)
    }
    
    const option = {
      title: {
        text: '重金属污染监测',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      radar: {
        indicator: metalNames.map(name => ({ name, max: 100 })),
        radius: '70%',
        axisName: {
          color: '#fff',
          fontSize: 10
        },
        axisLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.5)' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.3)' } }
      },
      series: [{
        type: 'radar',
        data: [{
          value: metalValues,
          name: '污染水平',
          itemStyle: { color: '#ff4757' },
          areaStyle: {
            color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
              { offset: 0, color: 'rgba(255, 71, 87, 0.3)' },
              { offset: 1, color: 'rgba(255, 71, 87, 0.1)' }
            ])
          }
        }]
      }]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 重金属污染图表初始化失败：', error)
  }
}

// 🆕 微观种类分布图
function initMicroSpeciesChart() {
  if (!microSpeciesChartRef.value) return
  
  try {
    const chart = echarts.init(microSpeciesChartRef.value)
    
    const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
    
    const option = {
      title: {
        text: '微观种类分布',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      series: [{
        type: 'pie',
        radius: ['30%', '60%'],
        center: ['50%', '50%'],
        data: microSpeciesData.value.map((item, index) => ({
          value: item.count,
          name: item.name,
          itemStyle: { color: colors[index] }
        })),
        label: {
          show: true,
          formatter: '{b}: {c}',
          color: '#fff',
          fontSize: 10
        }
      }]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 微观种类图表初始化失败：', error)
  }
}

// 🆕 AI水质预测图
function initAIPredictionChart() {
  if (!aiPredictionChartRef.value) return
  
  try {
    const chart = echarts.init(aiPredictionChartRef.value)
    
    const option = {
      title: {
        text: 'AI水质预测',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      series: [{
        type: 'gauge',
        radius: '75%',
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            width: 8,
            color: [
              [0.3, '#ff4757'],
              [0.7, '#ffa502'],
              [1, '#2ed573']
            ]
          }
        },
        pointer: {
          itemStyle: { color: 'auto' }
        },
        axisTick: {
          distance: -15,
          length: 8,
          lineStyle: { color: '#fff', width: 2 }
        },
        splitLine: {
          distance: -20,
          length: 15,
          lineStyle: { color: '#fff', width: 3 }
        },
        axisLabel: {
          color: '#fff',
          distance: -40,
          fontSize: 10
        },
        detail: {
          valueAnimation: true,
          formatter: '{value}%',
          color: '#00ffff',
          fontSize: 16
        },
        data: [{ value: aiPredictionAccuracy.value }]
      }]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ AI预测图表初始化失败：', error)
  }
}

// 🆕 营养盐浓度变化趋势图表 - 水平堆叠条形图样式
function initNutrientTrendChart() {
  if (!nutrientTrendChartRef.value) return
  
  try {
    const chart = echarts.init(nutrientTrendChartRef.value)
    
    // 基于真实化学离子数据
    const layerData = ['表层', '中层', '底层', '深层', '底质']
    
    const option = {
      title: {
        text: '营养盐浓度分布',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' },
        formatter: function (params) {
          let result = params[0].name + '<br/>'
          params.forEach(function (item) {
            result += item.marker + item.seriesName + ': ' + item.value + ' mg/L<br/>'
          })
          return result
        }
      },
      legend: {
        data: ['活性磷酸盐', '亚硝酸盐氮', '硝酸盐氮', '氨根', '硅酸盐'],
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '25%',
        left: '20%',
        right: '10%'
      },
      xAxis: {
        type: 'value',
        name: '浓度(mg/L)',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      yAxis: {
        type: 'category',
        data: layerData,
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } }
      },
      series: [
        {
          name: '活性磷酸盐',
          type: 'bar',
          stack: 'total',
          data: [0.0225, 0.0217, 0.0205, 0.0195, 0.0185],
          itemStyle: { color: '#67C23A' },
          label: {
            show: true,
            position: 'inside',
            textStyle: { color: '#fff', fontSize: 10 }
          }
        },
        {
          name: '亚硝酸盐氮',
          type: 'bar',
          stack: 'total',
          data: [0.0032, 0.0030, 0.0028, 0.0025, 0.0022],
          itemStyle: { color: '#409EFF' },
          label: {
            show: true,
            position: 'inside',
            textStyle: { color: '#fff', fontSize: 10 }
          }
        },
        {
          name: '硝酸盐氮',
          type: 'bar',
          stack: 'total',
          data: [0.1120, 0.1046, 0.0985, 0.0920, 0.0855],
          itemStyle: { color: '#E6A23C' },
          label: {
            show: true,
            position: 'inside',
            textStyle: { color: '#fff', fontSize: 10 }
          }
        },
        {
          name: '氨根',
          type: 'bar',
          stack: 'total',
          data: [0.0125, 0.0110, 0.0098, 0.0085, 0.0078],
          itemStyle: { color: '#F56C6C' },
          label: {
            show: true,
            position: 'inside',
            textStyle: { color: '#fff', fontSize: 10 }
          }
        },
        {
          name: '硅酸盐',
          type: 'bar',
          stack: 'total',
          data: [0.0856, 0.0798, 0.0745, 0.0689, 0.0634],
          itemStyle: { color: '#9C27B0' },
          label: {
            show: true,
            position: 'inside',
            textStyle: { color: '#fff', fontSize: 10 }
          }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 营养盐浓度图表初始化失败：', error)
  }
}

// 🆕 水质预测趋势图 - 改为堆叠面积图样式
function initPredictionTrendChart() {
  if (!predictionTrendChartRef.value) return
  
  try {
    const chart = echarts.init(predictionTrendChartRef.value)
    
    // 基于真实水质预测数据的时间序列
    const timeData = ['12:00', '13:00', '14:00', '15:00', '16:00']
    const temperatureData = [10.5, 10.6, 10.8, 11.0, 11.2]
    const salinityData = [30.2, 30.1, 30.0, 29.9, 29.8]
    const phData = [8.12, 8.15, 8.18, 8.20, 8.22]
    
    const option = {
      title: {
        text: '水质预测趋势',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        data: ['水温', '盐度', 'pH值'],
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      grid: {
        top: '25%',
        bottom: '25%',
        left: '10%',
        right: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeData,
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } }
      },
      yAxis: {
        type: 'value',
        axisLabel: { color: '#fff', fontSize: 9 },
        axisLine: { lineStyle: { color: '#444' } },
        splitLine: { lineStyle: { color: '#333' } }
      },
      series: [
        {
          name: '水温',
          type: 'line',
          data: temperatureData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(238, 102, 102, 0.8)'
              }, {
                offset: 1, color: 'rgba(238, 102, 102, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#ee6666' },
          itemStyle: { color: '#ee6666' }
        },
        {
          name: '盐度',
          type: 'line',
          data: salinityData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(84, 112, 198, 0.8)'
              }, {
                offset: 1, color: 'rgba(84, 112, 198, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#5470c6' },
          itemStyle: { color: '#5470c6' }
        },
        {
          name: 'pH值',
          type: 'line',
          data: phData,
          smooth: true,
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0, color: 'rgba(145, 204, 117, 0.8)'
              }, {
                offset: 1, color: 'rgba(145, 204, 117, 0.1)'
              }]
            }
          },
          lineStyle: { color: '#91cc75' },
          itemStyle: { color: '#91cc75' }
        }
      ]
    }
    
    chart.setOption(option)
    console.log('✅ 水质预测趋势图初始化成功')
  } catch (error) {
    console.error('❌ 水质预测趋势图初始化失败：', error)
  }
}

// 格式化调查时间
function formatSurveyTime(timeStr) {
  if (!timeStr) return '--'
  const date = new Date(timeStr)
  return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 切换生物多样性类型
function switchBiodiversityType(type) {
  activeBioTab.value = type
  nextTick(() => {
    initBiodiversityChart()
  })
}

// 打开形态分析图片
function openMorphologyImage(url) {
  if (url && url !== '--') {
    window.open(url, '_blank')
  }
}

// 图片错误处理
function handleImageError(event) {
  event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NS4wMDAxIDc1VjEyNUgxMjVWNzVINzUuMDAwMVoiIGZpbGw9IiNEOUQ5RDkiLz4KPC9zdmc+'
}

onMounted(async () => {
  console.log('🚀 专业大屏组件开始挂载...')
  console.log('📍 传入的站点信息：', props.stationInfo)
  console.log('📊 传入的查询参数：', props.query)
  
  try {
    // 开始时间更新
    updateTime()
    const timeInterval = setInterval(updateTime, 1000)
    
    // 在组件卸载时清除定时器
    onBeforeUnmount(() => {
      clearInterval(timeInterval)
      console.log('🧹 清除时间更新定时器')
    })
    
    console.log('⏰ 时间更新开始')
    
    // 🆕 先初始化选择器数据
    console.log('🔧 开始初始化选择器...')
    await initSelectors()
    
    // 🆕 添加全局调试函数
    window.debugCascadeSelector = debugDataStructure
    window.debugInvestigationCenters = () => {
      console.log('=== 全局调试：调查中心数据 ===')
      console.log('原始数据:', investigationCenters.value)
      console.log('计算属性结果:', investigationCenterOptions.value)
      console.log('当前选中值:', selectedInvestigationCenter.value)
    }
    
    // 🆕 自动调用一次调试函数
    setTimeout(() => {
      debugDataStructure()
    }, 1000)
    
    console.log('💡 提示：你可以在控制台运行 debugCascadeSelector() 或 debugInvestigationCenters() 来调试')
    
    // 确保组件完全挂载后再加载数据
    await nextTick()
    
    console.log('📦 开始加载站点数据...')
    // 加载站点数据
    await loadStationData()
    
    console.log('✅ 专业大屏组件挂载完成')
  } catch (error) {
    console.error('❌ 专业大屏组件挂载失败:', error)
    isLoading.value = false
  }
})

// 监听时间序列类型变化
watch(timeSeriesType, () => {
  nextTick(() => {
    initTimeSeriesChart()
  })
})

// 监听微观繁殖体样本类型变化
watch(activeMicroSample, () => {
  nextTick(() => {
    initMicroDistributionChart()
  })
})

// 🆕 监听站点信息变化，重新初始化图表
watch(stationInfo, (newStationInfo, oldStationInfo) => {
  if (newStationInfo.id !== oldStationInfo?.id) {
    console.log('📍 站点信息变化，重新初始化图表:', newStationInfo)
    nextTick(() => {
      initCharts()
    })
  }
}, { deep: true })

// 🆕 滚动和触摸事件处理
let touchStartX = 0
let touchStartY = 0

function handleScroll(event) {
  // 处理鼠标滚轮事件
  event.preventDefault()
  const container = event.currentTarget
  
  if (event.ctrlKey || event.metaKey) {
    // Ctrl+滚轮：水平滚动
    container.scrollLeft += event.deltaY
  } else {
    // 普通滚轮：垂直滚动
    container.scrollTop += event.deltaY
  }
}

function handleTouchStart(event) {
  touchStartX = event.touches[0].clientX
  touchStartY = event.touches[0].clientY
}

function handleTouchMove(event) {
  if (!touchStartX || !touchStartY) return
  
  const touchEndX = event.touches[0].clientX
  const touchEndY = event.touches[0].clientY
  
  const deltaX = touchStartX - touchEndX
  const deltaY = touchStartY - touchEndY
  
  const container = event.currentTarget
  
  // 根据滑动距离判断方向
  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    container.scrollLeft += deltaX * 0.5
  } else {
    // 垂直滑动
    container.scrollTop += deltaY * 0.5
  }
  
  touchStartX = touchEndX
  touchStartY = touchEndY
}

// 🆕 新增数据表格列定义
const environmentalColumns = [
  { title: '环境指标', key: 'indicator', width: 120 },
  { title: '数值', key: 'value', width: 80 },
  { title: '标准值', key: 'standard', width: 80 },
  { title: '评级', key: 'grade', width: 60 },
  { title: '状态', key: 'status', width: 80 }
]

const nutrientColumns = [
  { title: '营养盐类型', key: 'type', width: 100 },
  { title: '表层浓度', key: 'surface', width: 80 },
  { title: '底层浓度', key: 'bottom', width: 80 },
  { title: '变化趋势', key: 'trend', width: 80 },
  { title: '评估', key: 'assessment', width: 60 }
]

const predictionTrendColumns = [
  { title: '时间', key: 'time', width: 80 },
  { title: '水温(℃)', key: 'temperature', width: 80 },
  { title: '盐度(PSU)', key: 'salinity', width: 80 },
  { title: 'pH值', key: 'ph', width: 60 },
  { title: '预测置信度', key: 'confidence', width: 100 }
]

const biotoxicityColumns = [
  { title: '毒性指标', key: 'indicator', width: 100 },
  { title: '测定值', key: 'value', width: 80 },
  { title: '毒性等级', key: 'level', width: 80 },
  { title: '影响程度', key: 'impact', width: 80 },
  { title: '评估结果', key: 'result', width: 80 }
]

const ecologicalRiskColumns = [
  { title: '风险因子', key: 'factor', width: 100 },
  { title: '风险值', key: 'value', width: 80 },
  { title: '风险等级', key: 'level', width: 80 },
  { title: '可能性', key: 'probability', width: 80 },
  { title: '影响度', key: 'severity', width: 80 }
]

const waterQualityColumns = [
  { title: '质量参数', key: 'parameter', width: 100 },
  { title: '实测值', key: 'measured', width: 80 },
  { title: 'I类标准', key: 'class1', width: 80 },
  { title: 'II类标准', key: 'class2', width: 80 },
  { title: '等级评定', key: 'grade', width: 80 }
]

const marineHealthColumns = [
  { title: '健康指标', key: 'indicator', width: 100 },
  { title: '得分', key: 'score', width: 60 },
  { title: '权重', key: 'weight', width: 60 },
  { title: '加权得分', key: 'weightedScore', width: 80 },
  { title: '健康状态', key: 'status', width: 80 }
]

// 🆕 新增数据表格数据
const environmentalAnalysisData = ref([
  { indicator: '海水透明度', value: '2.1m', standard: '>1.5m', grade: 'A', status: '优秀' },
  { indicator: '溶解氧', value: '8.2mg/L', standard: '>6.0mg/L', grade: 'A', status: '良好' },
  { indicator: '化学需氧量', value: '1.8mg/L', standard: '<3.0mg/L', grade: 'A', status: '优秀' },
  { indicator: '石油类', value: '0.012mg/L', standard: '<0.05mg/L', grade: 'B', status: '良好' },
  { indicator: '悬浮物', value: '12.5mg/L', standard: '<20mg/L', grade: 'A', status: '优秀' }
])

const nutrientConcentrationData = ref([
  { type: '磷酸盐(PO₄³⁻)', surface: '0.15mg/L', bottom: '0.18mg/L', trend: '↗️ +0.02', assessment: '正常' },
  { type: '硝酸盐(NO₃⁻)', surface: '0.42mg/L', bottom: '0.38mg/L', trend: '↘️ -0.04', assessment: '优良' },
  { type: '亚硝酸盐(NO₂⁻)', surface: '0.08mg/L', bottom: '0.09mg/L', trend: '↗️ +0.01', assessment: '正常' },
  { type: '硅酸盐(SiO₃²⁻)', surface: '1.25mg/L', bottom: '1.32mg/L', trend: '↗️ +0.07', assessment: '稳定' },
  { type: '氨氮(NH₄⁺)', surface: '0.06mg/L', bottom: '0.08mg/L', trend: '↗️ +0.02', assessment: '良好' }
])

const predictionTrendData = ref([
  { time: '12:00', temperature: '10.5', salinity: '30.2', ph: '8.12', confidence: '94.2%' },
  { time: '13:00', temperature: '10.6', salinity: '30.1', ph: '8.15', confidence: '93.8%' },
  { time: '14:00', temperature: '10.8', salinity: '30.0', ph: '8.18', confidence: '95.1%' },
  { time: '15:00', temperature: '11.0', salinity: '29.9', ph: '8.20', confidence: '94.5%' },
  { time: '16:00', temperature: '11.2', salinity: '29.8', ph: '8.22', confidence: '93.9%' }
])

const biotoxicityData = ref([
  { indicator: '发光细菌毒性', value: '15.2 TU', level: '低毒', impact: '轻微', result: '安全' },
  { indicator: '藻类生长抑制', value: '8.5%', level: '低毒', impact: '轻微', result: '达标' },
  { indicator: '鱼类急性毒性', value: '0.3 LC50', level: '无毒', impact: '无影响', result: '优秀' },
  { indicator: '甲壳类毒性', value: '12.1 EC50', level: '低毒', impact: '轻微', result: '达标' },
  { indicator: '综合毒性评价', value: '0.18 TU', level: '低毒', impact: '可接受', result: '合格' }
])

const ecologicalRiskData = ref([
  { factor: '重金属污染', value: '0.25', level: '低风险', probability: '15%', severity: '轻微' },
  { factor: '有机污染物', value: '0.42', level: '中风险', probability: '35%', severity: '中等' },
  { factor: '富营养化', value: '0.18', level: '低风险', probability: '20%', severity: '轻微' },
  { factor: '生物入侵', value: '0.55', level: '中风险', probability: '40%', severity: '中等' },
  { factor: '栖息地破坏', value: '0.32', level: '低风险', probability: '25%', severity: '中等' }
])

const waterQualityData = ref([
  { parameter: '活性磷酸盐', measured: '0.15', class1: '≤0.10', class2: '≤0.20', grade: 'II类' },
  { parameter: '化学需氧量', measured: '1.8', class1: '≤2.0', class2: '≤3.0', grade: 'I类' },
  { parameter: '石油类', measured: '0.012', class1: '≤0.03', class2: '≤0.05', grade: 'I类' },
  { parameter: '重金属铜', measured: '0.003', class1: '≤0.005', class2: '≤0.010', grade: 'I类' },
  { parameter: '重金属锌', measured: '0.025', class1: '≤0.020', class2: '≤0.050', grade: 'II类' }
])

const marineHealthData = ref([
  { indicator: '水质指数', score: 88, weight: 0.25, weightedScore: 22.0, status: '良好' },
  { indicator: '生物多样性', score: 92, weight: 0.20, weightedScore: 18.4, status: '优秀' },
  { indicator: '生态功能', score: 85, weight: 0.20, weightedScore: 17.0, status: '良好' },
  { indicator: '环境压力', score: 90, weight: 0.15, weightedScore: 13.5, status: '优秀' },
  { indicator: '人类活动影响', score: 87, weight: 0.20, weightedScore: 17.4, status: '良好' }
])

// 🆕 污染水平判断
function getPollutionLevelText(level) {
  const levelMap = {
    'low': '轻度',
    'medium': '中度', 
    'high': '重度',
    'normal': '正常'
  }
  return levelMap[level] || '正常'
}

// 🆕 生态风险评估图 - 散点图替换表格
function initEcologicalRiskChart() {
  if (!ecologicalRiskChartRef.value) return
  
  try {
    const chart = echarts.init(ecologicalRiskChartRef.value)
    
    const option = {
      title: {
        text: '生态风险评估',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' },
        formatter: function(params) {
          return `<strong>${params.data.name}</strong><br/>
                  风险值: ${params.data.value[0]}<br/>
                  影响度: ${params.data.value[1]}<br/>
                  风险等级: ${params.data.level}`
        }
      },
      grid: {
        top: '25%',
        bottom: '15%',
        left: '15%',
        right: '15%'
      },
      xAxis: {
        type: 'value',
        name: '风险值',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      yAxis: {
        type: 'value',
        name: '影响度',
        nameTextStyle: { color: '#fff' },
        axisLabel: { color: '#fff' },
        axisLine: { lineStyle: { color: '#00ffff' } },
        splitLine: { lineStyle: { color: 'rgba(0, 255, 255, 0.2)' } }
      },
      series: [{
        type: 'scatter',
        symbolSize: function (data) {
          return Math.sqrt(data[0] * 100) + 5
        },
        data: [
          { value: [0.25, 0.3], name: '重金属污染', level: '低风险' },
          { value: [0.42, 0.6], name: '有机污染物', level: '中风险' },
          { value: [0.18, 0.25], name: '富营养化', level: '低风险' },
          { value: [0.55, 0.7], name: '生物入侵', level: '中风险' },
          { value: [0.32, 0.45], name: '栖息地破坏', level: '低风险' }
        ],
        itemStyle: {
          color: function(params) {
            const riskLevel = params.data.level
            if (riskLevel === '低风险') return '#2ed573'
            else if (riskLevel === '中风险') return '#ffa502'
            else return '#ff4757'
          },
          opacity: 0.8
        },
        markLine: {
          lineStyle: { color: '#ff4757', width: 2, type: 'dashed' },
          data: [
            { xAxis: 0.5, label: { formatter: '高风险线', color: '#fff' } },
            { yAxis: 0.5, label: { formatter: '高影响线', color: '#fff' } }
          ]
        }
      }]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 生态风险图表初始化失败：', error)
  }
}

// 🆕 水质等级评估图 - 环形图替换表格
function initWaterQualityChart() {
  if (!waterQualityChartRef.value) return
  
  try {
    const chart = echarts.init(waterQualityChartRef.value)
    
    const option = {
      title: {
        text: '水质等级评估',
        left: 'center',
        textStyle: {
          color: '#00ffff',
          fontSize: 14
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00ffff',
        textStyle: { color: '#fff' }
      },
      legend: {
        bottom: 5,
        textStyle: { color: '#fff', fontSize: 10 }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 60, name: 'I类水质', itemStyle: { color: '#2ed573' } },
            { value: 30, name: 'II类水质', itemStyle: { color: '#00ffff' } },
            { value: 8, name: 'III类水质', itemStyle: { color: '#ffa502' } },
            { value: 2, name: 'IV类水质', itemStyle: { color: '#ff4757' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c}%',
            color: '#fff',
            fontSize: 10
          },
          labelLine: {
            lineStyle: { color: '#fff' }
          }
        }
      ]
    }
    
    chart.setOption(option)
    
    const resizeHandler = () => chart.resize()
    window.addEventListener('resize', resizeHandler)
    
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      chart.dispose()
    })
    
  } catch (error) {
    console.error('❌ 水质等级图表初始化失败：', error)
  }
}

// 🆕 计算属性：为选择器提供正确格式的数据
const investigationCenterOptions = computed(() => {
  console.log('🔧 [DEBUG] 计算调查中心选项，原始数据：', investigationCenters.value)
  console.log('🔧 [DEBUG] 数据类型：', typeof investigationCenters.value)
  console.log('🔧 [DEBUG] 是否为数组：', Array.isArray(investigationCenters.value))
  console.log('🔧 [DEBUG] 数组长度：', investigationCenters.value?.length)
  
  // 🆕 添加防护检查
  if (!investigationCenters.value) {
    console.log('🔧 [DEBUG] investigationCenters.value 为空')
    return []
  }
  
  if (!Array.isArray(investigationCenters.value)) {
    console.log('🔧 [DEBUG] investigationCenters.value 不是数组')
    return []
  }
  
  if (investigationCenters.value.length === 0) {
    console.log('🔧 [DEBUG] investigationCenters.value 数组为空')
    return []
  }
  
  // 🆕 检查第一个元素的结构
  console.log('🔧 [DEBUG] 第一个元素：', investigationCenters.value[0])
  console.log('🔧 [DEBUG] 第一个元素的字段：', Object.keys(investigationCenters.value[0] || {}))
  
  try {
    const options = investigationCenters.value.map((ic, index) => {
      console.log(`🔧 [DEBUG] 处理第${index + 1}个调查中心：`, ic)
      console.log(`🔧 [DEBUG] ID: ${ic.id}, Name: ${ic.name}`)
      
      return {
        label: ic.name || `调查中心${ic.id}`,
        value: ic.id,
        key: ic.id
      }
    })
    console.log('🔧 [DEBUG] 调查中心选项结果：', options)
    return options
  } catch (error) {
    console.error('❌ [DEBUG] 调查中心选项计算出错：', error)
    return []
  }
})

const routeOptions = computed(() => {
  console.log('🔧 [DEBUG] 计算航线选项，原始数据：', routes.value)
  
  if (!Array.isArray(routes.value)) {
    return []
  }
  
  const options = routes.value.map(r => ({
    label: r.name || `航线${r.id}`,
    value: r.id,
    key: r.id
  }))
  console.log('🔧 [DEBUG] 航线选项结果：', options)
  return options
})

const timeOptions = computed(() => {
  console.log('🔧 [DEBUG] 计算调查次数选项，原始数据：', times.value)
  
  if (!Array.isArray(times.value)) {
    return []
  }
  
  const options = times.value.map(t => ({
    label: `第${t.times}次调查`,
    value: t.id,
    key: t.id
  }))
  console.log('🔧 [DEBUG] 调查次数选项结果：', options)
  return options
})

const stationOptions = computed(() => {
  console.log('🔧 [DEBUG] 计算站点选项，原始数据：', availableStations.value)
  
  if (!Array.isArray(availableStations.value)) {
    return []
  }
  
  const options = availableStations.value.map(s => ({
    label: s.name || s.stationName || `站点${s.id}`,
    value: s.id,
    key: s.id
  }))
  console.log('🔧 [DEBUG] 站点选项结果：', options)
  return options
})

// 🆕 新增：直接检查数据的调试函数
function debugDataStructure() {
  console.log('🔍 [DEBUG] ========== 数据结构调试 ==========')
  console.log('investigationCenters.value：', investigationCenters.value)
  console.log('investigationCenters.value 类型：', typeof investigationCenters.value)
  console.log('investigationCenters.value 长度：', investigationCenters.value?.length)
  console.log('investigationCenters.value 是否为数组：', Array.isArray(investigationCenters.value))
  
  if (investigationCenters.value && investigationCenters.value.length > 0) {
    console.log('第一个元素：', investigationCenters.value[0])
    console.log('第一个元素的所有属性：', Object.keys(investigationCenters.value[0]))
  }
  
  console.log('computed属性结果：', investigationCenterOptions.value)
  console.log('==========================================')
}

// 🆕 调试函数：选择器点击事件
function debugCenterClick() {
  console.log('🖱️ [DEBUG] 点击调查中心选择器')
  console.log('🔧 [DEBUG] 当前调查中心数据：', investigationCenters.value)
  console.log('🔧 [DEBUG] 当前选项数据：', investigationCenterOptions.value)
  console.log('🔧 [DEBUG] 当前选中值：', selectedInvestigationCenter.value)
}

function debugRouteClick() {
  console.log('🖱️ [DEBUG] 点击航线选择器')
  console.log('🔧 [DEBUG] 当前航线数据：', routes.value)
  console.log('🔧 [DEBUG] 当前选项数据：', routeOptions.value)
  console.log('🔧 [DEBUG] 当前选中值：', selectedRoute.value)
}

function debugTimeClick() {
  console.log('🖱️ [DEBUG] 点击调查次数选择器')
  console.log('🔧 [DEBUG] 当前调查次数数据：', times.value)
  console.log('🔧 [DEBUG] 当前选项数据：', timeOptions.value)
  console.log('🔧 [DEBUG] 当前选中值：', selectedTime.value)
}

function debugStationClick() {
  console.log('🖱️ [DEBUG] 点击站点选择器')
  console.log('🔧 [DEBUG] 当前站点数据：', availableStations.value)
  console.log('🔧 [DEBUG] 当前选项数据：', stationOptions.value)
  console.log('🔧 [DEBUG] 当前选中值：', selectedStationId.value)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 调试函数：选择事件
function debugCenterSelect(value) {
  console.log('✅ [DEBUG] 调查中心选择事件，选中值：', value)
  const selected = investigationCenters.value.find(ic => ic.id === value)
  console.log('✅ [DEBUG] 选中的调查中心对象：', selected)
}

function debugRouteSelect(value) {
  console.log('✅ [DEBUG] 航线选择事件，选中值：', value)
  const selected = routes.value.find(r => r.id === value)
  console.log('✅ [DEBUG] 选中的航线对象：', selected)
}

function debugTimeSelect(value) {
  console.log('✅ [DEBUG] 调查次数选择事件，选中值：', value)
  const selected = times.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 滚动处理
function handleScroll(event) {
  if (event.ctrlKey) {
    event.preventDefault()
    const container = event.currentTarget
    container.scrollLeft += event.deltaY
  }
}

function handleTouchStart(event) {
  // 处理触摸开始
}

function handleTouchMove(event) {
  // 处理触摸移动
}

// 污染等级文本
function getPollutionLevelText(level) {
  switch (level) {
    case 'low': return '低污染'
    case 'medium': return '中等污染'
    case 'high': return '高污染'
    default: return '正常'
  }
}

// 生命周期钩子
onMounted(async () => {
  console.log('🚀 专业大屏组件挂载完成')

  // 启动时间更新
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)

  // 初始化选择器
  await initSelectors()

  // 清理定时器
  onBeforeUnmount(() => {
    clearInterval(timeInterval)
  })
})
</script>

<style scoped>
.professional-dashboard {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
  font-family: 'Microsoft YaHei', sans-serif;
}

#ocean-animations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #001122 0%, #000000 100%);
  z-index: 1;
}

.status-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: bold;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.status-message.info {
  background: rgba(0, 123, 255, 0.9);
  border: 1px solid #007bff;
}

.status-message.success {
  background: rgba(40, 167, 69, 0.9);
  border: 1px solid #28a745;
}

.status-message.warning {
  background: rgba(255, 193, 7, 0.9);
  border: 1px solid #ffc107;
  color: #000;
}

.status-message.error {
  background: rgba(220, 53, 69, 0.9);
  border: 1px solid #dc3545;
}

.scroll-hints {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.scroll-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 255, 255, 0.1);
  border: 1px solid #00ffff;
  border-radius: 6px;
  color: #00ffff;
  font-size: 12px;
}

.dashboard-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: rgba(0, 20, 40, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid #00ffff;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.left-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 200px;
}

.time-display {
  color: #00ffff;
  font-size: 18px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}

.station-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.station-name {
  color: #fff;
  font-weight: bold;
}

.coordinates {
  color: #aaa;
}

.center-title-compact {
  flex: 1;
  text-align: center;
}

.main-title-compact {
  color: #00ffff;
  font-size: 24px;
  margin: 0 0 10px 0;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.selectors-container-compact {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.selector-group-compact {
  display: flex;
  align-items: center;
}

.refresh-btn-compact,
.debug-btn-compact {
  min-width: 40px !important;
}

.right-section {
  min-width: 120px;
  text-align: right;
}

.switch-btn-compact {
  background: linear-gradient(45deg, #00ffff, #0080ff) !important;
  border: none !important;
  color: #000 !important;
  font-weight: bold !important;
}

.dashboard-content-scrollable {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-x: auto;
  overflow-y: auto;
  padding: 20px;
  z-index: 10;
}

.chart-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  min-width: 1600px;
}

.compact-row {
  height: 280px;
}

.chart-panel {
  background: rgba(0, 30, 60, 0.8);
  border: 1px solid #00ffff;
  border-radius: 8px;
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.compact-panel {
  flex: 1;
  min-width: 300px;
}

.panel-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid rgba(0, 255, 255, 0.3);
  background: rgba(0, 255, 255, 0.1);
}

.panel-header-compact h4 {
  color: #00ffff;
  margin: 0;
  font-size: 14px;
  font-weight: bold;
}

.status-indicator-compact {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
}

.status-indicator-compact.active {
  background: #2ed573;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.hydro-grid-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  padding: 15px;
  height: calc(100% - 50px);
}

.hydro-item-compact {
  text-align: center;
  padding: 10px;
  background: rgba(0, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(0, 255, 255, 0.2);
}

.value-compact {
  font-size: 20px;
  font-weight: bold;
  color: #00ffff;
  margin-bottom: 5px;
}

.label-compact {
  font-size: 11px;
  color: #aaa;
}

.chart-controls-compact {
  display: flex;
  gap: 5px;
}

.chart-container-compact {
  height: calc(100% - 50px);
  width: 100%;
}

.trend-indicator-compact {
  display: flex;
  align-items: center;
  gap: 5px;
}

.trend-arrow {
  font-size: 16px;
}

.trend-arrow.up {
  color: #2ed573;
}

.trend-arrow.down {
  color: #ff4757;
}

.trend-arrow.stable {
  color: #ffa502;
}

.layer-indicator-compact {
  display: flex;
  gap: 8px;
}

.layer-compact {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

.layer-compact.surface {
  background: rgba(84, 112, 198, 0.3);
  color: #5470c6;
  border: 1px solid #5470c6;
}

.layer-compact.bottom {
  background: rgba(145, 204, 117, 0.3);
  color: #91cc75;
  border: 1px solid #91cc75;
}

.diversity-stats-compact {
  display: flex;
  gap: 10px;
  font-size: 10px;
}

.stat-compact {
  padding: 2px 6px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 3px;
  color: #00ffff;
}

.pollution-level-compact {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.pollution-level-compact.low {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.pollution-level-compact.medium {
  background: rgba(255, 165, 2, 0.3);
  color: #ffa502;
  border: 1px solid #ffa502;
}

.pollution-level-compact.high {
  background: rgba(255, 71, 87, 0.3);
  color: #ff4757;
  border: 1px solid #ff4757;
}

.species-count-compact {
  padding: 2px 8px;
  background: rgba(0, 255, 255, 0.1);
  border-radius: 4px;
  color: #00ffff;
  font-size: 11px;
  font-weight: bold;
}

.analysis-status-compact {
  display: flex;
  gap: 5px;
}

.status-compact {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
}

.status-compact.excellent {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.prediction-accuracy-compact {
  color: #00ffff;
  font-size: 11px;
  font-weight: bold;
}

.sediment-type-compact {
  padding: 2px 8px;
  background: rgba(250, 200, 88, 0.3);
  color: #fac858;
  border: 1px solid #fac858;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.nutrient-trend-compact {
  display: flex;
  gap: 8px;
  font-size: 10px;
}

.trend-compact {
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: bold;
}

.trend-compact.up {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.trend-compact.down {
  background: rgba(255, 71, 87, 0.3);
  color: #ff4757;
  border: 1px solid #ff4757;
}

.prediction-status-compact {
  display: flex;
  gap: 5px;
}

.status-compact.stable {
  background: rgba(0, 255, 255, 0.3);
  color: #00ffff;
  border: 1px solid #00ffff;
}

.timeline-status-compact {
  color: #00ffff;
  font-size: 11px;
  font-weight: bold;
}

.survey-timeline-compact {
  padding: 10px;
  height: calc(100% - 50px);
  overflow-y: auto;
}

.timeline-item-compact {
  padding: 8px 12px;
  margin-bottom: 8px;
  background: rgba(0, 255, 255, 0.05);
  border: 1px solid rgba(0, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.timeline-item-compact:hover {
  background: rgba(0, 255, 255, 0.1);
  border-color: #00ffff;
}

.timeline-item-compact.active {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
}

.timeline-date-compact {
  color: #00ffff;
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 4px;
}

.timeline-desc-compact {
  color: #aaa;
  font-size: 11px;
}

.toxicity-level-compact {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.toxicity-level-compact.low {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.risk-level-compact {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.risk-level-compact.medium {
  background: rgba(255, 165, 2, 0.3);
  color: #ffa502;
  border: 1px solid #ffa502;
}

.quality-grade-compact {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.quality-grade-compact.grade-ii {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.health-score-compact {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.health-score-compact.excellent {
  background: rgba(46, 213, 115, 0.3);
  color: #2ed573;
  border: 1px solid #2ed573;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: #00ffff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
.dashboard-content-scrollable::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dashboard-content-scrollable::-webkit-scrollbar-track {
  background: rgba(0, 255, 255, 0.1);
  border-radius: 4px;
}

.dashboard-content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.5);
  border-radius: 4px;
}

.dashboard-content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .chart-row {
    min-width: 1200px;
  }

  .compact-panel {
    min-width: 250px;
  }
}

@media (max-width: 1200px) {
  .main-title-compact {
    font-size: 20px;
  }

  .selectors-container-compact {
    gap: 8px;
  }

  .chart-row {
    min-width: 1000px;
  }
}
</style>.value.find(t => t.id === value)
  console.log('✅ [DEBUG] 选中的调查次数对象：', selected)
}

function debugStationSelect(value) {
  console.log('✅ [DEBUG] 站点选择事件，选中值：', value)
  const selected = availableStations.value.find(s => s.id === value)
  console.log('✅ [DEBUG] 选中的站点对象：', selected)
}

// 🆕 滚动处理
function handleScroll(event) {
  // 处理滚动事件
  if (event.ctrlKey) {
    // Ctrl+滚轮实现水平滚动
    event.preventDefault()
    const container = event.currentTarget
    container.scrollLeft += event.deltaY
  }
}

function handleTouchStart(event) {
  // 处理触摸开始
}

function handleTouchMove(event) {
  // 处理触摸移动
}

// 🆕 污染等级文本
function getPollutionLevelText(level) {
  switch (level) {
    case 'low': return '低污染'
    case 'medium': return '中等污染'
    case 'high': return '高污染'
    default: return '正常'
  }
}

// 🆕 生命周期钩子
onMounted(async () => {
  console.log('🚀 专业大屏组件挂载完成')

  // 启动时间更新
  updateTime()
  const timeInterval = setInterval(updateTime, 1000)

  // 初始化选择器
  await initSelectors()

  // 清理定时器
  onBeforeUnmount(() => {
    clearInterval(timeInterval)
  })
})
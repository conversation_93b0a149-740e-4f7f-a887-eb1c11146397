<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>10号</title>
	<!-- <meta http-equiv=refresh content="1"> -->
	<link rel="stylesheet" href="cs/index.css">
	<script src="js/jquery.js"></script>
	<script src="js/echarts.min.js"></script>
	<script src="js/can.js"></script>
	<script src="js/china.js"></script>
	<script src="js/vue.js"></script>
	<script src="js/data.js"></script>
	<script src="js/beihai.js"></script>			
</head>
<body>
	<div class="content">
		<!-- 底层背景动画-开始 -->
		<div id="animations">
			<div class="animation animation1">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation2">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation3 fanzhuan">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation4">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation5 fanzhuan">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation6">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation7 fanzhuan">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation8">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation9 fanzhuan">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
			<div class="animation animation10">
				<div class="animate1"></div>
				<div class="animate2"></div>
				<div class="animate3"></div>
			</div>
		</div>
		<!-- 底层背景动画-开始 -->
		<!-- 显示屏头部开始 -->
		<div id="head">
			<!-- 时间日期 -->
			<div class="getDate">
				<span id="time">09:00:00</span>
				<span id="week">星期三</span>
				<span id="year">2018-07-28</span>
			</div>
			<!-- 天气状况 -->
			<div class="weather">
				<span id="sky">多云</span>
				<span id="temperatur">35~28度</span>
				<span id="air">空气质量</span>
				<span id="state">优</span>
			</div>
			<!-- 大标题 -->
			<h1> 南方草牧商品交易所</h1>
		</div>
		<!-- 显示屏头部结束 -->	
		<!-- 显示屏中间-开始-->
		<div id="body">
			<div class="bodyLeft rel">
				<div class="bodyLeftTop">
					<div class="childtitle"><h2>各区域产品挂牌数</h2></div>
					<div class="add" onclick="Show(this,'bodyLeft',2.8,'before')">+</div>
					<!-- 数据中心-开始 -->
					<div class="dataCenter clear"><ul class="Data"></ul></div>
					<!-- 数据中心-开始 -->
					<div id="guapai" class="guapai"></div>
					<!-- 产品挂牌实时监控-开始 -->
					<div class="yuyue">
						<div class="yuyuejiankong">
							<div id="left-top-right" class="bodyLeftTopGPZB">
								<div class="GPZB">
									<ul></ul>
									<p>今日交割<span></span></p>
								</div>
								<div class="GPZB">
									<ul></ul>
									<p>今日挂牌<span></span></p>
								</div>
								<div class="GPZB">
									<ul></ul>
									<p>今日冻结<span></span></p>
								</div>
							</div>
							<div id="left-top-right-circle" class="left-top-right-circle"></div>
						</div>
					</div>
					<!-- 产品挂牌实时监控-结束 -->
				</div>
				<div class="bodyLeftBottom rel">
					<div class="add" onclick="Show(this,'bodyLeft',2.8,'after')">+</div>
					<div class="bodyLeftBottomLeft">
						<div class="childtitle"><h2>当前成交总量</h2></div>
						<div id="left-bottom"></div>
					</div>
					<!-- 月成交量-开始 -->
					<div class="bodyLeftBottomRight rel">
						<div class="childtitle"><h2>月成交总量</h2></div>
						<div class="t-right" >
							<ul id="cp">
								<li class="line"></li>
								<li>
									<span>一月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>二月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>

								<li>
									<span>三月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>四月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>五月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>六月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>七月</span>
									<div>
										<p class="active"><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>八月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>九月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>十月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>	
								<li>
									<span>十一月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>
								<li>
									<span>十二月</span>
									<div>
										<p><i></i><span></span></p>
									</div>
								</li>	
							</ul>
						</div>						
					</div>
					<!-- 月成交量-结束 -->
				</div>
			</div>
			<div class="bodyMiddle rel">
				<div class="bodyMiddleChild">
					<div class="add" onclick="Show(this,'bodyMiddle',1.8,'after')">+</div>
					<div class="childtitle"><h2>牧草产能区域分布</h2></div>
					<div class="navbar">
						<span class="active">全国分布</span>
						<span>北海区域</span>
						<span>大盘走势</span>
					</div>
					<!-- 分布区域-开始 -->
					<div class="mapmain">
						<!-- 牧草产能区域分布数据-开始 -->
						<div><ul id="list"></ul></div>
						<!-- 牧草产能区域分布数据-开始 -->
						<!-- 中国地图-开始 -->
						<div class="map" id="map"></div>
						<!-- 中国地图-结束 -->
						<!-- 北海地图-开始 -->
						<div class="map" id="map1"></div>
						<!-- 北海地图-结束 -->
						<!-- 大盘走势-开始 -->
						<div class="map" id="map2"></div>
						<!-- 大盘走势-结束 -->
					</div>
					<!-- 分布区域-结束 -->
					<!-- 地图下面亮光动画-开始 -->
					<div id="sun"></div>
					<!-- 地图下面亮光动画-结束 -->
				</div>
			</div>
			<div class="bodyRight">
				<!-- 成交量实时动态滚动-开始 -->
				<div class="bodyRightTop rel">
					<div class="childtitle"><h2>成交订单实时动态</h2></div>
					<div class="navbar">
						<span>草牧板块</span>
						<span class="active">猪板块</span>
						<span>牛板块</span>
						<span>羊板块</span>
					</div>
					<div class="add" onclick="Show(this,'bodyRight',2.8,'before')">+</div>
					<div class="bodyRightTopBG">
						<div class="default">
							<span class="num">成交订单号</span>
							<span class="name">产品名称</span>
							<span class="time">数量</span>
							<span class="status">估重/规格</span>
							<span>成交时间</span>
							<span>订单状态</span>
						</div>
						<!-- 成交订单数据-开始 -->
						<div class="liushuihaoul clear">
							<ul class="moveul"></ul>
						</div>
						<!-- 成交订单数据-结束 -->
						<!-- 下方消息提示滚动-开始 -->
						<div class="call">
							<ul class="moveul"></ul>
						</div>
						<!-- 下方消息提示滚动-结束 -->
					</div>					
				</div>
				<!-- 成交量实时动态滚动-结束 -->
				<!-- 挂牌产品价格动态折线图-开始 -->
				<div class="bodyRightBottom rel">
					<div class="childtitle"><h2>挂牌产品价格动态</h2></div>
					<div class="add" onclick="Show(this,'bodyRight',2.8,'after')">+</div>	
					<div id="jiagezoushi"></div>			
				</div>
				<!-- 挂牌产品价格动态折线图-结束 -->
			</div>
		</div>
		<!-- 显示屏中间-结束 -->
		<!-- 显示屏底部-开始-->		
		<div id="foot">
			<!-- 挂牌会员实时监控-开始 -->
			<div class="footparent0">
				<div class="footChild">
					<div class="childtitle"><h2>挂牌会员实时监控</h2></div>
					<div class="add" onclick="Show(this,'footparent0',2.8,'after')">+</div>
					<!-- 今日入驻申请会员数量-开始 -->
					<div class="huiYuanLst">
						<div class="yibiao" id="yibiao1"></div>	
						<div class="huiyuan">
							<ul  class="fangkuai"></ul>
							<span></span>
							<p>今日入驻申请会员数量</p>
						</div>
					</div>
					<!-- 今日入驻申请会员数量-结束 -->
					<!-- 入驻动态-开始 -->
					<div class="huiYuanLst">
						<span class="ruzhustatus">(入驻动态)</span>
						<div class="contgundong">
							<ul class="moveul"></ul>
						</div>
					</div>
					<!-- 入驻动态-结束 -->
					<!-- 今日申请实名会员数量-开始 -->
					<div class="huiYuanLst huiYuanLst3">
						<div class="yibiao" id="yibiao2"></div>	
						<div class="huiyuan">
							<ul  class="fangkuai"></ul>
							<span></span>
							<p>今日申请实名会员数量</p>
						</div>
					</div>
					<!-- 今日申请实名会员数量-结束 -->
					<!-- 今日通过实名认证会员数量-开始 -->
					<div class="huiYuanLst">
						<div class="yibiao" id="yibiao3"></div>	
						<div class="huiyuan">
							<ul class="fangkuai"></ul>
							<span></span>
							<p>今日通过实名认证申请会员数量</p>
						</div>
					</div>
					<!-- 今日通过实名认证会员数量-结束 -->
				</div>
			</div>
			<!-- 挂牌会员实时监控-结束 -->
			<!-- 北海市猪链网动态-开始 -->
			<div class="footparent1">
				<div class="footChild">
					<div class="childtitle"><h2>北海市猪链网动态</h2></div>
					<div class="add" onclick="Show(this,'footparent1',2.8,'after')">+</div>
					<div id="jiage"></div>
				</div>
			</div>
			<!-- 北海市猪链网动态-结束 -->
			<!-- 交易大厅实时监控-开始 -->
			<div class="footparent2">
				<div class="footChild">
					<div class="childtitle"><h2>交易大厅实时监控</h2></div>
					<div class="add" onclick="Show(this,'footparent2',2.8,'after')">+</div>
					<!-- 各板块成交量格子动画-开始 -->
					<div class="chengjiaoliang">
						<div class="CJL clear">
							<p></p>
							<span>草木板块成交量</span>
							<ul class="fangkuai"></ul>
						</div>
						<div class="CJL clear">
							<p></p>
							<span>猪联网成交量</span>
							<ul class="fangkuai"></ul>
						</div>
						<div class="CJL clear">
							<p></p>
							<span>牛联网成交量</span>
							<ul class="fangkuai"></ul>
						</div>
						<div class="CJL clear">
							<p></p>
							<span>羊联网成交量</span>
							<ul class="fangkuai"></ul>
						</div>	
					</div>
					<!-- 各板块成交量格子动画-开始 -->
					<div id="CJpie"></div>	
				</div>
			</div>
			<!-- 交易大厅实时监控-结束 -->
			<!-- 成交量实时监控-开始 -->
			<div class="footparent3">
				<div class="footChild">
					<div class="childtitle"><h2>成交量实时监控</h2></div>
					<div class="add" onclick="Show(this,'footparent3',2.8,'after')">+</div>
					<!-- 平均成交时间刻度轴-开始 -->
					<div id="timebar">
						<span id="pjtime">平均单笔成交时间：</span>
						<ul class="kedu clear"></ul>
						<div class="kuang">
							<div class="tianchong"></div>
						</div>
					</div>
					<!-- 平均成交时间刻度轴-开始 -->
					<div id="cjliang"></div>
				</div>
			</div>
			<!-- 成交量实时监控-结束 -->
		</div>	
		<!-- 显示屏底部-结束 -->	
		<div class="mask">
			<div class="maskContent"></div>
		</div>
	</div>
</body>
</html>
<script src="js/index.js"></script>
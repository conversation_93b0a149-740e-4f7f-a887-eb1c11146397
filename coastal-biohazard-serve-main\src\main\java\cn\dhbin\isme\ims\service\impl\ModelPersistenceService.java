package cn.dhbin.isme.ims.service.impl;

import org.springframework.stereotype.Service;
import weka.classifiers.trees.RandomForest;

import java.io.*;

@Service
public class ModelPersistenceService {

    // 保存模型
    public void saveModel(Serializable model, String filePath) throws Exception {
        try (ObjectOutputStream oos = new ObjectOutputStream(new FileOutputStream(filePath))) {
            oos.writeObject(model);
            oos.flush();
        } catch (IOException e) {
            // 捕获并处理文件写入错误
            e.printStackTrace();
            throw new Exception("Error while saving model: " + e.getMessage());
        }
    }

    // 加载模型
    public RandomForest loadModel(String filePath) throws Exception {
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(filePath))) {
            return (RandomForest) ois.readObject();
        } catch (IOException | ClassNotFoundException e) {
            // 捕获并处理文件读取或类转换错误
            e.printStackTrace();
            throw new Exception("Error while loading model: " + e.getMessage());
        }
    }

}

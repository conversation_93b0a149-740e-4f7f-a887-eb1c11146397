<!--------------------------------
-微观繁殖体分析
-1.表层水样
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action> 
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
        <!-- <NButton type="primary" @click="addCoordinate()">
          <i class="i-material-symbols:add mr-4 text-18" />
          添加经纬度
        </NButton> -->
      </div>
    </template>
    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select label-field="name" value-field="id" clearable v-model:value="queryItems.distributeId" filterable
                  :options="stationOption" placeholder="请选择站点" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :rules="formRules" :label-width="120"
              :model="modalForm" :disabled="modalAction === 'view'">
        <n-form-item label="站点" path="distributeId" :rule="{
          required: true,
          message: '请选择站点',
          type:'number',
          trigger: ['change'],
        }">
          <!-- <n-input v-model:value="modalForm.algaeName" /> -->
          <n-select label-field="name" value-field="id" @change="changeSelect" clearable
                    v-model:value="modalForm.distributeId" filterable :options="stationOption" placeholder="请选择站点" />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input disabled v-model:value="coordination.longitude" placeholder="经度"></n-input>
            </n-form-item>
            <n-form-item label="纬度">
              <n-input disabled v-model:value="coordination.latitude" placeholder="纬度"></n-input>
            </n-form-item>
          </div>
        </Transition>
        <n-form-item label="样品类型">
          <n-input disabled v-model:value="displaySampleType" placeholder="样品类型"></n-input>
        </n-form-item>
        <!-- <n-form-item label="丰度" path="abundance" :rule="{
          required: true,
          message: '请输入丰度',
          // trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.abundance">
            <template #suffix>
              ind./50g
            </template>
          </n-input>
        </n-form-item> -->
        <!-- <n-form-item label="样品种类">
          <n-select @change="changeSampleType" v-model:value="displaySampleTypeList" :options="sampleTypeOption"
            label-field="name" value-field="id" clearable filterable multiple />
        </n-form-item>


        <div v-for="item in modalForm.sampleTypeList">
          <n-form-item  :label="`${item.name}生物量`" >
            <n-input-number style="width: 100%;" v-model:value="item.number" :placeholder="`请输入${item.name}生物量`">
              <template #suffix>
                ind./50g
              </template>
            </n-input-number>
          </n-form-item>
        </div> -->

        <n-form-item label="样品种类" path="sampleTypeList" :rule="{
          required: true,
          message: '请选择至少一个样品种类',
          type: 'array',
          trigger: ['input', 'blur']
        }">
          <n-select @change="changeSampleType" v-model:value="displaySampleTypeList" :options="sampleTypeOption"
                    label-field="name" value-field="id" clearable filterable multiple />
        </n-form-item>

        <!--        <div v-for="(item, index) in modalForm.sampleTypeList" :key="item.id">-->
        <!--          <n-form-item :label="`${item.name}生物量`" :path="`sampleTypeList.${index}.number`"-->
        <!--            :rule="getValidationRule(item)">-->
        <!--            <n-input-number style="width: 100%;" v-model:value="item.number" :placeholder="`请输入${item.name}生物量`">-->
        <!--              <template #suffix>-->
        <!--                ind./50g-->
        <!--              </template>-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->
        <!--        </div>-->
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})

const sampleTypeOption = ref([])

const sampleType = ref(3)  //复用改该字段即可

// let sampleTypeList = ref([])

const displaySampleType = computed(() => {
  switch (sampleType.value) {
    case 1:
      return '底层水样微观繁殖体';
    case 2:
      return '表层水样微观繁殖体';
    case 3:
      return '藻样';
    default:
      return '沉积物';
  }
});

const displaySampleTypeList = computed(() => {
  return modalForm.value.sampleTypeList?.map(item => item.id)
});

// 获取验证规则
const getValidationRule = (item) => ({
  required: true,
  message: `请输入${item.name}生物量`,
  type: 'number',
  trigger: ['input', 'blur']
});

const getSampleTypeList = async () => {
  let { data } = await api.getListSampleTypes()
  sampleTypeOption.value = data
}

const getStationList = async () => {
  let { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}

const changeSampleType = (row) => {
  console.log(row);
  // sampleTypeList.value = row
  // modalForm.value.sampleTypeList=row
  modalForm.value.sampleTypeList = row.map(id => {
    const foundOption = sampleTypeOption.value.find(option => option.id === id);
    return foundOption;
  }).filter(Boolean);
}

const changeSelect = async (row) => {
  if (row != null) {
    console.log(row);
    stationOption.value.filter(item => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
        return
      }
    })
  } else {
    coordination.value = {}
  }
}

onMounted(() => {
  queryItems.value.sampleType = sampleType.value
  $table.value?.handleSearch()
  getStationList()
  getSampleTypeList()
  modalForm.value.sampleType = sampleType.value
  // console.log(modalForm.value);

})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit
} = useCrud({
  name: '生物量',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    width: 100,
    title: '站点', ellipsis: { tooltip: true },
    render(row) {
      return h(NTag,
        { type: 'success' },
        { default: () => row.stationPointDistribute.name })
    }
  },
  // {
  //   title: '经度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.stationPointDistribute.longitude)
  //   },
  // },
  // {
  //   title: '纬度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.stationPointDistribute.latitude)
  //   },
  // },
  {
    width: 200,
    title: '样品类型', ellipsis: { tooltip: true },
    render(row) {
      return h(NTag,
        { type: 'primary' },
        { default: () => displaySampleType.value })
    }
  },
  // {
  //   title: '丰度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.abundance + " ind./50g")
  //   },
  // },
  {
    title: '种类',
    // width: 300,
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList)) {
        return h('span', '无数据');
      }
      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        return h(NTag, { key: index, type: 'info' }, { default: () => type.name});
      });

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap);
    },
  },
  {
    width: 230,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            dashed: true,
            // secondary: true,
            onClick: () =>
              router.push({ path: `/ims/morphological-analysis`, query: { abundanceId: row.id, distributeName: row.stationPointDistribute.name } }),
          },
          {
            default: () => '形态分析',
            icon: () => h('i', { class: 'i-fe:trello text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleOpenUpdate(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

const add = () => {
  coordination.value.longitude = null
  coordination.value.latitude = null
  handleOpen({
    action: 'add',
    title: '新增记录',
    // row: { id: row.id, username: row.username, roleIds },
    onOk: async () => {
      // console.log(modalForm.value);
      // console.log(sampleTypeList.value, "种类");
      await modalFormRef.value?.validate()
      await api.create({
        distributeId: modalForm.value.distributeId,
        sampleType: sampleType.value,
        abundance: modalForm.value.abundance,
        sampleTypes: modalForm.value.sampleTypeList
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

function handleOpenUpdate(row) {
  console.log(row);

  coordination.value.longitude = row.stationPointDistribute.longitude
  coordination.value.latitude = row.stationPointDistribute.latitude
  // row.sampleTypeList=row.sampleTypeList.map(item=>item.id)
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row: row,
    onOk: updateSample,
  })
}


const updateSample = async () => {
  await modalFormRef.value?.validate()
  await api.update({
    id: modalForm.value.id,
    distributeId: modalForm.value.distributeId,
    sampleType: sampleType.value,
    abundance: modalForm.value.abundance,
    sampleTypes: modalForm.value.sampleTypeList
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}
// async function handleEnable(row) {
//   row.enableLoading = true
//   try {
//     await api.update({ id: row.id, enable: !row.enable })
//     row.enableLoading = false
//     $message.success('操作成功')
//     $table.value?.handleSearch()
//   }
//   catch (error) {
//     console.error(error)
//     row.enableLoading = false
//   }
// }
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style>

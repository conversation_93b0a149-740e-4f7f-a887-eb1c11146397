package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.MetalIonDto;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.MetalIonRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.MetalIonMapper;
import cn.dhbin.isme.ims.domain.entity.MetalIon;
import cn.dhbin.isme.ims.service.MetalIonService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 金属离子表(MetalIon)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-27 22:29:06
 */
@Service("metalIonService")
public class MetalIonServiceImpl extends ServiceImpl<MetalIonMapper, MetalIon> implements MetalIonService {

    @Autowired
    private MetalIonMapper metalIonMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<MetalIonDto> queryPage(MetalIonRequest request) {
        IPage<MetalIon> qp = request.toPage();
        LambdaQueryWrapper<MetalIon> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(MetalIon::getName, request.getName());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(MetalIon::getDistributeId, request.getDistributeId());
        }

        IPage<MetalIon> ret = metalIonMapper.selectPage(qp, queryWrapper);

        IPage<MetalIonDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            MetalIonDto dataDto = new MetalIonDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public List<MetalIonDto> getByStationId(Integer stationId) {
        // 创建查询条件
        LambdaQueryWrapper<MetalIon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MetalIon::getDistributeId, stationId);

        // 查询数据
        List<MetalIon> list = metalIonMapper.selectList(queryWrapper);

        // 转换为DTO
        return list.stream().map(data -> {
            MetalIonDto dataDto = new MetalIonDto();
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点信息
            if (data.getDistributeId() != null) {
                StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
                dataDto.setStationPointDistribute(stationPointDistribute);
            }

            return dataDto;
        }).collect(Collectors.toList());
    }
}


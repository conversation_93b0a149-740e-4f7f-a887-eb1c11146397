
package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class StationPointScaleExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("名称")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty("经度")
    @ColumnWidth(20)
    private Double longitude;

    @ExcelProperty("纬度")
    @ColumnWidth(20)
    private Double latitude;

    @ExcelProperty("空间范围描述")
    @ColumnWidth(30)
    private String description;

    @ExcelProperty("创建时间")
    @ColumnWidth(25)
    private String createTime;
}
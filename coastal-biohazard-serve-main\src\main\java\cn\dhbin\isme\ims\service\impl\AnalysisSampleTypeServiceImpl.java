package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.AnalysisSampleType;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import cn.dhbin.isme.ims.mapper.AnalysisSampleTypeMapper;
import cn.dhbin.isme.ims.service.AnalysisSampleTypeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Service("AnalysisSampleTypeService")
public class AnalysisSampleTypeServiceImpl extends ServiceImpl<AnalysisSampleTypeMapper, AnalysisSampleType> implements AnalysisSampleTypeService {

    @Autowired
    private AnalysisSampleTypeMapper analysissampleTypeMapper;

    @Override
    public List<AnalysisSampleType> AnalysislistSampleTypes() {
        List<AnalysisSampleType> stationPoints = analysissampleTypeMapper.selectList(null);
        return stationPoints;
    }

    @Override
    public Page<AnalysisSampleType> queryPage(SampleTypeRequest request) {
        return null;
    }


}


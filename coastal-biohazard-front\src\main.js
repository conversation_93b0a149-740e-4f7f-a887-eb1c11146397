/**********************************
 * @Description: 入口文件
 * @FilePath: main.js
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:41:32
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

// import DataV, { setClassNamePrefix } from '@dataview/datav-vue3';
import DataVVue3 from '@kjgl77/datav-vue3'
import { createApp } from 'vue'
import App from './App.vue'
import { setupDirectives } from './directives'
import { setupRouter } from './router'
import { setupStore } from './store'
import { setupNaiveDiscreteApi } from './utils'
import '@/styles/global.scss'
import '@/styles/reset.css'
import 'uno.css'

async function bootstrap() {
  const app = createApp(App)
  app.use(DataVVue3)
  // app.use(DataV, { classNamePrefix: 'dv-' });
  setupStore(app)
  setupDirectives(app)
  await setupRouter(app)

  app.mount('#app')
  setupNaiveDiscreteApi()
}

bootstrap()

package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.mapper.AbundanceSampleMapper;
import cn.dhbin.isme.ims.mapper.SampleTypeMapper;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.AbundanceLayerSpeciesDataMapper;

import cn.dhbin.isme.ims.service.AbundanceLayerSpeciesDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:29
 */
@Service("abundanceLayerSpeciesDataService")
public class AbundanceLayerSpeciesDataServiceImpl extends ServiceImpl<AbundanceLayerSpeciesDataMapper, AbundanceLayerSpeciesData> implements AbundanceLayerSpeciesDataService {

    @Autowired
    private AbundanceLayerSpeciesDataMapper abundanceLayerSpeciesDataMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Autowired
    private AbundanceSampleMapper abundanceSampleMapper;

    @Autowired
    private SampleTypeMapper sampleTypeMapper;

    @Override
    public Page<AbundanceLayerSpeciesDataDto> queryPage(AbundanceLayerSpeciesDataRequest request) {
        IPage<AbundanceLayerSpeciesData> qp = request.toPage();
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getSampleType() != null) {
            queryWrapper.eq(AbundanceLayerSpeciesData::getSampleType, request.getSampleType());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(AbundanceLayerSpeciesData::getDistributeId, request.getDistributeId());
        }

        IPage<AbundanceLayerSpeciesData> ret = abundanceLayerSpeciesDataMapper.selectPage(qp, queryWrapper);

        IPage<AbundanceLayerSpeciesDataDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            AbundanceLayerSpeciesDataDto dataDto = new AbundanceLayerSpeciesDataDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            // 获取与该丰富度相关的所有样本类型ID和数量
            List<SampleInfo> sampleInfos = abundanceSampleMapper.getSampleInfosByAbundanceId(data.getId());

            // 提取 sampleIds 并创建一个 map 来存储 number
            Map<Integer, Double> sampleIdToNumberMap = new HashMap<>();
            List<Integer> sampleIds = sampleInfos.stream()
                    .peek(sampleInfo -> sampleIdToNumberMap.put(sampleInfo.getSampleId(), sampleInfo.getNumber()))
                    .map(SampleInfo::getSampleId)
                    .collect(Collectors.toList());

            // 检查 sampleIds 是否为空
            List<SampleType> sampleTypes = new ArrayList<>();
            if (!sampleIds.isEmpty()) {
                sampleTypes = sampleTypeMapper.selectBatchIds(sampleIds);
            }

            // 将 number 映射到 sampleTypes 中
            for (SampleType sampleType : sampleTypes) {
                sampleType.setNumber(sampleIdToNumberMap.get(sampleType.getId()));
            }

            // 设置到DTO中
            dataDto.setSampleTypeList(sampleTypes);


            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public void addAbundance(Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes) {
        AbundanceLayerSpeciesData data = new AbundanceLayerSpeciesData();
        data.setDistributeId(distributeId);
        data.setSampleType(sampleType);
        data.setAbundance(abundance);
        abundanceLayerSpeciesDataMapper.insert(data);
        Integer abundanceId = data.getId();

        // 检查 sampleTypeIds 是否为空或空列表
        if (sampleTypes == null || sampleTypes.isEmpty()) {
            return;
        }

        // 插入 abundance_sample 表
        List<AbundanceSample> samples = new ArrayList<>();
        for (SampleType s : sampleTypes) {
            AbundanceSample sample = new AbundanceSample();
            sample.setAbundanceId(abundanceId);
            sample.setSampleId(s.getId());
            sample.setNumber(s.getNumber());
            samples.add(sample);
        }

        abundanceLayerSpeciesDataMapper.insertBatch(samples);
    }

//    @Override
//    public void updateAbundance(Integer id, Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes) {
//        // 查询现有的 AbundanceLayerSpeciesData
//        AbundanceLayerSpeciesData existingData = abundanceLayerSpeciesDataMapper.selectById(id);
//
//        if (existingData != null) {
//            // 修改 AbundanceLayerSpeciesData 的数据
//            existingData.setDistributeId(distributeId);
//            existingData.setSampleType(sampleType);
//            existingData.setAbundance(abundance);
//
//            // 更新 AbundanceLayerSpeciesData 记录
//            abundanceLayerSpeciesDataMapper.updateById(existingData);
//
//            Integer abundanceId = existingData.getId();
//
//            // 检查 sampleTypeIds 是否为空或空列表
//            if (sampleTypeIds == null || sampleTypeIds.isEmpty()) {
//                // 清理旧的关联关系
//                abundanceSampleMapper.deleteByAbundanceId(abundanceId);
//                return;
//            }
//
//            // 获取现有的 AbundanceSample 记录
//            List<AbundanceSample> existingSamples = abundanceSampleMapper.selectByAbundanceId(abundanceId);
//
//            // 记录需要删除的 AbundanceSample ID
//            List<Integer> samplesToDelete = new ArrayList<>();
//            for (AbundanceSample sample : existingSamples) {
//                if (!sampleTypeIds.contains(sample.getSampleId())) {
//                    samplesToDelete.add(sample.getId());
//                }
//            }
//
//            // 删除不再存在的 AbundanceSample 记录
//            if (!samplesToDelete.isEmpty()) {
//                abundanceSampleMapper.deleteBatchIds(samplesToDelete);
//            }
//
//            // 插入新的 AbundanceSample 记录
//            List<AbundanceSample> newSamples = new ArrayList<>();
//            for (Integer sampleId : sampleTypeIds) {
//                boolean isNewSample = true;
//                for (AbundanceSample sample : existingSamples) {
//                    if (sample.getSampleId().equals(sampleId)) {
//                        isNewSample = false;
//                        break;
//                    }
//                }
//                if (isNewSample) {
//                    AbundanceSample sample = new AbundanceSample();
//                    sample.setAbundanceId(abundanceId);
//                    sample.setSampleId(sampleId);
//                    newSamples.add(sample);
//                }
//            }
//
//            if (!newSamples.isEmpty()) {
//                abundanceLayerSpeciesDataMapper.insertBatch(newSamples);
//            }
//        }
//    }

    @Override
    public void updateAbundance(Integer id, Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes) {
        // 查询现有的 AbundanceLayerSpeciesData
        AbundanceLayerSpeciesData existingData = abundanceLayerSpeciesDataMapper.selectById(id);

        if (existingData != null) {
            // 修改 AbundanceLayerSpeciesData 的数据
            existingData.setDistributeId(distributeId);
            existingData.setSampleType(sampleType);
            existingData.setAbundance(abundance);

            // 更新 AbundanceLayerSpeciesData 记录
            abundanceLayerSpeciesDataMapper.updateById(existingData);

            Integer abundanceId = existingData.getId();

            if (sampleTypes == null || sampleTypes.isEmpty()) {
                // 清理旧的关联关系
                abundanceSampleMapper.deleteByAbundanceId(abundanceId);
                return;
            }

            // 获取现有的 AbundanceSample 记录
            List<AbundanceSample> existingSamples = abundanceSampleMapper.selectByAbundanceId(abundanceId);

            // 提取 sampleTypeIds 和 numbers
            List<Integer> sampleTypeIds = sampleTypes.stream().map(SampleType::getId).toList();
            Map<Integer, Double> sampleNumberMap = sampleTypes.stream().collect(Collectors.toMap(SampleType::getId, SampleType::getNumber));

            // 记录需要删除的 AbundanceSample ID
            List<Integer> samplesToDelete = new ArrayList<>();
            // 记录需要更新的 AbundanceSample
            List<AbundanceSample> samplesToUpdate = new ArrayList<>();
            // 记录需要新增的 AbundanceSample
            List<AbundanceSample> newSamples = new ArrayList<>();

            // 处理现有样本
            for (AbundanceSample existingSample : existingSamples) {
                Integer existingSampleId = existingSample.getSampleId();
                if (sampleTypeIds.contains(existingSampleId)) {
                    // 更新 number 字段
                    existingSample.setNumber(sampleNumberMap.get(existingSampleId));
                    samplesToUpdate.add(existingSample);
                } else {
                    // 标记为删除
                    samplesToDelete.add(existingSample.getId());
                }
            }

            // 处理新增样本
            for (SampleType currentSampleType : sampleTypes) {
                Integer sampleId = currentSampleType.getId();
                boolean isNewSample = true;
                for (AbundanceSample existingSample : existingSamples) {
                    if (existingSample.getSampleId().equals(sampleId)) {
                        isNewSample = false;
                        break;
                    }
                }
                if (isNewSample) {
                    AbundanceSample sample = new AbundanceSample();
                    sample.setAbundanceId(abundanceId);
                    sample.setSampleId(sampleId);
                    sample.setNumber(currentSampleType.getNumber());
                    newSamples.add(sample);
                }
            }

            // 执行删除操作
            if (!samplesToDelete.isEmpty()) {
                abundanceSampleMapper.deleteBatchIds(samplesToDelete);
            }

            // 执行更新操作
            if (!samplesToUpdate.isEmpty()) {
                for (AbundanceSample sample : samplesToUpdate) {
                    abundanceSampleMapper.updateById(sample);
                }
            }

            // 执行新增操作
            if (!newSamples.isEmpty()) {
                abundanceLayerSpeciesDataMapper.insertBatch(newSamples);
            }
        }
    }
    @Override
    public List<AbundanceLayerSpeciesDataDto> queryList(Integer distributeId) {
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();


        if (distributeId != null) {
            queryWrapper.eq(AbundanceLayerSpeciesData::getDistributeId, distributeId);
        }

        List<AbundanceLayerSpeciesData> resultList = abundanceLayerSpeciesDataMapper.selectList(queryWrapper);

        List<AbundanceLayerSpeciesDataDto> dtoList = resultList.stream().map(data -> {
            // 初始化目标对象
            AbundanceLayerSpeciesDataDto dataDto = new AbundanceLayerSpeciesDataDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            // 获取与该丰富度相关的所有样本类型ID和数量
            List<SampleInfo> sampleInfos = abundanceSampleMapper.getSampleInfosByAbundanceId(data.getId());

            // 提取 sampleIds 并创建一个 map 来存储 number
            Map<Integer, Double> sampleIdToNumberMap = new HashMap<>();
            List<Integer> sampleIds = sampleInfos.stream()
                    .peek(sampleInfo -> sampleIdToNumberMap.put(sampleInfo.getSampleId(), sampleInfo.getNumber()))
                    .map(SampleInfo::getSampleId)
                    .collect(Collectors.toList());

            // 检查 sampleIds 是否为空
            List<SampleType> sampleTypes = new ArrayList<>();
            if (!sampleIds.isEmpty()) {
                sampleTypes = sampleTypeMapper.selectBatchIds(sampleIds);
            }

            // 将 number 映射到 sampleTypes 中
            for (SampleType sampleType : sampleTypes) {
                sampleType.setNumber(sampleIdToNumberMap.get(sampleType.getId()));
            }


            // 设置到DTO中
            dataDto.setSampleTypeList(sampleTypes);


            return dataDto;
        }).collect(Collectors.toList());

        return dtoList;
    }


}


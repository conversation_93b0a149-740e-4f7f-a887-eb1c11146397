package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 微观繁殖体-种类中间表(AbundanceSample)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:11:41
 */
@Data
@TableName("abundance_sample")
public class AbundanceSample implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 微观繁殖体id
     **/
    private Integer abundanceId;

    /**
     * 种类id
     **/
    private Integer sampleId;

    private Double number;

public Serializable pkVal() {
          return null;
      }
}



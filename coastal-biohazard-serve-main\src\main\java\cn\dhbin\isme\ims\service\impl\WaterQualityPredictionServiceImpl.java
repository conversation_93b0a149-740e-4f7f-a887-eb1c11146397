package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.dto.WaterQualityPredictionDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.mapper.WaterQualityPredictionMapper;
import cn.dhbin.isme.ims.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 水质预测服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WaterQualityPredictionServiceImpl extends ServiceImpl<WaterQualityPredictionMapper, WaterQualityPrediction> 
        implements WaterQualityPredictionService {

    private final StationPointDistributeService stationPointDistributeService;
    private final WaterPhWeatherDataService waterPhWeatherDataService;
    private final ChemicalIonService chemicalIonService;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    public WaterQualityPredictionDto predictWaterQuality(BigDecimal longitude, BigDecimal latitude, 
                                                        Integer scaleId, Integer taskId, Integer timesId) {
        
        // 1. 获取附近站点的历史数据
        List<StationPointDistribute> nearbyStations = getNearbyStations(longitude, latitude, scaleId, taskId, timesId);
        
        // 2. 执行预测算法
        WaterQualityPredictionDto prediction = performPrediction(longitude, latitude, nearbyStations);
        
        // 3. 设置关联信息
        prediction.setScaleId(scaleId);
        prediction.setTaskId(taskId);
        prediction.setTimesId(timesId);
        prediction.setPredictionTime(LocalDateTime.now());
        prediction.setModelVersion("IDW-v1.0");
        
        // 4. 保存预测记录
        savePredictionRecord(prediction);
        
        return prediction;
    }

    @Override
    public SseEmitter getAnalysisStream(BigDecimal longitude, BigDecimal latitude,
                                      Integer scaleId, Integer taskId, Integer timesId) {
        
        SseEmitter emitter = new SseEmitter(30000L); // 30秒超时
        
        CompletableFuture.runAsync(() -> {
            try {
                // 获取预测结果用于分析
                WaterQualityPredictionDto prediction = predictWaterQuality(longitude, latitude, scaleId, taskId, timesId);
                
                // 模拟AI分析过程，分段发送分析结果
                String[] analysisSegments = generateAnalysisSegments(prediction);
                
                for (String segment : analysisSegments) {
                    try {
                        emitter.send(SseEmitter.event()
                                .name("analysis")
                                .data(segment));
                        
                        // 模拟分析处理时间
                        Thread.sleep(200 + (int)(Math.random() * 300));
                    } catch (IllegalStateException e) {
                        // SSE连接已关闭
                        break;
                    }
                }
                
                emitter.complete();
                
            } catch (Exception e) {
                log.error("SSE分析流处理异常", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("分析过程出现异常: " + e.getMessage()));
                } catch (IOException ioException) {
                    log.error("发送错误信息失败", ioException);
                }
                emitter.completeWithError(e);
            }
        }, executorService);
        
        emitter.onCompletion(() -> log.info("SSE连接正常关闭"));
        emitter.onTimeout(() -> log.warn("SSE连接超时"));
        emitter.onError(throwable -> log.error("SSE连接异常", throwable));
        
        return emitter;
    }

    @Override
    public List<WaterQualityPredictionDto> getByStationId(Integer stationId, BigDecimal longitude, BigDecimal latitude) {
        try {
            log.info("获取站点ID: {} 的水质预测数据", stationId);
            
            // 获取站点信息
            StationPointDistribute station = stationPointDistributeService.getById(stationId);
            if (station == null) {
                log.warn("站点ID: {} 不存在", stationId);
                return Collections.emptyList();
            }
            
            // 如果没有提供经纬度，使用站点的坐标
            BigDecimal predictionLongitude = longitude != null ? longitude : new BigDecimal(station.getLongitude().toString());
            BigDecimal predictionLatitude = latitude != null ? latitude : new BigDecimal(station.getLatitude().toString());
            
            // 查询该站点的历史预测记录
            LambdaQueryWrapper<WaterQualityPrediction> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WaterQualityPrediction::getScaleId, station.getScaleId())
                       .orderByDesc(WaterQualityPrediction::getCreateTime)
                       .last("LIMIT 5"); // 最多返回5条历史记录
            
            List<WaterQualityPrediction> predictions = list(queryWrapper);
            
            if (predictions.isEmpty()) {
                // 如果没有历史预测记录，生成一个新的预测
                WaterQualityPredictionDto newPrediction = predictWaterQuality(
                    predictionLongitude, predictionLatitude, 
                    station.getScaleId(), station.getTaskId(), station.getTimesId()
                );
                return Collections.singletonList(newPrediction);
            }
            
            // 转换为DTO
            List<WaterQualityPredictionDto> result = predictions.stream().map(prediction -> {
                WaterQualityPredictionDto dto = new WaterQualityPredictionDto();
                BeanUtils.copyProperties(prediction, dto);
                return dto;
            }).collect(Collectors.toList());
            
            log.info("成功获取站点ID: {} 的水质预测数据，共{}条记录", stationId, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取站点ID: {} 的水质预测数据失败", stationId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取附近站点
     */
    private List<StationPointDistribute> getNearbyStations(BigDecimal longitude, BigDecimal latitude, 
                                                          Integer scaleId, Integer taskId, Integer timesId) {
        LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationPointDistribute::getScaleId, scaleId);
        
        if (taskId != null) {
            queryWrapper.eq(StationPointDistribute::getTaskId, taskId);
        }
        if (timesId != null) {
            queryWrapper.eq(StationPointDistribute::getTimesId, timesId);
        }
        
        return stationPointDistributeService.list(queryWrapper);
    }

    /**
     * 执行预测算法（反距离权重插值法）
     */
    private WaterQualityPredictionDto performPrediction(BigDecimal longitude, BigDecimal latitude, 
                                                       List<StationPointDistribute> stations) {
        
        WaterQualityPredictionDto prediction = new WaterQualityPredictionDto();
        prediction.setLongitude(longitude);
        prediction.setLatitude(latitude);
        
        if (stations.isEmpty()) {
            // 如果没有历史数据，使用默认值
            setDefaultPredictionValues(prediction);
            prediction.setConfidenceScore(new BigDecimal("0.3"));
            return prediction;
        }
        
        // 收集有效的水质数据
        double totalWeight = 0.0;
        double weightedSalt = 0.0;
        double weightedPh = 0.0;
        double weightedTemp = 0.0;
        double weightedTransparency = 0.0;
        int validStationCount = 0;
        
        for (StationPointDistribute station : stations) {
            // 计算距离权重
            double distance = calculateDistance(longitude.doubleValue(), latitude.doubleValue(),
                                              station.getLongitude().doubleValue(), station.getLatitude().doubleValue());
            
            if (distance < 0.001) distance = 0.001; // 避免除零
            double weight = 1.0 / Math.pow(distance, 2); // 反距离平方权重
            
            // 获取该站点的水质数据
            WaterQualityData waterQuality = getStationWaterQuality(station.getId());
            if (waterQuality != null) {
                weightedSalt += waterQuality.saltExtent * weight;
                weightedPh += waterQuality.phExtent * weight;
                weightedTemp += waterQuality.waterTemperature * weight;
                weightedTransparency += waterQuality.transparentExtent * weight;
                totalWeight += weight;
                validStationCount++;
            }
        }
        
        if (totalWeight > 0 && validStationCount > 0) {
            // 计算加权平均值
            prediction.setSaltExtent(new BigDecimal(weightedSalt / totalWeight).setScale(2, RoundingMode.HALF_UP));
            prediction.setPhExtent(new BigDecimal(weightedPh / totalWeight).setScale(2, RoundingMode.HALF_UP));
            prediction.setWaterTemperature(new BigDecimal(weightedTemp / totalWeight).setScale(2, RoundingMode.HALF_UP));
            prediction.setTransparentExtent(new BigDecimal(weightedTransparency / totalWeight).setScale(2, RoundingMode.HALF_UP));
            
            // 计算置信度（基于站点数量和距离分布）
            double confidence = Math.min(0.95, 0.5 + (validStationCount * 0.1));
            prediction.setConfidenceScore(new BigDecimal(confidence).setScale(4, RoundingMode.HALF_UP));
        } else {
            setDefaultPredictionValues(prediction);
            prediction.setConfidenceScore(new BigDecimal("0.3"));
        }
        
        return prediction;
    }

    /**
     * 设置默认预测值
     */
    private void setDefaultPredictionValues(WaterQualityPredictionDto prediction) {
        prediction.setSaltExtent(new BigDecimal("32.5"));
        prediction.setPhExtent(new BigDecimal("8.1"));
        prediction.setWaterTemperature(new BigDecimal("15.5"));
        prediction.setTransparentExtent(new BigDecimal("2.5"));
    }

    /**
     * 计算两点间的距离（简化的欧氏距离）
     */
    private double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        double deltaLon = lon1 - lon2;
        double deltaLat = lat1 - lat2;
        return Math.sqrt(deltaLon * deltaLon + deltaLat * deltaLat);
    }

    /**
     * 获取站点水质数据
     */
    private WaterQualityData getStationWaterQuality(Integer stationId) {
        // 从水文气象数据获取基础参数
        LambdaQueryWrapper<WaterPhWeatherData> weatherQuery = new LambdaQueryWrapper<>();
        weatherQuery.eq(WaterPhWeatherData::getDistributeId, stationId).last("LIMIT 1");
        WaterPhWeatherData weatherData = waterPhWeatherDataService.getOne(weatherQuery);
        
        if (weatherData != null) {
            WaterQualityData data = new WaterQualityData();
            data.saltExtent = weatherData.getSaltExtent() != null ? weatherData.getSaltExtent().doubleValue() : 32.0;
            data.phExtent = weatherData.getPhExtent() != null ? weatherData.getPhExtent().doubleValue() : 8.0;
            data.waterTemperature = weatherData.getWaterTemperature() != null ? weatherData.getWaterTemperature().doubleValue() : 15.0;
            data.transparentExtent = weatherData.getTransparentExtent() != null ? weatherData.getTransparentExtent().doubleValue() : 2.0;
            return data;
        }
        
        return null;
    }

    /**
     * 保存预测记录
     */
    private void savePredictionRecord(WaterQualityPredictionDto prediction) {
        WaterQualityPrediction record = new WaterQualityPrediction();
        BeanUtils.copyProperties(prediction, record);
        record.setCreateTime(LocalDateTime.now());
        save(record);
    }

    /**
     * 生成分析文本段落
     */
    private String[] generateAnalysisSegments(WaterQualityPredictionDto prediction) {
        return new String[] {
            "正在分析预测点位的水文环境特征...\n\n",
            String.format("📍 预测位置：经度 %.6f°，纬度 %.6f°\n", 
                         prediction.getLongitude(), prediction.getLatitude()),
            "🔍 基于反距离权重插值算法进行水质参数预测...\n\n",
            "📊 **水质预测结果分析**\n\n",
            String.format("• **盐度**: %.2f PSU\n", prediction.getSaltExtent()),
            "  - 数值处于正常海水盐度范围内，表明该区域水体盐度分布稳定\n",
            String.format("• **pH值**: %.2f\n", prediction.getPhExtent()),
            "  - pH值显示水体呈弱碱性，符合典型海洋环境特征\n",
            String.format("• **水温**: %.2f°C\n", prediction.getWaterTemperature()),
            "  - 水温适中，有利于海洋生物的正常生长和繁殖\n",
            String.format("• **透明度**: %.2f米\n", prediction.getTransparentExtent()),
            "  - 透明度反映了水体的清澈程度和悬浮物含量\n\n",
            "🎯 **预测置信度分析**\n\n",
            String.format("置信度: %.1f%%\n", prediction.getConfidenceScore().multiply(new BigDecimal("100"))),
            "预测精度较高，建议可作为调查规划的参考依据。\n\n",
            "⚠️ **注意事项**\n\n",
            "• 预测结果基于历史数据和空间插值算法\n",
            "• 实际水质状况可能受天气、潮汐等因素影响\n",
            "• 建议结合现场实测数据进行综合判断\n\n",
            "✅ 分析完成。预测结果已保存至数据库供后续查询使用。"
        };
    }

    /**
     * 水质数据内部类
     */
    private static class WaterQualityData {
        double saltExtent;
        double phExtent;
        double waterTemperature;
        double transparentExtent;
    }
} 
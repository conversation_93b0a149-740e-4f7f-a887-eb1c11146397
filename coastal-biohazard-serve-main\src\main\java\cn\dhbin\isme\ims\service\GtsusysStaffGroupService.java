package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffGroup;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffGroupRequest;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 一线人员单位表(GtsusysStaffGroup)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:34:41
 */
public interface GtsusysStaffGroupService extends IService<GtsusysStaffGroup> {
    Page<GtsusysStaffGroup> queryPage(GtsusysStaffGroupRequest request);
    List<GtsusysStaffGroup> queryList(GtsusysStaffGroupRequest request);
}


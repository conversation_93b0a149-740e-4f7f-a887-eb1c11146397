<template>
  <AppPage show-footer>
    <div class="flex">
      <n-card title="✨ 生物量分析图"  class="min-w-200 w-50% h-350">
        <v-chart :option="chartOptions" autoresize />
      </n-card>
    </div>
  </AppPage>
</template>

<script setup>
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import { GridComponent, LegendComponent, TooltipComponent } from 'echarts/components'
import * as echarts from 'echarts/core'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import VChart from 'vue-echarts'

// 使用 ECharts 的功能
echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
  PieChart,
])

const algaeData = ref([
  { name: '绿藻', biomass: 200, ratio: 25 },
  { name: '硅藻', biomass: 300, ratio: 37.5 },
  { name: '蓝藻', biomass: 100, ratio: 12.5 },
  { name: '红藻', biomass: 150, ratio: 18.75 },
  { name: '褐藻', biomass: 250, ratio: 31.25 }
])

const chartOptions = ref({
  backgroundColor: '#f5f5f5', // 背景颜色
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: 'rgba(255, 255, 255, 0.8)'
      }
    },
    formatter: function (params) {
      let result = '';
      params.forEach(function (item) {
        if (item.seriesName === '生物量') {
          console.log(item.color);

          result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:#5793f3;"></span>`;
          result += `${item.seriesName}: ${item.value} mg<br/>`;
        } else if (item.seriesName === '比例') {
          result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${item.color};"></span>`;
          result += `${item.seriesName}: ${item.value}%<br/>`;
        }
      });
      return result;
    }
  },
  legend: {
    data: ['生物量', '比例'],
    textStyle: {
      color: '#666666', // 文字颜色
      fontSize: 14 // 字体大小
    }
  },
  xAxis: {
    type: 'category',
    // boundaryGap: false,
    data: algaeData.value.map(item => item.name),
    axisLine: {
      lineStyle: {
        color: '#666666' // 轴线颜色
      }
    },
    axisTick: {
      alignWithLabel: true
    },
    axisLabel: {
      color: '#666666', // 标签文字颜色
      fontSize: 12 // 标签文字大小
    }
  },
  yAxis: [
    {
      type: 'value',
      name: '生物量 (mg)',
      position: 'left',
      axisLabel: {
        color: '#666666',
        fontSize: 12,
        formatter: '{value} mg'
      },
      splitLine: {
        lineStyle: {
          color: '#e0e0e0' // 分割线颜色
        }
      },
      axisLine: {
        lineStyle: {
          color: '#666666' // 轴线颜色
        }
      }
    },
    {
      type: 'value',
      name: '比例 (%)',
      position: 'right',
      min: 0,
      max: 100,
      interval: 25,
      axisLabel: {
        color: '#666666',
        fontSize: 12,
        formatter: '{value}%' // 百分比格式化
      },
      splitLine: {
        lineStyle: {
          color: '#e0e0e0' // 分割线颜色
        }
      },
      axisLine: {
        lineStyle: {
          color: '#666666' // 轴线颜色
        }
      }
    }
  ],
  series: [
    {
      name: '生物量',
      type: 'bar',
      barWidth: '40%',
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              { offset: 0, color: '#5793f3' },
              { offset: 1, color: '#1890ff' }
            ],
            false
          ),
          barBorderRadius: 5
        }
      },
      data: algaeData.value.map(item => item.biomass),
      yAxisIndex: 0
    },
    {
      name: '比例',
      type: 'line',
      symbol: 'circle',
      symbolSize: 10,
      lineStyle: {
        color: '#eb2f96' // 线条颜色
      },
      itemStyle: {
        color: '#eb2f96' // 数据点颜色
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(
          0, 0, 0, 1,
          [
            { offset: 0, color: 'rgba(235, 47, 150, 0.3)' },
            { offset: 1, color: 'rgba(235, 47, 150, 0)' }
          ]
        )
      },
      data: algaeData.value.map(item => ({
        value: item.ratio,
        name: `${item.ratio}%` // 显示带百分号的名字
      })),
      yAxisIndex: 1
    }
  ]
});
</script>

<style lang="scss" scoped></style>
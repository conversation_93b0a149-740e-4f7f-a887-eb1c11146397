import { request } from '@/utils'

export default {
  create: data => request.post('/abundance', data),
  read: (params = {}) => request.get('/abundance', { params }),
  update: data => request.patch(`/abundance`, data),
  delete: id => request.delete(`/abundance/${id}`),
  getListStationPoints: scaleId => request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  getStationPoint: distributeId => request.get(`/station-point-distribute/${distributeId}`),
  getListSampleTypes: () => request.get('/sample-type/list'),
  uploadImg: file => request.post('/upload/img', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  readMorphological: (params = {}) => request.get('/morphological-analysis-data', { params }),
  updateMorphological: data => request.patch(`/morphological-analysis-data`, data),
  deleteMorphological: id => request.delete(`/morphological-analysis-data/${id}`),
  createMorphological: data => request.post(`/morphological-analysis-data`, data),

  createSampleType: data => request.post('/sample-type', data),
  readSampleType: (params = {}) => request.get('/sample-type', { params }),
  updateSampleType: data => request.patch(`/sample-type`, data),
  deleteSampleType: id => request.delete(`/sample-type/${id}`),

  // 导出表层水样记录
  exportSurfaceWaterSample: (params = {}) => request.get('/surface-water/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入表层水样记录
  importSurfaceWaterSample: data => request.post('/surface-water/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出底层水样微观繁殖体记录
  exportBottomWaterSample: (params = {}) => request.get('/bottom-water/export', {
    params: {
      ...params,
      sampleType: 1, // 底层水样类型为1
    },
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入底层水样微观繁殖体记录
  importBottomWaterSample: data => request.post('/bottom-water/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出沉积物微观繁殖体记录
  exportSedimentMicroorganism: (params = {}) => request.get('/sediment/export', {
    params: {
      ...params,
      sampleType: 3, // 沉积物类型为3
    },
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入沉积物微观繁殖体记录
  importSedimentMicroorganism: data => request.post('/sediment/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
}

package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.ims.domain.entity.SurveyRouteTask;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 调查航线/任务服务接口
 */
public interface SurveyRouteTaskService extends IService<SurveyRouteTask> {
    
    /**
     * 根据调查中心ID获取航线列表
     * @param scaleId 调查中心ID
     * @return 航线列表
     */
    List<SurveyRouteTask> getRoutesByScaleId(Integer scaleId);
} 
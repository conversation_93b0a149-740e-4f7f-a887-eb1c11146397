package cn.dhbin.isme.ims.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldDescription {
    private String fieldName; // 字段名称
    private boolean isFluctuated; // 是否波动明显
    private double maxValue; // 最大值
    private double minValue; // 最小值
    private double averageValue; // 平均值
    private String maxStationName; // 最大值对应的站点名称
    private String minStationName; // 最小值对应的站点名称
}

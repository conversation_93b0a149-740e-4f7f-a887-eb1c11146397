package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.GtsusysStaffManage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 一线作业人员表(GtsusysStaffManage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
@Mapper
public interface GtsusysStaffManageMapper extends BaseMapper<GtsusysStaffManage> {
    @Select("SELECT id FROM gtsusys_staff_group WHERE name = #{groupName}")
    Integer getGroupId(@Param("groupName") String groupName);
}


package cn.dhbin.isme.ims.domain.entity;

import weka.core.Attribute;
import weka.core.DenseInstance;
import weka.core.Instance;
import weka.core.Instances;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class DataPreparer {

    public static Instances createInstances(LocalDate[] dates) {
        // 定义属性列表
        ArrayList<Attribute> attributes = new ArrayList<>();

        // ID
        attributes.add(new Attribute("ID"));

        // Date (作为字符串处理)
        attributes.add(new Attribute("Date", (List<String>) null));

        // Location (字符串)
        List<String> locations = new ArrayList<>();
        locations.add("青岛海域"); // 添加名义值列表
        locations.add("日照海域");
        attributes.add(new Attribute("Location", locations));

        // 其他数值属性
        attributes.add(new Attribute("AlgaeDensity"));
        attributes.add(new Attribute("WaterTemperature"));
        attributes.add(new Attribute("Salinity"));
        attributes.add(new Attribute("PhValue"));
        attributes.add(new Attribute("WindSpeed"));
        attributes.add(new Attribute("Rainfall"));
        attributes.add(new Attribute("LightIntensity"));

        // RiskLevel (名义值字符串)
        List<String> riskLevels = new ArrayList<>();
        riskLevels.add("low");
        riskLevels.add("medium");
        riskLevels.add("high");
        attributes.add(new Attribute("RiskLevel", riskLevels));

        // 创建 Instances 对象
        Instances data = new Instances("TestDataset", attributes, dates.length);
        data.setClassIndex(data.numAttributes() - 1); // 设置类属性索引

        // 创建新的 Instance 对象并填充数据
        for (int i = 0; i < dates.length; i++) {
            // 创建一个包含空值的 Instance
            Instance instance = new DenseInstance(data.numAttributes());

            // 设置 ID
            instance.setValue(attributes.get(0), i + 1);

            // 设置日期
            instance.setValue(attributes.get(1), dates[i].toString());

            // 设置位置 (这里只是示例，需根据实际逻辑调整)
            instance.setValue(attributes.get(2), "青岛海域");

            // 设置其他数值字段 (示例值，可以替换为动态数据)
            instance.setValue(attributes.get(3), 0.04); // AlgaeDensity
            instance.setValue(attributes.get(4), 16.2); // WaterTemperature
            instance.setValue(attributes.get(5), 0.3846153846153844); // Salinity
            instance.setValue(attributes.get(6), 8.3); // PhValue
            instance.setValue(attributes.get(7), 4.8); // WindSpeed
            instance.setValue(attributes.get(8), 0.0); // Rainfall
            instance.setValue(attributes.get(9), 580.0); // LightIntensity

            // 通过名义值的索引设置风险等级，而不是直接设置字符串
            int riskLevelIndex = riskLevels.indexOf("low"); // 使用名义值的索引
            instance.setValue(attributes.get(10), riskLevelIndex);

            // 将实例添加到数据集中
            data.add(instance);
        }

        return data;
    }

    public static void main(String[] args) {
        LocalDate[] dates = {
                LocalDate.of(2024, 10, 1),
                LocalDate.of(2024, 10, 2),
                LocalDate.of(2024, 10, 3)
        };

        Instances data = createInstances(dates);
        System.out.println(data);
    }
}
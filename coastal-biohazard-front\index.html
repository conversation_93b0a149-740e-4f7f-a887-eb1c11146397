<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/favicon.png" />
    <title>%VITE_TITLE%</title>
    <style>
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
      }
      .dark .loading-container {
        background-color: #232324;
        color: rgba(255, 255, 255, 0.9);
      }

      .loading-container .loading {
        --speed-of-animation: 0.9s;
        --gap: 12px;
        --first-color: #4c86f9;
        --second-color: #49a84c;
        --third-color: #f6bb02;
        --fourth-color: #26a69a;
        --fifth-color: #2196f3;

        margin: auto;
        width: 160px;
        height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--gap);
      }

      .loading-container .loading span {
        width: 6px;
        height: 80px;
        background: var(--first-color);
        animation: scale var(--speed-of-animation) ease-in-out infinite;
      }

      .loading-container .loading span:nth-child(2) {
        background: var(--second-color);
        animation-delay: -0.8s;
      }

      .loading-container .loading span:nth-child(3) {
        background: var(--third-color);
        animation-delay: -0.7s;
      }

      .loading-container .loading span:nth-child(4) {
        background: var(--fourth-color);
        animation-delay: -0.6s;
      }

      .loading-container .loading span:nth-child(5) {
        background: var(--fifth-color);
        animation-delay: -0.5s;
      }

      @keyframes scale {
        0%,
        40%,
        100% {
          transform: scaleY(0.25);
        }

        20% {
          transform: scaleY(1);
        }
      }
    </style>
  </head>
  <body class="dark:text-#e9e9e9 auto-bg">
    <div id="app">
      <div class="loading-container">
        <div class="loading">
          <span></span>
          <span></span>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>

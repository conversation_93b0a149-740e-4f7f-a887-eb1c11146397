package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.MapDataAggregationDto;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.domain.entity.SurveyRouteTask;
import cn.dhbin.isme.ims.domain.entity.SurveyTimes;
import cn.dhbin.isme.ims.service.MapDataAggregationService;
import cn.dhbin.isme.ims.service.StationPointScaleService;
import cn.dhbin.isme.ims.service.SurveyRouteTaskService;
import cn.dhbin.isme.ims.service.SurveyTimesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 站点地图分布控制器
 * 提供三级级联查询和地图数据聚合功能
 */
@Slf4j
@RestController
@RequestMapping("/station-map-distribution")
@RequiredArgsConstructor
public class StationMapDistributionController {

    private final StationPointScaleService stationPointScaleService;
    private final SurveyRouteTaskService surveyRouteTaskService;
    private final SurveyTimesService surveyTimesService;
    private final MapDataAggregationService mapDataAggregationService;

    /**
     * 获取调查中心列表（级联第一级）
     * @return 调查中心列表
     */
    @GetMapping("/scales")
    public R<List<StationPointScale>> getScaleList() {
        try {
            List<StationPointScale> scales = stationPointScaleService.list();
            log.info("获取调查中心列表成功，数量: {}", scales.size());
            return R.ok(scales);
        } catch (Exception e) {
            log.error("获取调查中心列表失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "获取调查中心列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据调查中心ID获取航线列表（级联第二级）
     * @param scaleId 调查中心ID
     * @return 航线列表
     */
    @GetMapping("/routes/by-scale/{scaleId}")
    public R<List<SurveyRouteTask>> getRoutesByScaleId(@PathVariable Integer scaleId) {
        try {
            List<SurveyRouteTask> routes = surveyRouteTaskService.getRoutesByScaleId(scaleId);
            log.info("获取航线列表成功，调查中心ID: {}, 数量: {}", scaleId, routes.size());
            return R.ok(routes);
        } catch (Exception e) {
            log.error("获取航线列表失败，调查中心ID: {}", scaleId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "获取航线列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据任务ID获取调查次数列表（级联第三级）
     * @param taskId 任务ID
     * @return 调查次数列表
     */
    @GetMapping("/survey-times/by-task/{taskId}")
    public R<List<SurveyTimes>> getSurveyTimesByTaskId(@PathVariable Integer taskId) {
        try {
            List<SurveyTimes> surveyTimes = surveyTimesService.getSurveyTimesByTaskId(taskId);
            log.info("获取调查次数列表成功，任务ID: {}, 数量: {}", taskId, surveyTimes.size());
            return R.ok(surveyTimes);
        } catch (Exception e) {
            log.error("获取调查次数列表失败，任务ID: {}", taskId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "获取调查次数列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取地图数据聚合信息（支持三级筛选）
     * @param scaleId 调查中心ID（必填）
     * @param taskId 任务ID（可选）
     * @param timesId 调查次数ID（可选）
     * @return 地图数据聚合DTO
     */
    @GetMapping("/map-data")
    public R<MapDataAggregationDto> getPointDistributes(
            @RequestParam Integer scaleId,
            @RequestParam(required = false) Integer taskId,
            @RequestParam(required = false) Integer timesId) {
        
        try {
            MapDataAggregationDto mapData = mapDataAggregationService.getMapData(scaleId, taskId, timesId);
            
            log.info("获取地图数据成功，调查中心ID: {}, 任务ID: {}, 调查次数ID: {}, 站点数量: {}", 
                    scaleId, taskId, timesId, 
                    mapData.getStationPointDistribute() != null ? mapData.getStationPointDistribute().size() : 0);
            
            return R.ok(mapData);
        } catch (Exception e) {
            log.error("获取地图数据失败，调查中心ID: {}, 任务ID: {}, 调查次数ID: {}", 
                     scaleId, taskId, timesId, e);
                         return R.build(new BizException(BizResponseCode.ERR_11099, "获取地图数据失败: " + e.getMessage()));
        }
    }

    /**
     * 创建新的航线任务
     * @param surveyRouteTask 航线任务信息
     * @return 创建结果
     */
    @PostMapping("/routes")
    public R<Void> createRouteTask(@RequestBody SurveyRouteTask surveyRouteTask) {
        try {
            surveyRouteTaskService.save(surveyRouteTask);
            log.info("创建航线任务成功，任务名称: {}", surveyRouteTask.getName());
            return R.ok();
        } catch (Exception e) {
            log.error("创建航线任务失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "创建航线任务失败: " + e.getMessage()));
        }
    }

    /**
     * 创建新的调查次数
     * @param surveyTimes 调查次数信息
     * @return 创建结果
     */
    @PostMapping("/survey-times")
    public R<Void> createSurveyTimes(@RequestBody SurveyTimes surveyTimes) {
        try {
            surveyTimesService.save(surveyTimes);
            log.info("创建调查次数成功，任务ID: {}, 次数: {}", surveyTimes.getTaskId(), surveyTimes.getTimes());
            return R.ok();
        } catch (Exception e) {
            log.error("创建调查次数失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "创建调查次数失败: " + e.getMessage()));
        }
    }

    /**
     * 更新航线任务
     * @param routeTask 航线任务信息
     * @return 更新结果
     */
    @PutMapping("/routes")
    public R<Void> updateRouteTask(@RequestBody SurveyRouteTask routeTask) {
        try {
            surveyRouteTaskService.updateById(routeTask);
            log.info("更新航线任务成功，任务ID: {}", routeTask.getId());
            return R.ok();
        } catch (Exception e) {
            log.error("更新航线任务失败，任务ID: {}", routeTask.getId(), e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "更新航线任务失败: " + e.getMessage()));
        }
    }

    /**
     * 更新调查次数
     * @param surveyTimes 调查次数信息
     * @return 更新结果
     */
    @PutMapping("/survey-times")
    public R<Void> updateSurveyTimes(@RequestBody SurveyTimes surveyTimes) {
        try {
            surveyTimesService.updateById(surveyTimes);
            log.info("更新调查次数成功，ID: {}", surveyTimes.getId());
            return R.ok();
        } catch (Exception e) {
            log.error("更新调查次数失败，ID: {}", surveyTimes.getId(), e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "更新调查次数失败: " + e.getMessage()));
        }
    }

    /**
     * 删除航线任务
     * @param taskId 任务ID
     * @return 删除结果
     */
    @DeleteMapping("/routes/{taskId}")
    public R<Void> deleteRouteTask(@PathVariable Integer taskId) {
        try {
            surveyRouteTaskService.removeById(taskId);
            log.info("删除航线任务成功，任务ID: {}", taskId);
            return R.ok();
        } catch (Exception e) {
            log.error("删除航线任务失败，任务ID: {}", taskId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "删除航线任务失败: " + e.getMessage()));
        }
    }

    /**
     * 删除调查次数
     * @param timesId 调查次数ID
     * @return 删除结果
     */
    @DeleteMapping("/survey-times/{timesId}")
    public R<Void> deleteSurveyTimes(@PathVariable Integer timesId) {
        try {
            surveyTimesService.removeById(timesId);
            log.info("删除调查次数成功，ID: {}", timesId);
            return R.ok();
        } catch (Exception e) {
            log.error("删除调查次数失败，ID: {}", timesId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "删除调查次数失败: " + e.getMessage()));
        }
    }
} 
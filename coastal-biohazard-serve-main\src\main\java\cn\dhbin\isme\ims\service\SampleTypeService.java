package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.SelectOptionDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 样品种类表(SampleType)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:37:24
 */
public interface SampleTypeService extends IService<SampleType> {
    List<SampleType> listSampleTypes();

    Page<SampleType> queryPage(SampleTypeRequest request);
}


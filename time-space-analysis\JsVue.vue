<template>
  <CommonPage>
    <template #title-suffix>
      <div
        class="ml-16 flex cursor-pointer items-center text-16 opacity-60 transition-all-300 hover:opacity-40"
        @click="router.back()"
      >
        <i class="i-material-symbols:arrow-left-alt" />
        <span class="ml-4">返回</span>
      </div>
    </template>
    <template #action>
      <!-- 级联选择器 -->
      <div class="filter-container" style="display:flex;width: 50%;gap: 16px">
        <NSelect
          v-model:value="selectedInvestigationCenter"
          :options="investigationCenters.map(c => ({ label: c.name, value: c.id }))"
          placeholder="选择调查中心"
          @update:value="() => selectedRoute = null"
        />
        <NSelect
          v-model:value="selectedRoute"
          :options="routes.map(r => ({ label: r.name, value: r.id }))"
          placeholder="选择航线"
          :disabled="!selectedInvestigationCenter"
          @update:value="() => selectedTime = null"
        />
        <NSelect
          v-model:value="selectedTime"
          :options="times.map(t => ({ label: `第${t.times}次调查 ${t.date}`, value: t.times }))"
          placeholder="选择调查次数"
          :disabled="!selectedRoute"
        />
        <NButton type="primary" @click="applyFilters">
          筛选
        </NButton>
      </div>

      <NModal v-model:show="showPredictionModal" preset="dialog" title="预测所选位置水文信息">
        <div style="display: flex; flex-direction: column; gap: 10px;">
          <NAlert type="warning" closable>
            <div style="display: flex; flex-direction: column; gap: 10px;">
              <span>经度范围: {{ longitudeRange.min }} 到 {{ longitudeRange.max }}</span>
              <span>纬度范围: {{ latitudeRange.min }} 到 {{ latitudeRange.max }}</span>
            </div>
          </NAlert>
          <NInputNumber v-model:value="longitude" placeholder="经度" :min="longitudeRange.min" :max="longitudeRange.max" />
          <NInputNumber v-model:value="latitude" placeholder="纬度" :min="latitudeRange.min" :max="latitudeRange.max" />
        </div>
        <template #action>
          <NButton type="primary" :disabled="!isValidInput" @click="predictWaterInfo">
            确认
          </NButton>
        </template>
      </NModal>
      <NModal
        v-model:show="showResultModal"
        style="width: 800px"
        preset="dialog"
        title="预测结果分析"
        :mask-closable="false"
      >
        <div class="flex flex-col gap-4">
          <!-- 图表部分 -->
          <div ref="chartRef" style="width: 100%; height: 400px" />
          <!-- 分析结果 -->
          <NCard title="AI实时分析预测结果">
            <div class="h-[100%]">
              <!-- 加载状态 -->
              <div
                v-if="isLoading"
                class="absolute inset-0 flex items-center justify-center bg-gray-50/50"
              >
                <n-spin size="small">
                  <template #description>
                    <span style="font-size: 14px" class="text-gray-600">AI正在分析中，请稍候...</span>
                  </template>
                </n-spin>
              </div>

              <!-- 内容容器 -->
              <div
                ref="analysisRef"
                class="analysis-container whitespace-pre-wrap text-gray-800 leading-relaxed"
                :class="{ 'opacity-50': isLoading }"
              />
            </div>
          </NCard>
        </div>

        <template #action>
          <NButton
            type="primary"
            :loading="isLoading"
            @click="closeResultModal"
          >
            {{ isLoading ? '分析中...' : '关闭' }}
          </NButton>
        </template>
      </NModal>
    </template>

    <div id="map-container" />

    <NDrawer
      v-model:show="DrawerVisible"
      width="600"
      placement="right"
      class="bg-gray-50"
    >
      <NDrawerContent title="站点详情" closable>
        <template #header>
          <span>站点详情</span>
          <NButton style="margin-left: 15px" tertiary type="primary" @click="DrawerVisibleHome = true">
            点击按月份查询
          </NButton>
          <NButton style="margin-left: 15px" tertiary type="info" @click="DrawerVisibleDataView = true">
            点击查看数据大屏
          </NButton>
        </template>
        <!-- 整体容器：上下垂直分布，留白和挤出 -->
        <div class="flex flex-col p-4 space-y-6">
          <!-- 基础信息卡片 -->
          <div class="rounded-xl bg-white p-14 shadow">
            <h4 class="mb-2 border-b border-gray-200 pb-1 font-semibold">
              站点信息
            </h4>
            <div class="grid grid-cols-2 mt-6 gap-2 text-12px text-gray-700">
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  名称：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.name || '--' }}
                </p>
              </div>
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  经度：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.longitude.toFixed(6) }}
                </p>
              </div>
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  纬度：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.latitude.toFixed(6) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 活动关联状态卡片 -->
          <div class="rounded-xl bg-white p-14 shadow">
            <h4 class="mb-2 border-b border-gray-200 pb-1 font-semibold">
              活动关联状态
            </h4>
            <div class="mt-6 flex flex-wrap gap-3">
              <NTag
                :type="wpActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goW"
              >
                水文气象采集：{{ wpActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="ciActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goC"
              >
                化学样本采集：{{ ciActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="mbActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goA"
              >
                成体生物量采集：{{ mbActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="mrActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goM"
              >
                微观繁殖体采集：{{ mrActivities ? '已关联' : '未关联' }}
              </NTag>
            </div>
            <!--          </div> -->

            <!-- 数据模块：每个子模块单独卡片 -->
            <div class="space-y-4">
              <!-- 水文气象数据 -->
              <div class="mt-10 flex flex-col gap-10px rounded-xl bg-white p-8 shadow">
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    水文气象数据
                  </h4>
                  <NTag type="warning">
                    {{ formatDateTime(weatherData[0].beforeInvestigate) }} - {{ formatDateTime(weatherData[0].afterInvestigate) }}
                  </NTag>
                </div>
                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(weatherData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(weatherData[0], 1)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible1" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="`${baseUrl}/upload/getImage?imageName=${file}`"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openVideo(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible1 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="weatherColumns"
                  :data="weatherData"
                  stripe
                  bordered
                />

                <!-- 化学离子数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    化学离子数据
                  </h4>
                  <NTag type="warning">
                    {{ formatDateTime(chemicalData[0]?.beforeInvestigate) }} - {{ formatDateTime(chemicalData[0]?.afterInvestigate) }}
                  </NTag>
                </div>

                <NButton
                  v-if="hasEvidence(chemicalData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(chemicalData[0], 2)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible2" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="`${baseUrl}/upload/getImage?imageName=${file}`"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openVideo(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible2 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="chemicalColumns"
                  :data="chemicalData"
                  stripe
                  bordered
                />

                <!-- 成体生物量数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    成体生物量数据
                  </h4>
                  <NTag type="warning">
                    {{ formatDateTime(abundanceData[0]?.beforeInvestigate) }} - {{ formatDateTime(abundanceData[0]?.afterInvestigate) }}
                  </NTag>
                </div>

                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(abundanceData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(abundanceData[0], 3)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible3" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="`${baseUrl}/upload/getImage?imageName=${file}`"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openVideo(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible3 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="abundanceColumns"
                  :data="abundanceData"
                  stripe
                  bordered
                />

                <!-- 微观繁殖体数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    微观繁殖体数据
                  </h4>
                  <NTag type="warning">
                    {{ formatDateTime(microData[0]?.beforeInvestigate) }} - {{ formatDateTime(microData[0]?.afterInvestigate) }}
                  </NTag>
                </div>

                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(microData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(microData[0], 3)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible4" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="`${baseUrl}/upload/getImage?imageName=${file}`"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openVideo(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible4 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="microColumns"
                  :data="microData"
                  stripe
                  bordered
                />
              </div>
            </div>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
    <NDrawer
      v-model:show="DrawerVisibleHome"
      width="650"
      placement="left"
      class="bg-gray-50"
    >
      <HomeEcharts :scale-id="selectedInvestigationCenter" />
    </NDrawer>
    <NDrawer
      v-model:show="DrawerVisibleDataView"
      :height="mainHeight - 60"
      placement="bottom"
      class="bg-gray-50"
    >
      <DataView :scale-id="selectedInvestigationCenter" :task-id="selectedRoute" :times-id="selectedTime" :distribute-id="selectedStation.id" />
    </NDrawer>
    <NModal
      v-model:show="modalVisible"
      draggable="true"
      preset="dialog"
      :title="`${type === 1 ? '新增' : '修改'}站点信息`"
      :show-icon="false"
      positive-text="确认"
      negative-text="取消"
      @positive-click="handleSubmit"
    >
      <NForm
        ref="modalFormRef"
        :rules="rules"
        label-placement="left"
        label-align="left"
        :label-width="110"
        :model="modalForm"
      >
        <NFormItem label="调查中心" path="scaleId">
          <n-select
            v-model:value="modalForm.scaleId" label-field="name" value-field="id" clearable filterable
            :options="stationOption" placeholder="请选择调查中心"
          />
        </NFormItem>
        <NFormItem label="任务编码" path="taskName">
          <NInput v-model:value="modalForm.taskName" placeholder="请输入任务编码" />
        </NFormItem>
        <NFormItem label="站点名称" path="name">
          <NInput v-model:value="modalForm.name" placeholder="请输入名称">
            <template #prefix>
              <!-- 动态显示前缀 -->
              <span style="color:rgb(112 112 112)">{{ dynamicPrefix }}</span>
            </template>
          </NInput>
        </NFormItem>
        <NFormItem label="经度" path="longitude">
          <NInputNumber
            v-model:value="modalForm.longitude"
            style="width: 100%;"
            placeholder="经度"
          />
        </NFormItem>
        <NFormItem label="纬度" path="latitude">
          <NInputNumber
            v-model:value="modalForm.latitude"
            style="width: 100%;"
            placeholder="纬度"
          />
        </NFormItem>
        <NFormItem label="关联活动" path="activeTypes">
          <n-checkbox-group v-model:value="modalForm.activeTypes">
            <n-space vertical>
              <n-checkbox value="wp">
                水文气象采集活动 (W)
              </n-checkbox>
              <n-checkbox value="ci">
                化学样本采集活动 (C)
              </n-checkbox>
              <n-checkbox value="mb">
                成体生物量采集活动 (A)
              </n-checkbox>
              <n-checkbox value="mr">
                微观繁殖体采集活动 (M)
              </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </NFormItem>
      </NForm>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { CommonPage } from '@/components/index.js'
import { useAuthStore } from '@/store/index.js'
import { formatDateTime } from '@/utils/index.js'

import HomeEcharts from '@/views/home/<USER>'
import DataView from '@/views/ims/data-view/index.vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import * as echarts from 'echarts'
import { EventSourcePolyfill } from 'event-source-polyfill'
import { NAlert, NButton, NCard, NForm, NFormItem, NInput, NInputNumber, NModal, NTag } from 'naive-ui'
import { computed, h, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import api from './api'

// 预测相关状态
const showPredictionModal = ref(false)
const showResultModal = ref(false)
const longitude = ref(null)
const latitude = ref(null)
const hasMarkers = ref(false)
const selectedStation = ref(null)
const resultData = ref(null)
const chartRef = ref(null)
const longitudeRange = ref({ min: null, max: null })
const latitudeRange = ref({ min: null, max: null })

// 站点管理相关状态
const route = useRoute()
const router = useRouter()
const map = ref(null)
const markers = ref([])
const polygon = ref(null)
const centerMarker = ref(null)
const stationPointScale = ref([])
const stationPointDistribute = ref([])
const mainPoint = ref([])
const modalFormRef = ref(null)
const modalVisible = ref(false)
const DrawerVisible = ref(false)
const DrawerVisibleHome = ref(false)
const DrawerVisibleDataView = ref(false)
const type = ref(1)
const originalLng = ref()
const originalLat = ref()

// 新增 DAG 相关状态
const directedEdges = ref([])
const routePaths = ref([])
const routeMarkers = ref([])
const isDrawingRoute = ref(false)
const routeStartPoint = ref(null)

const stationOption = ref([])

const modalForm = reactive({
  scaleId: null,
  name: '',
  longitude: null,
  latitude: null,
  id: null,
  activeTypes: null,
  taskId: null,
  taskName: null,
})

const evidenceModalVisible1 = ref(false)
const evidenceModalVisible2 = ref(false)
const evidenceModalVisible3 = ref(false)
const evidenceModalVisible4 = ref(false)
const selectedEvidenceFiles = ref([])
const currentRow = ref(null)
const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)

// 判断是否有佐证材料
function hasEvidence(row) {
  if (!row)
    return
  if (!row.evidenceFiles)
    return false
  try {
    const files = JSON.parse(row.evidenceFiles)
    return Array.isArray(files) && files.length > 0
  }
  catch (error) {
    console.error('解析 evidenceFiles 失败:', error)
    return false
  }
}

// 获取解析后的文件列表
function getEvidenceFiles(row) {
  if (!row.evidenceFiles)
    return []
  try {
    const files = JSON.parse(row.evidenceFiles)
    return Array.isArray(files) ? files : []
  }
  catch (error) {
    console.error('解析 evidenceFiles 失败:', error)
    return []
  }
}

// 显示佐证材料抽屉
function showEvidenceModal(row, type) {
  currentRow.value = row
  selectedEvidenceFiles.value = getEvidenceFiles(row)
  switch (type) {
    case 1:
      evidenceModalVisible1.value = true
      break
    case 2:
      evidenceModalVisible2.value = true
      break
    case 3:
      evidenceModalVisible3.value = true
      break
    case 4:
      evidenceModalVisible4.value = true
      break
    default:
      selectedEvidenceFiles.value = []
  }
}

// 判断是否为图片
function isImage(filename) {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
  const ext = filename.toLowerCase().slice(-4)
  return imageExtensions.includes(ext)
}

// 打开视频
function openVideo(filename) {
  window.open(`${baseUrl.value}/upload/getImage?imageName=${filename}`, '_blank')
}

async function getStationOption() {
  const { data } = await api.getStationPoints()
  stationOption.value = data
}

function generatePrefix(types) {
  if (!types || types.length === 0)
    return ''

  const prefixMap = {
    wp: 'W',
    ci: 'C',
    mb: 'A',
    mr: 'M',
  }

  // 安全处理排序
  return [...types]
    .sort((a, b) => {
      const order = Object.keys(prefixMap)
      return order.indexOf(a) - order.indexOf(b)
    })
    .map(type => prefixMap[type] || '')
    .join('')
}

function goW() {
  router.push({
    path: '/ims/water-environmental',
    query: { distributeId: weatherData.value[0].distributeId, times: selectedTime.value },
  })
}

function goC() {
  router.push({
    path: '/ims/seawater-chemistry',
    query: { distributeId: chemicalData.value[0].distributeId, times: selectedTime.value },
  })
}

function goA() {
  router.push({
    path: '/ims/sample-of-algae',
    query: { distributeId: abundanceData.value[0].distributeId, times: selectedTime.value },
  })
}

function goM() {
  router.push({
    path: '/ims/surface-water-sample-record',
    query: { distributeId: microData.value[0].distributeId, times: selectedTime.value },
  })
}

// 计算属性：动态生成前缀
const dynamicPrefix = computed(() => {
  return generatePrefix(modalForm?.activeTypes || [])
})

const weatherColumns = [
  { title: '天气', key: 'weather', render: row => `${row.weather || '--'}` },
  { title: '风向', key: 'windDirection', render: row => `${row.windDirection || '--'}` },
  { title: '气温', key: 'airTemperature', render: row => `${row.airTemperature || '--'}℃` },
  { title: '水温', key: 'waterTemperature', render: row => `${row.waterTemperature || '--'}℃` },
  { title: '盐度', key: 'saltExtent', render: row => `${row.saltExtent || '--'} PSU` },
  { title: 'pH', key: 'phExtent', render: row => `${row.phExtent || '--'}` },
  { title: '透明度', key: 'transparentExtent', render: row => `${row.transparentExtent || '--'} 米` },
]

const chemicalColumns = [
  { title: '活性磷酸盐', key: 'activePhosphate', render: row => `${row.activePhosphate || '--'} mg/L` },
  { title: '亚硝酸盐氮', key: 'nitriteNitrogen', render: row => `${row.nitriteNitrogen || '--'} mg/L` },
  { title: '硝酸盐氮', key: 'nitrateNitrogen', render: row => `${row.nitrateNitrogen || '--'} mg/L` },
  { title: '氨根', key: 'ammoniaHydrogen', render: row => `${row.ammoniaHydrogen || '--'} mg/L` },
]

const abundanceColumns = [
  {
    title: '样品类型',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'primary' }, { default: () => '藻样' })
    },
  },
  {
    title: '种类',
    // width: 300,
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList) || row.sampleTypeList.length === 0) {
        return h('span', { style: { color: 'orange' } }, '--')
      }
      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        return h(NTag, { key: index, type: 'info' }, { default: () => type.name })
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
]

const microColumns = [
  {
    title: '样品类型',
    ellipsis: { tooltip: true },
    render(row) {
      // 非空检查
      if (row.sampleType === null || row.sampleType === undefined) {
        return h('span', { style: { color: 'orange' } }, '--')
      }

      // 根据 sampleType 映射名称
      let typeName
      switch (row.sampleType) {
        case 1:
          typeName = '底层水样微观繁殖体'
          break
        case 2:
          typeName = '表层水样微观繁殖体'
          break
        case 0:
          typeName = '沉积物'
          break
        default:
          typeName = '未知类型'
      }

      // 返回 NTag 组件
      return h(NTag, { type: 'primary' }, typeName)
    },
  },
  {
    title: '种类及丰度',
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList) || row.sampleTypeList.length === 0) {
        return h('span', { style: { color: 'red' } }, '--')
      }

      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        const name = type.name || '未知种类'
        const number = type.number !== undefined ? `${type.number} ind./L` : '--'
        return h(
          NTag,
          { key: index, type: 'primary', style: { margin: '4px 0' } },
          `${name} ${number}`,
        )
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
]

const wpActivities = computed(() => selectedStation.value?.wpActivities)
const ciActivities = computed(() => selectedStation.value?.ciActivities)
const mbActivities = computed(() => selectedStation.value?.mbActivities)
const mrActivities = computed(() => selectedStation.value?.mrActivities)

const weatherData = computed(() => {
  if (!selectedStation.value?.waterPhWeatherData)
    return []
  return [{ ...selectedStation.value.waterPhWeatherData }]
})

const chemicalData = computed(() => {
  if (!selectedStation.value?.chemicalIon)
    return []
  return [{ ...selectedStation.value.chemicalIon }]
})

const abundanceData = computed(() => {
  if (!selectedStation.value?.abundanceLayerSpeciesDataList)
    return []
  return selectedStation.value.abundanceLayerSpeciesDataList.filter(item => item.sampleType === 3)
})

const microData = computed(() => {
  if (!selectedStation.value?.abundanceLayerSpeciesDataList)
    return []
  return selectedStation.value.abundanceLayerSpeciesDataList.filter(item => item.sampleType !== 3)
})

// 响应式变量
const selectedInvestigationCenter = ref(null) // 选中的调查中心 ID
const investigationCenters = ref([]) // 调查中心列表
const selectedRoute = ref(null) // 选中的航线 ID
const routes = ref([]) // 航线列表（根据调查中心动态加载）
const selectedTime = ref(null) // 选中的调查次数
const times = ref([]) // 调查次数列表（根据航线动态加载）

async function getScaleList() {
  const { data } = await api.getScaleList()
  investigationCenters.value = data
}

// 当调查中心变化时，加载航线
watch(selectedInvestigationCenter, async (newVal) => {
  if (newVal) {
    const res = await api.getRoutesByScaleId(newVal)
    routes.value = res.data
    selectedRoute.value = null // 重置航线选择
    times.value = []
    selectedTime.value = null
  }
})

// 当航线变化时，加载调查次数
watch(selectedRoute, async (newVal) => {
  if (newVal) {
    const res = await api.getSurveyTimesByTaskId(newVal)
    times.value = res.data
    selectedTime.value = null
  }
})

async function applyFilters() {
  await getPoints()
  loadMap()
}

const rules = reactive({
  name: { required: true, message: '请输入站点名称', trigger: ['input', 'blur'] },
  longitude: { required: true, message: '请输入经度', type: 'number', trigger: ['input', 'blur'] },
  latitude: { required: true, message: '请输入纬度', type: 'number', trigger: ['input', 'blur'] },
  scaleId: { required: true, message: '请选择调查中心', type: 'number', trigger: ['change', 'blur'] },
  activeTypes: {
    type: 'array',
    required: true,
    validator: (rule, value) => Boolean(value && value.length > 0),
    message: '请至少选择一项关联活动',
    trigger: ['change'],
  },
})

const isValidInput = computed(() => {
  return (
    longitude.value !== null
    && latitude.value !== null
    && longitude.value >= longitudeRange.value.min
    && longitude.value <= longitudeRange.value.max
    && latitude.value >= latitudeRange.value.min
    && latitude.value <= latitudeRange.value.max
  )
})

async function getPoints() {
  if (route.query.scaleId) {
    selectedInvestigationCenter.value = route.query.scaleId
  }

  // 如果未选择完整筛选条件，只保留已有数据，不重新请求
  if (!selectedInvestigationCenter.value || !selectedRoute.value || !selectedTime.value) {
    $message.warning('请先选择调查中心、航线和调查次数')

    // 设置默认值，确保地图能正常加载
    stationPointScale.value = [119.366892, 34.760023] // 北京作为默认中心
    stationPointDistribute.value = []
    mainPoint.value = []
    return
  }

  const { data } = await api.getPointDistributes(
    selectedInvestigationCenter.value,
    selectedRoute.value,
    selectedTime.value,
  )

  stationPointScale.value = [data.stationPointScale.longitude, data.stationPointScale.latitude]
  stationPointDistribute.value = data.stationPointDistribute.map(item => [item.longitude, item.latitude])
  mainPoint.value = data.stationPointDistribute

  // 计算范围
  if (stationPointDistribute.value.length > 0) {
    longitudeRange.value.min = Math.min(...stationPointDistribute.value.map(point => point[0]))
    longitudeRange.value.max = Math.max(...stationPointDistribute.value.map(point => point[0]))
    latitudeRange.value.min = Math.min(...stationPointDistribute.value.map(point => point[1]))
    latitudeRange.value.max = Math.max(...stationPointDistribute.value.map(point => point[1]))
    hasMarkers.value = true
  }

  initDirectedGraph()
}

// 初始化有向无环图
function initDirectedGraph() {
  // 清除现有路径
  // clearRoutePaths()

  // 创建有向边
  const points = mainPoint.value
  for (let i = 0; i < points.length - 1; i++) {
    const from = points[i]
    const to = points[i + 1]

    directedEdges.value.push({
      from: from.id,
      to: to.id,
      fromPos: [from.longitude, from.latitude],
      toPos: [to.longitude, to.latitude],
      createTime: from.createTime,
    })
  }

  // 绘制所有路径
  drawAllRoutes()
}

// 绘制所有路径
function drawAllRoutes(AMap) {
  if (!AMap || !map.value)
    return

  // 清除现有路径
  clearRoutePaths()

  directedEdges.value.forEach((edge, index) => {
    const path = [edge.fromPos, edge.toPos]

    const polyline = new AMap.Polyline({
      path,
      strokeColor: '#1890ff',
      strokeWeight: 4,
      strokeStyle: 'dashed',
      strokeDasharray: [10, 5], // 虚线样式
      lineJoin: 'round',
      showDir: true,
      strokeOpacity: 0.8,
      isOutline: true,
      outlineColor: '#fff',
      onInit: (e) => {
        const svgPath = e.getElement() // 获取 SVG 路径元素
        if (svgPath) {
          // 显式设置 stroke-dasharray（必须与 strokeDasharray 一致）
          svgPath.style.strokeDasharray = '10,5'
          // 启动流动动画
          svgPath.style.animation = `flow ${2 + index * 0.1}s linear infinite`
        }
      },
    })

    map.value.add(polyline)
    routePaths.value.push(polyline)
  })
}

// 清除所有路径
function clearRoutePaths() {
  if (!map.value)
    return

  routePaths.value.forEach((path) => {
    map.value.remove(path)
  })

  routeMarkers.value.forEach((marker) => {
    map.value.remove(marker)
  })

  routePaths.value = []
  routeMarkers.value = []
}

const isLoading = ref(false)
const analysisRef = ref(null)
const eventSource = ref(null)
let hasReceivedData = false // 标记是否已收到数据

async function predictWaterInfo() {
  try {
    // 先保存坐标值，避免异步丢失
    const currentLongitude = longitude.value
    const currentLatitude = latitude.value
    const currentScaleId = route.query.scaleId

    // 发起预测请求
    const { data } = await api.predictWaterQuality(
      currentLongitude,
      currentLatitude,
      selectedInvestigationCenter.value,
      selectedRoute.value,
      selectedTime.value,
    )

    resultData.value = data
    showResultModal.value = true

    // 等待模态框渲染完成
    await nextTick()

    // 安全检查
    if (!analysisRef.value) {
      throw new Error('分析结果容器未初始化')
    }

    // 初始化图表
    initChart()

    // 开始流式分析（使用保存的坐标值）
    await startAnalysisStream(currentLongitude, currentLatitude, currentScaleId)
  }
  catch (error) {
    console.error('预测失败:', error)
    $message.error(`预测失败: ${error.message}`)
  }
  finally {
    // 最后才重置输入
    showPredictionModal.value = false
    resetInputs() // 如果确实需要重置
  }
}

async function startAnalysisStream(lng, lat, scaleId) {
  try {
    isLoading.value = true
    hasReceivedData = false

    const container = analysisRef.value
    container.textContent = ''

    const params = new URLSearchParams({
      longitude: lng,
      latitude: lat,
      scaleId: selectedInvestigationCenter.value,
      taskId: selectedRoute.value,
      times: selectedTime.value,
    })

    const { accessToken } = useAuthStore()

    eventSource.value = new EventSourcePolyfill(
      `/api/waterquality/predict/analysis-stream?${params}`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
        withCredentials: true,
      },
    )

    eventSource.value.addEventListener('open', () => {
      // 连接成功后5秒仍未收到数据显示提示
      setTimeout(() => {
        if (!hasReceivedData) {
          $message.warning('数据接收较慢，请耐心等待...')
        }
      }, 5000)
    })

    eventSource.value.onmessage = (e) => {
      if (!hasReceivedData) {
        hasReceivedData = true
        isLoading.value = false // 收到第一条数据时关闭加载状态
      }
      try {
        appendAnalysisText(e.data)
      }
      catch (error) {
        console.error('解析数据失败:', error)
      }
    }

    eventSource.value.onerror = () => {
      if (!hasReceivedData) {
        $message.error('分析服务连接失败')
      }
      isLoading.value = false
      eventSource.value?.close()
    }
  }
  catch (err) {
    isLoading.value = false
    console.error('SSE连接错误:', err)
    $message.error('启动分析失败')
    throw err
  }
}

let buffer = ''
let isRendering = false
const BATCH_SIZE = 20 // 每批渲染字符数
const DEBOUNCE_TIME = 100 // 渲染间隔时间
// 打字机效果输出
// 改进后的打字机效果
function appendAnalysisText(text) {
  buffer += text
  if (!isRendering) {
    scheduleRender()
  }
}

function scheduleRender() {
  if (buffer.length === 0) {
    isRendering = false
    return
  }

  isRendering = true
  requestAnimationFrame(() => {
    const container = analysisRef.value
    const fragment = document.createDocumentFragment()
    const charsToRender = buffer.substring(0, BATCH_SIZE)

    // 批量创建元素
    const spans = []
    for (const char of charsToRender) {
      const span = document.createElement('span')
      span.textContent = char
      span.style.cssText = `
        opacity: 0;
        animation: fadeIn 0.3s ease forwards;
        white-space: pre-wrap;
      `
      spans.push(span)
    }

    // 使用CSS动画替代JS控制
    const style = document.createElement('style')
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(2px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `
    fragment.appendChild(style)

    // 批量添加
    spans.forEach((span, index) => {
      span.style.animationDelay = `${index * 30}ms`
      fragment.appendChild(span)
    })

    container.appendChild(fragment)
    buffer = buffer.substring(BATCH_SIZE)

    // 优化滚动控制
    throttledScroll(container)

    // 分批渲染
    if (buffer.length > 0) {
      setTimeout(scheduleRender, DEBOUNCE_TIME)
    }
    else {
      isRendering = false
    }
  })
}

// 节流滚动函数
let lastScroll = 0
function throttledScroll(container) {
  const now = Date.now()
  if (now - lastScroll > 500) {
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth',
    })
    lastScroll = now
  }
}

onBeforeUnmount(() => {
  eventSource.value?.close()
})

// 关闭模态框时断开连接
function closeResultModal() {
  if (eventSource.value) {
    eventSource.value.close()
  }
  isLoading.value = false
  showResultModal.value = false
}

function initChart() {
  try {
    // 1. 容器检查
    const chartDom = chartRef.value
    if (!chartDom) {
      throw new Error('图表容器未找到')
    }

    // 2. 数据验证
    if (!resultData.value) {
      throw new Error('预测结果数据未加载')
    }

    // 3. 安全获取坐标值
    const getSafeCoordinate = (value) => {
      const num = Number(value)
      return Number.isNaN(num) ? '--' : num.toFixed(4)
    }

    const coordText = longitude.value !== null && latitude.value !== null
      ? `预测点位 (${getSafeCoordinate(longitude.value)}, ${getSafeCoordinate(latitude.value)})`
      : '水质预测结果'

    // 4. 安全获取数据值
    const safeData = {
      saltExtent: resultData.value.saltExtent ?? 0,
      phExtent: resultData.value.phExtent ?? 7,
      waterTemperature: resultData.value.waterTemperature ?? 0,
      transparentExtent: resultData.value.transparentExtent ?? 0,
    }

    // 5. 初始化图表实例
    const myChart = echarts.init(chartDom)

    // 6. 配置项
    const option = {
      title: {
        text: coordText,
        left: 'center',
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold',
        },
        subtext: '水文参数预测值',
        subtextStyle: {
          color: '#666',
          fontSize: 12,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params) => {
          const data = params[0]
          return `
            <div style="padding: 8px; background: #fff; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
              <div style="font-weight: 600; color: #333; margin-bottom: 6px">${data.name}</div>
              <div style="display: flex; justify-content: space-between">
                <span style="color: #666">预测值:</span>
                <span style="font-weight: 600; color: #2b8cbe"> ${Number(data.value).toFixed(3)} ${data.data.unit}</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-top: 4px">
                <span style="color: #666">正常范围:</span>
                <span style="color: #888">${data.data.min}-${data.data.max}${data.data.unit}</span>
              </div>
            </div>
          `
        },
      },
      grid: {
        top: '20%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['盐度', 'pH值', '水温', '透明度'],
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 14,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '数值',
        nameTextStyle: {
          color: '#666',
          padding: [0, 0, 0, 40],
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          color: '#999',
        },
      },
      series: [
        {
          name: '预测值',
          type: 'bar',
          barWidth: '50%',
          data: [
            {
              value: safeData.saltExtent,
              name: '盐度',
              unit: 'PSU',
              min: 0,
              max: 40,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#7bccc4' },
                  { offset: 1, color: '#4a9d9c' },
                ]),
              },
            },
            {
              value: safeData.phExtent,
              name: 'pH值',
              unit: '',
              min: 0,
              max: 14,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#f03b20' },
                  { offset: 1, color: '#c92a2a' },
                ]),
              },
            },
            {
              value: safeData.waterTemperature,
              name: '水温',
              unit: '℃',
              min: 0,
              max: 30,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#fd8d3c' },
                  { offset: 1, color: '#f76808' },
                ]),
              },
            },
            {
              value: safeData.transparentExtent,
              name: '透明度',
              unit: '米',
              min: 0,
              max: 5,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2c7fb8' },
                  { offset: 1, color: '#1a5f8e' },
                ]),
              },
            },
          ],
          label: {
            show: true,
            position: 'top',
            formatter: ({ value }) => value.toFixed(2),
            color: '#333',
            fontSize: 12,
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 4,
            shadowOffsetY: 2,
          },
        },
      ],
      graphic: {
        type: 'group',
        right: 20,
        top: 10,
        children: [
          {
            type: 'circle',
            shape: { r: 6 },
            style: { fill: '#2b8cbe' },
          },
          {
            type: 'text',
            left: 10,
            style: {
              text: '预测值',
              fill: '#666',
              fontSize: 12,
            },
          },
        ],
      },
    }

    // 7. 渲染图表
    myChart.setOption(option)

    // 8. 响应式适配
    const resizeHandler = () => myChart.resize()
    window.addEventListener('resize', resizeHandler)

    // 9. 清理监听器
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      myChart.dispose()
    })

    return myChart
  }
  catch (error) {
    console.error('图表初始化失败:', error)
    $message.error('结果展示初始化失败')
    return null
  }
}

function resetInputs() {
  longitude.value = null
  latitude.value = null
}

// 在script setup顶部添加工具函数
function convexHull(points) {
  if (points.length <= 3)
    return points

  points = Array.from(new Set(points.map(JSON.stringify))).map(JSON.parse)
  points.sort((a, b) => a[0] - b[0] || a[1] - b[1])

  const lower = []
  for (const p of points) {
    while (lower.length >= 2 && cross(lower[lower.length - 2], lower[lower.length - 1], p) <= 0) {
      lower.pop()
    }
    lower.push(p)
  }

  const upper = []
  for (let i = points.length - 1; i >= 0; i--) {
    const p = points[i]
    while (upper.length >= 2 && cross(upper[upper.length - 2], upper[upper.length - 1], p) <= 0) {
      upper.pop()
    }
    upper.push(p)
  }

  lower.pop()
  upper.pop()
  return lower.concat(upper)
}

function cross(o, a, b) {
  return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])
}

// 完整的createPolygon函数
function createPolygon(AMap) {
  const points = stationPointDistribute.value.map(pos => [pos[0], pos[1]])
  const hull = convexHull(points)

  polygon.value = new AMap.Polygon({
    path: hull,
    fillColor: '#ccebc5',
    strokeColor: 'rgba(43, 140, 190, 0.2)', // ✅ 低透明度颜色
    strokeWeight: 1, // 调整边线粗细
    fillOpacity: 0.4,
    strokeStyle: 'dashed',
    strokeDasharray: [2, 5], // 更细的虚线
    strokeOpacity: 0.2, // ✅ 设置透明度（0-1）
  })

  // 修改后的多边形点击事件处理
  polygon.value.on('click', (e) => {
    // 使用自定义属性而不是直接修改 originalEvent
    const event = {
      ...e,
      _processed: true, // 使用自定义属性标记已处理
    }

    const lnglat = event.lnglat || event.target.getPosition()
    longitude.value = Number(lnglat.getLng().toFixed(6))
    latitude.value = Number(lnglat.getLat().toFixed(6))
    showPredictionModal.value = true

    // 阻止事件冒泡到地图
    if (event.cancelable) {
      event.stopPropagation()
    }
  })

  map.value.add(polygon.value)
}

async function handleMapClick(e) {
  const AMap = window.AMap
  const lnglat = e.lnglat || e.target.getPosition()
  const clickPoint = [lnglat.getLng(), lnglat.getLat()]

  // 检查是否在凸包内部
  if (polygon.value) {
    const polygonPath = polygon.value.getPath().map(p => [p.lng, p.lat])

    if (polygonPath.length >= 3 && AMap.GeometryUtil.isPointInRing(clickPoint, polygonPath)) {
      // 在区域内，自动填充预测坐标
      longitude.value = Number(clickPoint[0].toFixed(6))
      latitude.value = Number(clickPoint[1].toFixed(6))
      showPredictionModal.value = true
      return
    }
  }

  // 在区域外，允许添加站点
  modalForm.longitude = Number(clickPoint[0].toFixed(6))
  modalForm.latitude = Number(clickPoint[1].toFixed(6))
  modalForm.name = null
  modalForm.id = null
  modalForm.scaleId = selectedInvestigationCenter.value
  modalForm.taskId = selectedRoute.value
  modalForm.taskName = routes.value.find(x => x.id === selectedRoute.value)?.name || null
  type.value = 1
  modalVisible.value = true
  $message.success('已获取该点坐标,请添加站点信息')

  if (isDrawingRoute.value) {
    const lnglat = e.lnglat || e.target.getPosition()
    const clickPoint = [lnglat.getLng(), lnglat.getLat()]

    // 查找最近的站点
    const nearestStation = findNearestStation(clickPoint)

    if (!nearestStation) {
      $message.warning('请点击已有的站点')
      return
    }

    if (!routeStartPoint.value) {
      // 设置起点
      routeStartPoint.value = nearestStation
      $message.info(`已选择 ${nearestStation.name} 作为起点，请选择终点`)
    }
    else {
      // 设置终点并创建路径
      const AMap = window.AMap
      createDirectedEdge(routeStartPoint.value, nearestStation, AMap)

      $message.success(`已创建从 ${routeStartPoint.value.name} 到 ${nearestStation.name} 的航线`)
      routeStartPoint.value = null
      isDrawingRoute.value = false
    }
  }
}

// 查找最近的站点
function findNearestStation(point) {
  const AMap = window.AMap
  if (!AMap || !mainPoint.value.length)
    return null

  let minDistance = Infinity
  let nearestStation = null

  mainPoint.value.forEach((station) => {
    const stationPoint = new AMap.LngLat(station.longitude, station.latitude)
    const clickPoint = new AMap.LngLat(point[0], point[1])
    const distance = AMap.GeometryUtil.distance(stationPoint, clickPoint)

    if (distance < minDistance && distance < 0.01) { // 0.01度约1公里
      minDistance = distance
      nearestStation = station
    }
  })

  return nearestStation
}

// 创建有向边
function createDirectedEdge(from, to, AMap) {
  // 检查是否已存在相同路径
  const exists = directedEdges.value.some(edge =>
    edge.from === from.id && edge.to === to.id,
  )

  if (exists) {
    $message.warning('该路径已存在')
    return
  }

  // 添加新路径
  directedEdges.value.push({
    from: from.id,
    to: to.id,
    fromPos: [from.longitude, from.latitude],
    toPos: [to.longitude, to.latitude],
    createTime: new Date().toISOString(),
  })

  // 重新绘制所有路径
  drawAllRoutes(AMap)
}

async function handleSubmit() {
  try {
    await modalFormRef.value?.validate()

    const longitude = Number(modalForm.longitude)
    const latitude = Number(modalForm.latitude)

    if (Number.isNaN(longitude) || Number.isNaN(latitude)) {
      throw new TypeError('经纬度必须是有效数字')
    }

    const prefix = generatePrefix(modalForm.activeTypes)
    const fullName = prefix + modalForm.name

    if (type.value === 1) {
      const response = await api.createDistribute({
        taskName: modalForm.taskName,
        name: fullName, // 使用拼接后的名称
        scaleId: modalForm.scaleId,
        wpActivities: modalForm.activeTypes.includes('wp'),
        ciActivities: modalForm.activeTypes.includes('ci'),
        mbActivities: modalForm.activeTypes.includes('mb'),
        mrActivities: modalForm.activeTypes.includes('mr'),
        longitude,
        latitude,
      })

      if (response.code !== 0) {
        throw new Error(response.message || 'API请求失败')
      }
      await getPoints()
      loadMap()
      $message.success('添加站点成功')
    }
    else {
      const response = await api.updateDistribute({
        ...modalForm,
        longitude,
        latitude,
        scaleId: route.query.scaleId,
      })
      if (response.code !== 0) {
        throw new Error(response.message || 'API请求失败')
      }
      await getPoints()
      loadMap()
      $message.success('修改站点成功')
    }

    const AMap = window.AMap || await AMapLoader.load({
      key: '3b4146d6e0e773ad45d905f20e1a3070',
      version: '2.0',
    })

    const newPos = new AMap.LngLat(longitude, latitude)
    const markerName = modalForm.name

    if (type.value === 1) {
      const infoWindow = new AMap.InfoWindow({
        content: `
          <div class="custom-info-window">
            <div class="title">${markerName}</div>
            <div class="coord">经度: ${longitude.toFixed(6)}</div>
            <div class="coord">纬度: ${latitude.toFixed(6)}</div>
          </div>
        `,
        offset: new AMap.Pixel(-6, -30),
      })

      const newMarker = new AMap.Marker({
        position: newPos,
        content: createMarkerContent(markerName),
        anchor: 'bottom-center',
      })

      newMarker.on('click', () => {
        infoWindow.open(map.value, newPos)
        Object.assign(modalForm, {
          longitude,
          latitude,
          name: markerName,

        })
        modalVisible.value = true
      })

      try {
        map.value.add(newMarker)
        markers.value.push(newMarker)
      }
      catch (addError) {
        console.error('标记添加失败:', addError)
        throw new Error('标记添加失败，请手动刷新查看')
      }
    }
    else {
      const targetMarker = markers.value.find((marker) => {
        const pos = marker.getPosition()
        return pos && pos.lng === originalLng.value && pos.lat === originalLat.value
      })

      if (targetMarker) {
        targetMarker.setPosition(newPos)

        const newContent = `
          <div class="custom-info-window">
            <div class="title">${markerName}</div>
            <div class="coord">经度: ${longitude.toFixed(6)}</div>
            <div class="coord">纬度: ${latitude.toFixed(6)}</div>
          </div>
        `

        if (targetMarker.infoWindow) {
          targetMarker.infoWindow.close()
        }

        targetMarker.infoWindow = new AMap.InfoWindow({
          content: newContent,
          offset: new AMap.Pixel(-6, -30),
        })

        targetMarker.setContent(createMarkerContent(markerName))

        targetMarker.off('click')
        targetMarker.on('click', () => {
          targetMarker.infoWindow.open(map.value, newPos)
          Object.assign(modalForm, {
            longitude,
            latitude,
            name: markerName,
          })
          modalVisible.value = true
        })
      }
      else {
        throw new Error('未找到要修改的标记')
      }
    }

    updatePolygonPath()

    modalFormRef.value?.restoreValidation()
    Object.assign(modalForm, {
      name: '',
      longitude: null,
      latitude: null,
    })
    return true
  }
  catch (error) {
    $message.error(error.message || '操作失败')
    console.error('提交错误详情:', error)
    return false
  }
}

function createMarkerContent(name) {
  return `
    <div class="custom-marker">
      <div class="label">
        <span class="icon">📍</span>
        <span class="text">${name}</span>
      </div>
      <div class="pin"></div>
    </div>
  `
}

function updatePolygonPath() {
  if (!polygon.value || !markers.value.length)
    return

  const newPath = markers.value.map((marker) => {
    const pos = marker.getPosition()
    return [pos.getLng(), pos.getLat()]
  })

  const hull = convexHull(newPath)
  polygon.value.setPath(hull)
}

async function loadMap() {
  try {
    // const AMap = await AMapLoader.load({
    //   key: '3b4146d6e0e773ad45d905f20e1a3070',
    //   version: '2.0',
    //   plugins: ['AMap.Polygon', 'AMap.Marker', 'AMap.InfoWindow', 'AMap.GeometryUtil'],
    // })

    map.value = new AMap.Map('map-container', {
      viewMode: '2D',
      zoom: 10,
      center: stationPointScale.value,
      layers: [
        new AMap.TileLayer({
          visible: true,
          zIndex: 99,
          opacity: 1,
          getTileUrl: (a, b, c) => {
            // 经纬度转换成本地瓦片所在路径
            const flag = '00000000'
            const zz = c
            const z = `L${zz}`
            const xx = a.toString(16)
            const x = `C${flag.substring(0, 8 - xx.length)}${xx}`
            const yy = b.toString(16)
            const y = `R${flag.substring(0, 8 - yy.length)}${yy}`
            return `/MAP_zxy/${z}/${y}/${x}.png`
          },
        }),
      ],
    })

    map.value.on('click', (e) => {
      if (e._processed)
        return
      handleMapClick(e)
    })

    createCenterMarker(AMap)

    // 仅在有站点数据时才创建标记和多边形
    if (stationPointDistribute.value && stationPointDistribute.value.length > 0) {
      createMarkers(AMap)
      createPolygon(AMap)
      drawAllRoutes(AMap)
    }
  }
  catch (error) {
    $message.error(`地图加载失败: ${error.message}`)
  }
}

function createCenterMarker(AMap) {
  const centerPos = stationPointScale.value || [119.366892, 34.760023] // 默认值
  const centerName = route.query.scaleName || '调查中心'

  const content = `
    <div class="custom-marker center-marker">
      <div class="label">
        <span class="icon">🏢</span>
        <span class="text">${centerName}</span>
      </div>
      <div class="pin"></div>
    </div>
  `

  const infoWindow = new AMap.InfoWindow({
    content: `
      <div class="custom-info-window center-info">
        <div class="title">${centerName}</div>
        <div class="coord">经度: ${(centerPos[0] || 0).toFixed(6)}</div>
        <div class="coord">纬度: ${(centerPos[1] || 0).toFixed(6)}</div>
      </div>
    `,
    offset: new AMap.Pixel(-6, -30),
  })

  centerMarker.value = new AMap.Marker({
    position: centerPos,
    content,
    anchor: 'bottom-center',
  })

  centerMarker.value.on('click', () => {
    infoWindow.open(map.value, centerPos)
  })

  map.value.add(centerMarker.value)
}

function createMarkers(AMap) {
  if (!stationPointDistribute.value || stationPointDistribute.value.length === 0)
    return

  markers.value = stationPointDistribute.value.map((pos, index) => {
    const marker = new AMap.Marker({
      position: pos,
      content: createMarkerContent(mainPoint.value[index].name),
      anchor: 'bottom-center',
    })

    const infoWindow = new AMap.InfoWindow({
      content: `
        <div class="custom-info-window">
          <div class="title">${mainPoint.value[index].name}</div>
          <div class="coord">经度: ${pos[0].toFixed(6)}</div>
          <div class="coord">纬度: ${pos[1].toFixed(6)}</div>
        </div>
      `,
      offset: new AMap.Pixel(-6, -30),
    })

    marker.on('click', () => {
      selectedStation.value = mainPoint.value[index]
      DrawerVisible.value = true // 显示抽屉
      modalForm.longitude = pos[0]
      modalForm.latitude = pos[1]
      modalForm.name = mainPoint.value[index].name
      modalForm.id = mainPoint.value[index].id
      originalLng.value = pos[0]
      originalLat.value = pos[1]
      type.value = 2
      infoWindow.open(map.value, pos)
    })

    map.value.add(marker)
    return marker
  })
}

onBeforeUnmount(() => {
  if (map.value) {
    map.value.off('click', handleMapClick)
    map.value.destroy()
  }
})

const mainWidth = ref(1591)
const mainHeight = ref(360)

onMounted(async () => {
  if (window.innerWidth) {
    mainWidth.value = window.innerWidth
  }
  if (window.innerHeight) {
    mainHeight.value = window.innerHeight
  }
  getStationOption()
  await getScaleList()
  await getPoints()
  loadMap()
})
</script>

<style>
/* 流动动画替代闪烁 */
@keyframes flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -30; /* 根据虚线样式调整 */
  }
}
</style>

<style scoped>
/* 缩放动画 */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
.analysis-container {
  min-height: 300px;
}

/* 打字机效果优化 */
.analysis-container span {
  display: inline-block;
  white-space: pre-wrap;
  word-break: break-word;
}
.station-card {
  --header-bg: #f8fafc;
  --border-color: #e2e8f0;
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.title {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
}

.coord {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  padding: 0 16px 16px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.data-item:hover {
  transform: translateY(-2px);
}

.data-content {
  flex: 1;
}

label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.unit {
  font-size: 14px;
  color: #94a3b8;
  margin-left: 4px;
}

.ph-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

.ph-tag.alkaline {
  background: #f8d7da;
  color: #721c24;
}

.ph-tag.neutral {
  background: #d4edda;
  color: #155724;
}

.ph-tag.acidic {
  background: #fff3cd;
  color: #856404;
}
#map-container {
  width: 100%;
  height: 100vh;
}

.custom-content-marker {
  position: relative;
  width: 25px;
  height: 34px;
}

.custom-content-marker img {
  width: 100%;
  height: 100%;
}

.custom-content-marker .close-btn {
  position: absolute;
  top: -6px;
  right: -8px;
  width: 15px;
  height: 15px;
  font-size: 12px;
  background: #ccc;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 15px;
  box-shadow: -1px 1px 1px rgba(10, 10, 10, 0.2);
}

.custom-content-marker .close-btn:hover {
  background: #666;
}

::v-global(#map-container) {
  width: 100%;
  height: 100vh;
}

::v-global(.custom-marker) {
  position: relative;
  text-align: center;
}

::v-global(.custom-marker .label) {
  display: flex;
  align-items: center;
  background: white;
  padding: 4px 8px 4px 6px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  transform: translateX(-50%);
  font-size: 14px;
  line-height: 1;
}

::v-global(.custom-marker .label .icon) {
  margin-right: 4px;
  font-size: 16px;
}

::v-global(.custom-marker .label .text) {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

::v-global(.custom-marker .pin) {
  width: 20px;
  height: 20px;
  background: #2b8cbe;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg) translateX(-50%);
  margin: -10px auto 0;
}

::v-global(.center-marker .pin) {
  background: #ff6b6b;
}

::v-global(.custom-info-window .title) {
  font-weight: bold;
  margin-bottom: 4px;
  color: #2b8cbe;
}

::v-global(.custom-info-window .coord) {
  font-size: 12px;
  color: #666;
}

::v-global(.custom-marker .info-icon) {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 12px;
  background: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

::v-global(.amap-polygon) {
  stroke-dasharray: 5 5;
  stroke-linejoin: round;
  animation: dash 30s linear infinite;
}

@keyframes dash {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}
</style>

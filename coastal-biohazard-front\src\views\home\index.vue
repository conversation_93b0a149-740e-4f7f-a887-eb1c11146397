<template>
  <AppPage show-footer>
    <NSpin :show="loading" description="数据加载中...">
      <div class="dashboard-container">
        <n-card class="analysis-card" :bordered="false">
          <!-- 头部区域 -->
          <div class="card-header">
            <div class="title-group">
              <NH2 prefix="bar" class="dashboard-title">
                <NGradientText type="info" size="24px">
                  🌊 海洋环境监测分析平台
                </NGradientText>
              </NH2>
              <div style="position: absolute; right: 24px; top: 32px">
                <NSpace justify="end">
                  <NButton type="info" ghost @click="refreshData">
                    <template #icon>
                      <NIcon :component="RefreshOutline" />
                    </template>
                    刷新数据
                  </NButton>
                </NSpace>
              </div>

              <NText depth="3" class="subtitle"> 实时监测与数据分析 </NText>
            </div>
            <n-select
              v-model:value="scaleId"
              class="station-select"
              size="large"
              :options="stationOption"
              label-field="name"
              value-field="id"
              placeholder="选择监测站点"
              clearable
              filterable
              @update:value="changeOption"
            >
              <template #arrow>
                <NIcon :component="CompassOutline" />
              </template>
            </n-select>
          </div>

          <!-- 图表区域 -->
          <div class="chart-group mt-16">
            <n-card
              class="chart-card"
              title="🌡️ 水文环境指标"
              :segmented="{ content: true }"
            >
              <template #header-extra>
                <NSpace>
                  <n-select
                    v-model:value="selectedMonth"
                    :options="monthOptions"
                    placeholder="选择月份"
                    size="small"
                    style="width: 120px"
                    @update:value="handleMonthChange"
                  />
                  <NTag type="info" size="small" round> 年度最新数据 </NTag>
                </NSpace>
              </template>

              <div class="chart-wrapper">
                <VChart style="height: 500px" :option="swOption" autoresize />
              </div>

              <!-- 这里新增站点标签 -->
              <div
                class="station-labels"
                style="
                  text-align: center;
                  margin: 8px 0 0 0;
                  font-size: 13px;
                  color: #888;
                "
              >
                <template
                  v-if="
                    swOption.xAxis &&
                    swOption.xAxis[0] &&
                    swOption.xAxis[0].data &&
                    swOption.xAxis[0].data.length
                  "
                >
                  站点：{{ swOption.xAxis[0].data.join("，") }}
                </template>
              </div>

              <template #footer>
                <NText depth="3" class="footnote">
                  注：水温单位为°C，透明度单位为m，盐度和pH为无量纲
                </NText>
              </template>
            </n-card>

            <n-card
              class="chart-card"
              title="🧪 海水化学指标"
              :segmented="{ content: true }"
            >
              <template #header-extra>
                <NTag type="info" size="small" round> 实验室分析 </NTag>
              </template>
              <div class="chart-wrapper">
                <VChart style="height: 500px" :option="option" autoresize />
              </div>
              <template #footer>
                <NText depth="3" class="footnote">
                  注：所有化学指标单位均为mg/L
                </NText>
              </template>
            </n-card>
          </div>

          <!-- 数据分析摘要 -->
          <n-card
            class="analysis-summary"
            title="📊 监测数据分析摘要"
            :segmented="{ content: true }"
          >
            <!-- 新增：站点和月份选择 -->
            <!--            <div-->
            <!--              style="-->
            <!--                display: flex;-->
            <!--                justify-content: flex-end;-->
            <!--                gap: 16px;-->
            <!--                margin-bottom: 12px;-->
            <!--              "-->
            <!--            >-->
            <!--              <n-select-->
            <!--                v-model:value="scaleId"-->
            <!--                :options="stationOption"-->
            <!--                label-field="name"-->
            <!--                value-field="id"-->
            <!--                placeholder="选择监测站点"-->
            <!--                size="small"-->
            <!--                style="width: 180px"-->
            <!--                @update:value="changeOption"-->
            <!--                clearable-->
            <!--                filterable-->
            <!--              />-->
            <!--              <n-select-->
            <!--                v-model:value="selectedMonth"-->
            <!--                :options="monthOptions"-->
            <!--                placeholder="选择月份"-->
            <!--                size="small"-->
            <!--                style="width: 120px"-->
            <!--                @update:value="handleMonthChange"-->
            <!--              />-->
            <!--            </div>-->
            <NGrid x-gap="24" y-gap="16" cols="1 s:1 m:2 l:2">
              <NGi v-for="(item, index) in fieldDescription" :key="index">
                <NAlert
                  :type="item.fluctuated ? 'info' : 'success'"
                  :bordered="false"
                  class="analysis-item"
                >
                  <template #icon>
                    <NIcon :color="item.fluctuated ? '#2080F0' : '#18A058'">
                      <PulseOutline v-if="item.fluctuated" />
                      <TrendingUpOutline v-else />
                    </NIcon>
                  </template>
                  <div class="analysis-content">
                    <NText strong>
                      {{ item.fieldName }}
                    </NText>
                    <span v-if="item.fluctuated">
                      呈现显著波动：
                      <NTag type="primary" size="small" round class="value-tag">
                        <template #icon>
                          <NIcon :component="ArrowUpOutline" />
                        </template>
                        峰值 {{ item.maxValue }}{{ getUnit(item.fieldName) }}
                      </NTag>
                      于{{ item.maxStationName }}，
                      <NTag type="warning" size="small" round class="value-tag">
                        <template #icon>
                          <NIcon :component="ArrowDownOutline" />
                        </template>
                        谷值 {{ item.minValue }}{{ getUnit(item.fieldName) }}
                      </NTag>
                      于{{ item.minStationName }}
                    </span>
                    <span v-else> 保持稳定 </span>
                    ，平均值为
                    <NText
                      :type="item.fluctuated ? 'primary' : 'success'"
                      strong
                    >
                      {{ item.averageValue }}{{ getUnit(item.fieldName) }}
                    </NText>
                  </div>
                </NAlert>
              </NGi>
            </NGrid>
          </n-card>
        </n-card>
      </div>
    </NSpin>
  </AppPage>
</template>

<script setup>
import { AppPage } from '@/components/index.js'
import {
  ArrowDownOutline,
  ArrowUpOutline,
  CompassOutline,
  PulseOutline,
  RefreshOutline,
  TrendingUpOutline,
} from '@vicons/ionicons5'
import {
  BarChart,
  LineChart,
} from 'echarts/charts'
import {
  DataZoomComponent,
  GridComponent,
  LegendComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  VisualMapComponent,
} from 'echarts/components'
import * as echarts from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import {
  NAlert,
  NButton,
  NGi,
  NGradientText,
  NGrid,
  NH2,
  NIcon,
  NSpace,
  NSpin,
  NTag,
  NText,
} from 'naive-ui'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import VChart from 'vue-echarts'
import api from './api'

// 初始化ECharts组件
echarts.use([
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  TitleComponent,
  ToolboxComponent,
  DataZoomComponent,
  VisualMapComponent,
])

// 响应式数据
const loading = ref(false)
const scaleId = ref()
const organizedData = ref([])
const chemicalIonList = ref([])
const fieldDescription = ref([])
const stationOption = ref([])
const selectedMonth = ref(new Date().getMonth() + 1) // 默认当前月份
const monthOptions = ref([
  { label: '1月', value: 1 },
  { label: '2月', value: 2 },
  { label: '3月', value: 3 },
  { label: '4月', value: 4 },
  { label: '5月', value: 5 },
  { label: '6月', value: 6 },
  { label: '7月', value: 7 },
  { label: '8月', value: 8 },
  { label: '9月', value: 9 },
  { label: '10月', value: 10 },
  { label: '11月', value: 11 },
  { label: '12月', value: 12 },
])
// 获取单位
function getUnit(fieldName) {
  const units = {
    '气温': '°C',
    '水温': '°C',
    '盐度': '',
    'pH': '',
    '透明度': 'm',
    '活性磷酸盐': 'mg/L',
    '亚硝酸盐-氮': 'mg/L',
    '硝酸盐-氮': 'mg/L',
    '氨-氢': 'mg/L',
  }
  return units[fieldName] || ''
}

// 修改图表配置 - 海水化学分析
const option = ref({
  color: ['#36A3F7', '#34D160', '#FFC233', '#FF6B6B'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    formatter: (params) => {
      let result = `<div style="font-weight:bold;margin-bottom:5px">${params[0].name}</div>`
      params.forEach((item) => {
        result += `
          <div style="display:flex;align-items:center;margin:3px 0">
            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background:${item.color};margin-right:5px"></span>
            ${item.seriesName}: <b style="margin-left:5px">${item.value} mg/L</b>
          </div>
        `
      })
      return result
    },
  },
  legend: {
    data: ['活性磷酸盐', '亚硝酸盐-氮', '硝酸盐-氮', '氨-氢'],
    top: 'top',
    itemGap: 20,
    textStyle: {
      fontSize: 12,
    },
  },
  toolbox: {
    show: true,
    right: 20,
    feature: {
      magicType: {
        show: true,
        type: ['bar', 'stack'],
        title: {
          bar: '切换柱状图',
          stack: '切换堆叠图',
        },
      },
      saveAsImage: {
        show: true,
        title: '保存图片',
        pixelRatio: 2,
      },
    },
  },
  grid: {
    containLabel: true,
    left: '3%',
    right: '4%',
    bottom: '12%',
    top: '15%',
  },
  xAxis: [
    {
      type: 'category',
      // 初始可以设为空数组或默认数据
      data: [],
      axisTick: { show: false },
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 12,
        formatter: (value) => {
          if (!value)
            return ''
          const parts = value.split(' ')
          // 添加安全性检查
          return parts.length >= 3
            ? `${parts[0]}\n${parts[1]} ${parts[2]}`
            : value
        },
      },
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
    },
  ],
  yAxis: {
    type: 'value',
    name: '含量 (mg/L)',
    nameTextStyle: {
      padding: [0, 0, 0, 40],
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#f0f0f0',
      },
    },
  },
  series: [
    {
      name: '活性磷酸盐',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      emphasis: {
        itemStyle: {
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          shadowBlur: 8,
        },
      },
      data: [],
    },
    {
      name: '亚硝酸盐-氮',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
    {
      name: '硝酸盐-氮',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
    {
      name: '氨-氢',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
  ],
})

// 图表配置 - 水文环境分析
const swOption = ref({
  color: ['#6F7BF7', '#9F44D3', '#FFA941', '#4CD964'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      crossStyle: {
        color: '#999',
      },
    },
    formatter: (params) => {
      let result = `<div style="font-weight:bold;margin-bottom:5px">${params[0].name}</div>`
      params.forEach((item) => {
        const unit = item.seriesName === '水温'
          ? '°C'
          : item.seriesName === '透明度' ? 'm' : ''
        result += `
          <div style="display:flex;align-items:center;margin:3px 0">
            <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background:${item.color};margin-right:5px"></span>
            ${item.seriesName}: <b style="margin-left:5px">${item.value} ${unit}</b>
          </div>
        `
      })
      return result
    },
  },
  legend: {
    data: ['透明度', '盐度', 'pH', '水温'],
    top: 'top',
    itemGap: 20,
    textStyle: {
      fontSize: 12,
    },
  },
  toolbox: {
    show: true,
    right: 20,
    feature: {
      magicType: {
        show: true,
        type: ['line', 'bar'],
        title: {
          line: '切换折线图',
          bar: '切换柱状图',
        },
      },
      saveAsImage: {
        show: true,
        title: '保存图片',
        pixelRatio: 2,
      },
    },
  },
  grid: {
    containLabel: true,
    left: '3%',
    right: '4%',
    bottom: '12%',
    top: '15%',
  },
  xAxis: [
    {
      type: 'category',
      data: [],
      axisTick: { show: false },
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 12,
        formatter: (value) => {
          const parts = value.split(' ')
          return `${parts[0]}\n${parts[1]} ${parts[2]}`
        },
      },
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
    },
  ],
  yAxis: [
    {
      type: 'value',
      name: '指标值',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: '#f0f0f0',
        },
      },
    },
    {
      type: 'value',
      name: '温度 (°C)',
      splitLine: { show: false },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#4CD964',
        },
      },
      axisLabel: {
        color: '#4CD964',
      },
    },
  ],
  series: [
    {
      name: '透明度',
      type: 'bar',
      barGap: '10%',
      barCategoryGap: '30%',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
    {
      name: '盐度',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
    {
      name: 'pH',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
    {
      name: '水温',
      type: 'bar',
      barWidth: 18,
      itemStyle: {
        borderRadius: [4, 4, 0, 0],
        shadowColor: 'rgba(0, 0, 0, 0.1)',
        shadowBlur: 4,
      },
      data: [],
    },
  ],
})

// 添加状态变量
const chartInitialized = ref(false)
const chartError = ref(null)

// 修改数据获取方法
async function getOrganizedData() {
  try {
    loading.value = true
    chartError.value = null
    if (scaleId.value == null) {
      scaleId.value = stationOption.value[0]?.id
    }

    if (scaleId.value) {
      const { data } = await api.getOrganizedData(scaleId.value, selectedMonth.value)

      if (data.waterPhWeatherDataList==null) {
        window.$message.warning(`当前调查中心${selectedMonth.value}月暂无最新监测数据`)
      }

      organizedData.value = JSON.parse(JSON.stringify(data.waterPhWeatherDataList))
      fieldDescription.value = JSON.parse(JSON.stringify(data.fieldDescriptionList || []))
      chemicalIonList.value = JSON.parse(JSON.stringify(data.chemicalIonList || []))

      if (!chartInitialized.value) {
        await nextTick()
        chartInitialized.value = true
      }

      updateChartsData()
    }
    else {
      $message.warning('请选择调查中心')
    }
  }
  catch (error) {
    console.error('数据加载失败:', error)
    chartError.value = error
    resetCharts()
  }
  finally {
    loading.value = false
  }
}

// 新增方法：更新图表数据
function updateChartsData() {
  try {
    // 化学分析图表数据更新
    // const chemicalOption = JSON.parse(JSON.stringify(option.value))
    option.value.xAxis[0].data = chemicalIonList.value.map(
      item => item?.distributeName ? `站点 ${item.distributeName} ${item.sampleLayer === 2 ? '表层' : '底层'}` : '',
    ).filter(Boolean)

    option.value.series[0].data = chemicalIonList.value.map(item => item?.activePhosphate || 0)
    option.value.series[1].data = chemicalIonList.value.map(item => item?.nitriteNitrogen || 0)
    option.value.series[2].data = chemicalIonList.value.map(item => item?.nitrateNitrogen || 0)
    option.value.series[3].data = chemicalIonList.value.map(item => item?.ammoniaHydrogen || 0)
    // option.value = chemicalOption

    // 水文分析图表数据更新
    // const swOptionData = JSON.parse(JSON.stringify(swOption.value))
    console.log(organizedData.value);
    swOption.value.xAxis[0].data = organizedData.value.map(
      item => item?.distributeId ? `站点 ${item.distributeId} ${item.sampleLayer === 2 ? '表层' : '底层'}` : '',
    ).filter(Boolean)

    swOption.value.series[0].data = organizedData.value.map(item => item?.transparentExtent || 0)
    swOption.value.series[1].data = organizedData.value.map(item => item?.saltExtent || 0)
    swOption.value.series[2].data = organizedData.value.map(item => item?.phExtent || 0)
    swOption.value.series[3].data = organizedData.value.map(item => item?.waterTemperature || 0)
    // swOption.value = swOptionData
  }
  catch (error) {
    console.error('图表更新失败:', error)
    // window.$message.error('图表渲染失败')
    resetCharts()
  }
}

// 修改重置图表方法
function resetCharts() {
  const emptyData = Array.from({ length: 5 }).fill(0)
  const emptyLabels = Array.from({ length: 5 }).fill('暂无数据')

  option.value = {
    ...option.value,
    xAxis: [{ ...option.value.xAxis[0], data: emptyLabels }],
    series: option.value.series.map(series => ({
      ...series,
      data: emptyData,
    })),
  }

  swOption.value = {
    ...swOption.value,
    xAxis: [{ ...swOption.value.xAxis[0], data: emptyLabels }],
    series: swOption.value.series.map(series => ({
      ...series,
      data: emptyData,
    })),
  }
}

async function getScaleOption() {
  try {
    const { data } = await api.getStationPoints()
    stationOption.value = data
  }
  catch (error) {
    $message.error('获取站点列表失败')
  }
}

function changeOption(value) {
  scaleId.value = value
  getOrganizedData()
}

function refreshData() {
  getOrganizedData()
  $message.success('数据已刷新')
}

// 响应式调整
function handleResize() {
  option.value = { ...option.value }
  swOption.value = { ...swOption.value }
}

// 统一报告状态管理
const reportState = ref({
  loading: false,
  visible: false,
  rawContent: '',
  date: new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }),
  error: null,
})

// 计算属性
// const processedContent = computed(() => {
//   if (!reportState.value.rawContent)
//     return ''
//   return formatReportContent(reportState.value.rawContent)
// })

// 观察数据变化自动更新
watchEffect(() => {
  reportState.value.date = new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })
})

// 数据整合方法（带缓存）
let cachedData = null
function getCurrentData() {
  if (cachedData)
    return cachedData

  cachedData = {
    metadata: {
      reportTime: new Date().toISOString(),
      stationId: scaleId.value,
      stationName: stationOption.value.find(s => s.id === scaleId.value)?.name || '未知站点',
    },
    hydrology: organizedData.value.map(formatHydrologyData),
    chemistry: chemicalIonList.value.map(formatChemistryData),
    keyIndicators: fieldDescription.value.map(formatIndicatorData),
    // biology: formatBiologyData(fdCharts.value),
  }

  return cachedData
}

// 数据格式化方法
function formatHydrologyData(item) {
  return {
    station: item.distributeName,
    layer: item.sampleLayer === 2 ? '表层' : '底层',
    transparency: item.transparentExtent,
    salinity: item.saltExtent,
    pH: item.phExtent,
    waterTemp: item.waterTemperature,
    airTemp: item.airTemperature,
  }
}

function formatChemistryData(item) {
  return {
    station: item.distributeName,
    layer: item.sampleLayer === 2 ? '表层' : '底层',
    activePhosphate: item.activePhosphate,
    nitriteNitrogen: item.nitriteNitrogen,
    nitrateNitrogen: item.nitrateNitrogen,
    ammoniaNitrogen: item.ammoniaHydrogen,
  }
}

function formatIndicatorData(item) {
  return {
    name: item.fieldName,
    maxValue: item.maxValue,
    minValue: item.minValue,
    avgValue: item.averageValue,
    maxStation: item.maxStationName,
    minStation: item.minStationName,
    hasFluctuation: item.fluctuated,
  }
}

// function formatBiologyData(charts) {
//   return charts.flatMap(chart =>
//     chart.option.series[0].data.map((d) => ({
//       type: chart.title,
//       name: d.name,
//       value: d.value,
//       unit: d.name.includes('ind./L') ? 'ind./L' : 'ind./50g',
//     })),
//   )
// }

// 修改月份变化处理函数
function handleMonthChange(month) {
  selectedMonth.value = month
  getOrganizedData()
}

onMounted(async () => {
  try {
    await getScaleOption()
    await getOrganizedData()
    window.addEventListener('resize', handleResize)
  }
  catch (error) {
    console.error('初始化失败:', error)
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.report-modal {
  border-radius: 12px;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.report-subtitle {
  display: flex;
  align-items: center;
  gap: 16px;
}

.content-box {
  padding: 24px;
  max-width: 700px;
  margin: 0 auto;
}

/* 纸张样式 */
.paper-texture {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 24px;
  background-image: linear-gradient(
      rgba(245, 245, 245, 0.5),
      rgba(245, 245, 245, 0.5)
  ),
  radial-gradient(
      circle at 20% 30%,
      rgba(255, 255, 255, 0.1) 45%,
      transparent 50%
  ),
  radial-gradient(
      circle at 80% 70%,
      rgba(255, 255, 255, 0.1) 45%,
      transparent 50%
  );
  background-size: 200% 200%, 20px 20px, 20px 20px;
}

/* 全局字体样式 */
//::v-global(body) {
//  font-family: 'Georgia', 'Times New Roman', serif;
//  line-height: 1.6;
//  color: #333;
//}

/* 深度样式覆盖 */
::v-deep(.content-box) {
  font-size: 16px;
  line-height: 1.8;
  word-break: break-all;
  hyphens: auto;
}

::v-deep(.report-spinner .n-spin__description) {
  font-family: "Georgia", "Times New Roman", serif;
  color: #666;
  height: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .content-box {
    padding: 16px;
    font-size: 14px;
  }
}
.dashboard-container {
  @apply h-full p-4 bg-gray-50 dark:bg-dark-800;

  .analysis-card {
    @apply rounded-xl shadow-sm dark:bg-dark-700;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);

    .card-header {
      @apply flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4;

      .title-group {
        @apply flex flex-col;

        .dashboard-title {
          @apply m-0 text-2xl font-bold;

          :deep(.n-h2-prefix) {
            @apply text-primary;
          }
        }

        .subtitle {
          @apply text-sm mt-1;
        }
      }

      .station-select {
        @apply w-full sm:w-72;

        :deep(.n-base-selection-label) {
          @apply font-medium;
        }
      }
    }

    .chart-group {
      @apply grid grid-cols-1 lg:grid-cols-2 gap-6;

      .chart-card {
        @apply rounded-lg overflow-hidden border border-gray-100 dark:border-dark-600;
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          transform: translateY(-2px);
        }

        :deep(.n-card-header) {
          @apply bg-gray-50 dark:bg-dark-700 px-6 py-4 border-b border-gray-100 dark:border-dark-600;
        }

        .chart-wrapper {
          @apply h-96;
        }

        .footnote {
          @apply text-xs;
        }
      }
    }

    .analysis-summary {
      @apply mt-6 border border-gray-100 dark:border-dark-600;

      :deep(.n-card-header) {
        @apply bg-gray-50 dark:bg-dark-700 px-6 py-4 border-b border-gray-100 dark:border-dark-600;
      }

      .analysis-item {
        @apply rounded-lg;

        .analysis-content {
          @apply text-sm leading-relaxed text-gray-700 dark:text-gray-300;
        }

        .value-tag {
          @apply mx-1;
        }
      }
    }

    .action-footer {
      @apply mt-6 pt-4 border-t border-gray-100 dark:border-dark-600;
    }
  }
}

@media (max-width: 640px) {
  .dashboard-container {
    @apply p-2;

    .analysis-card {
      @apply rounded-lg;

      .chart-group {
        @apply gap-4;

        .chart-card {
          .chart-wrapper {
            @apply h-72;
          }
        }
      }
    }
  }
}
</style>

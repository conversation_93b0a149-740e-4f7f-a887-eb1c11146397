package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 时空范围点位表（单点）(StationPointScale)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:40:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StationPointScaleRequest extends PageRequest {
    private String name;
    
public Serializable pkVal() {
          return null;
      }
}



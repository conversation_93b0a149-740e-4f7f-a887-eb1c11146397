-- =====================================================
-- 修复QDW站点生物多样性指数数据
-- =====================================================

-- 更新浮游植物的生物多样性指数（distribute_id = 7, type = 0）
UPDATE `biodiversity` SET 
    `h_index_min` = 1.28,
    `h_index_max` = 3.45, 
    `h_avg` = 2.38,
    `j_index_min` = 0.68,
    `j_index_max` = 0.92,
    `j_avg` = 0.81,
    `d_index_min` = 0.22,
    `d_index_max` = 0.88,
    `d_avg` = 0.55,
    `description` = 'QDW站点浮游植物群落结构复杂，以大型链状硅藻为优势种群，春季繁殖活跃，生物多样性指数高，群落稳定性良好。检测到多种指示性藻种，显示海域营养状态适中。'
WHERE `distribute_id` = 7 AND `type` = 0;

-- 如果浮游植物记录不存在，则插入新记录
INSERT IGNORE INTO `biodiversity` 
(`distribute_id`, `type`, `name`, `h_index_min`, `h_index_max`, `h_avg`, `j_index_min`, `j_index_max`, `j_avg`, `d_index_min`, `d_index_max`, `d_avg`, `description`, `abundance`, `biodiversity`) 
VALUES 
(7, 0, '具槽帕拉藻(Paralia sulcata)、离心海链藻(Thalassiosira excentrica)、斯氏几内亚藻(Guinardia striata)、菱形海线藻(Thalassionema nitzschioides)', 
 1.28, 3.45, 2.38, 0.68, 0.92, 0.81, 0.22, 0.88, 0.55, 
 'QDW站点浮游植物群落结构复杂，以大型链状硅藻为优势种群，春季繁殖活跃，生物多样性指数高，群落稳定性良好。检测到多种指示性藻种，显示海域营养状态适中。', 
 '125.8×10^4', 2.150);

-- 更新浮游动物的生物多样性指数（distribute_id = 7, type = 1）
UPDATE `biodiversity` SET 
    `h_index_min` = 1.15,
    `h_index_max` = 3.22,
    `h_avg` = 2.28,
    `j_index_min` = 0.62,
    `j_index_max` = 0.89,
    `j_avg` = 0.76,
    `d_index_min` = 0.18,
    `d_index_max` = 0.82,
    `d_avg` = 0.51,
    `description` = 'QDW站点浮游动物群落以小型桡足类为主导，毛颚类和糠虾类为重要组成部分。群落垂直分布明显，昼夜迁移活跃。生物量季节变化显著，与浮游植物群落耦合度高。'
WHERE `distribute_id` = 7 AND `type` = 1;

-- 更新底栖生物的生物多样性指数（distribute_id = 7, type = 2）  
UPDATE `biodiversity` SET 
    `h_index_min` = 1.32,
    `h_index_max` = 3.18,
    `h_avg` = 2.42,
    `j_index_min` = 0.71,
    `j_index_max` = 0.91,
    `j_avg` = 0.82,
    `d_index_min` = 0.25,
    `d_index_max` = 0.85,
    `d_avg` = 0.58,
    `description` = 'QDW站点底栖生物群落以环节动物为绝对优势，甲壳类和软体动物次之。群落结构稳定，功能群完整，具有典型的温带海域底栖群落特征。生物扰动活跃，对沉积物再悬浮有重要影响。'
WHERE `distribute_id` = 7 AND `type` = 2;

-- 插入游泳动物的生物多样性指数（如果不存在）
INSERT IGNORE INTO `biodiversity` 
(`distribute_id`, `type`, `name`, `h_index_min`, `h_index_max`, `h_avg`, `j_index_min`, `j_index_max`, `j_avg`, `d_index_min`, `d_index_max`, `d_avg`, `description`, `abundance`, `biodiversity`) 
VALUES 
(7, 4, '硬骨鱼类(鲱科、鲽科、虾虎鱼科)、软骨鱼类、头足类(枪乌贼属)、大型甲壳类(对虾科、蟹科)', 
 0.95, 2.85, 1.92, 0.48, 0.78, 0.63, 0.15, 0.72, 0.44, 
 'QDW站点游泳动物群落以小型经济鱼类为主，季节性洄游种类丰富。群落结构受水团性质影响明显，渔业资源状况良好，生态系统功能完整。', 
 '85.6', NULL);

-- 验证更新结果
SELECT 
    distribute_id as '站点ID',
    CASE type 
        WHEN 0 THEN '浮游植物'
        WHEN 1 THEN '浮游动物' 
        WHEN 2 THEN '底栖生物'
        WHEN 4 THEN '游泳动物'
        ELSE '未知类型'
    END as '群落类型',
    h_avg as 'Shannon指数',
    j_avg as 'Pielou指数', 
    d_avg as 'Margalef指数',
    abundance as '丰度'
FROM `biodiversity` 
WHERE `distribute_id` = 7 
ORDER BY `type`; 
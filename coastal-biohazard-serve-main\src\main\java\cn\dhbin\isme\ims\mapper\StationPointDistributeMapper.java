package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 调查站位表(StationPointDistribute)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:38:31
 */
@Mapper
public interface StationPointDistributeMapper extends BaseMapper<StationPointDistribute> {
    @Select("SELECT * FROM station_point_distribute WHERE name = #{name}")
    StationPointDistribute selectByName(String name);
}


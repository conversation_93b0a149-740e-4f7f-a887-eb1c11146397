package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.entity.Permission;
import cn.dhbin.isme.pms.domain.request.CreatePermissionRequest;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:01:57+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CreatePermissionRequestToPermissionImpl implements CreatePermissionRequestToPermission {

    @Override
    public Permission to(CreatePermissionRequest arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Permission permission = new Permission();

        permission.setCode( arg0.getCode() );
        permission.setComponent( arg0.getComponent() );
        permission.setDescription( arg0.getDescription() );
        permission.setEnable( arg0.getEnable() );
        permission.setIcon( arg0.getIcon() );
        permission.setLayout( arg0.getLayout() );
        permission.setMethod( arg0.getMethod() );
        permission.setName( arg0.getName() );
        permission.setOrder( arg0.getOrder() );
        permission.setParentId( arg0.getParentId() );
        permission.setPath( arg0.getPath() );
        permission.setRedirect( arg0.getRedirect() );
        permission.setShow( arg0.getShow() );
        permission.setType( arg0.getType() );

        return permission;
    }
}

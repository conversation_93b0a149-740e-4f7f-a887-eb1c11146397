package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.MorphologicalAnalysisDataMapper;
import cn.dhbin.isme.ims.service.MorphologicalAnalysisDataService;
import cn.dhbin.isme.ims.service.AnalysisOfBiologicalFactorsService;
import cn.dhbin.isme.ims.domain.entity.AnalysisOfBiologicalFactors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 形态分析表(MorphologicalAnalysisData)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-29 12:50:57
 */
@Service("morphologicalAnalysisDataService")
public class MorphologicalAnalysisDataServiceImpl extends ServiceImpl<MorphologicalAnalysisDataMapper, MorphologicalAnalysisData> implements MorphologicalAnalysisDataService {

    @Autowired
    private MorphologicalAnalysisDataMapper morphologicalAnalysisDataMapper;

    @Autowired
    private AnalysisOfBiologicalFactorsService analysisOfBiologicalFactorsService;

    @Override
    public Page<MorphologicalAnalysisData> queryPage(MorphologicalAnalysisDataRequest request) {
        IPage<MorphologicalAnalysisData> qp = request.toPage();
        LambdaQueryWrapper<MorphologicalAnalysisData> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getAbundanceId() != null) {
            queryWrapper.eq(MorphologicalAnalysisData::getAbundanceId, request.getAbundanceId());
        }

        IPage<MorphologicalAnalysisData> ret = morphologicalAnalysisDataMapper.selectPage(qp, queryWrapper);


        return Page.convert(ret);
    }

    @Override
    public List<MorphologicalAnalysisData> getByStationId(Integer stationId) {
        // 首先获取该站点的所有微观繁殖体分析记录
        LambdaQueryWrapper<AnalysisOfBiologicalFactors> analysisWrapper = new LambdaQueryWrapper<>();
        analysisWrapper.eq(AnalysisOfBiologicalFactors::getDistributeId, stationId);
        List<AnalysisOfBiologicalFactors> analysisList = analysisOfBiologicalFactorsService.list(analysisWrapper);

        if (analysisList.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有abundance_id
        List<Integer> abundanceIds = analysisList.stream()
                .map(AnalysisOfBiologicalFactors::getId)
                .collect(Collectors.toList());

        // 根据abundance_id获取形态分析数据
        LambdaQueryWrapper<MorphologicalAnalysisData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(MorphologicalAnalysisData::getAbundanceId, abundanceIds);
        queryWrapper.orderByDesc(MorphologicalAnalysisData::getCreateTime);

        return morphologicalAnalysisDataMapper.selectList(queryWrapper);
    }
}


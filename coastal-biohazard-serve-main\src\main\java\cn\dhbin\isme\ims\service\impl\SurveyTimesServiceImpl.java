package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.SurveyTimes;
import cn.dhbin.isme.ims.mapper.SurveyTimesMapper;
import cn.dhbin.isme.ims.service.SurveyTimesService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调查次数服务实现
 */
@Service
@RequiredArgsConstructor
public class SurveyTimesServiceImpl extends ServiceImpl<SurveyTimesMapper, SurveyTimes> 
        implements SurveyTimesService {

    @Override
    public List<SurveyTimes> getSurveyTimesByTaskId(Integer taskId) {
        LambdaQueryWrapper<SurveyTimes> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SurveyTimes::getTaskId, taskId)
                   .orderByAsc(SurveyTimes::getTimes);
        return list(queryWrapper);
    }
} 
package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.CoordinateDto;
import cn.dhbin.isme.ims.domain.dto.StationPointDistributeDto;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.StationPointDistributeRequest;
import cn.dhbin.isme.ims.service.StationPointDistributeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/station-point-distribute")
@RequiredArgsConstructor
public class StationPointDistributeController {
    private final StationPointDistributeService stationPointDistributeService;

    @GetMapping("/list")
    public R<List<?>> listStationPoints(@RequestParam(defaultValue = "0") Integer scaleId) {
        List<StationPointDistribute> stationPoints;

        if (scaleId == 0) {
            // 如果 scale_id 为 0，返回所有记录
            stationPoints = stationPointDistributeService.list();
        } else {
            // 如果 scale_id 不为 0，根据 scale_id 进行筛选
            LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StationPointDistribute::getScaleId, scaleId);
            stationPoints = stationPointDistributeService.list(queryWrapper);
        }

        return R.ok(stationPoints);
    }

    @GetMapping("/{distributeId}")
    public R<?> getStationPoint(@PathVariable("distributeId") Integer distributeId) {
        StationPointDistribute byId = stationPointDistributeService.getById(distributeId);
        CoordinateDto coordinateDto = new CoordinateDto();
        coordinateDto.setLatitude(byId.getLatitude());
        coordinateDto.setLongitude(byId.getLongitude());
        return R.ok(coordinateDto);
    }

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<StationPointDistributeDto>> selectAll(StationPointDistributeRequest request) {
        Page<StationPointDistributeDto> ret = stationPointDistributeService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody StationPointDistribute data) {
//        if (data.getRange() != null && data.getRange().size() == 2) {
//            long startTime = data.getRange().get(0);
//            long endTime = data.getRange().get(1);
//
//            // 转换时间戳为日期
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date beforeInvestigate = new Date(startTime);
//            Date afterInvestigate = new Date(endTime);
//
//            // 设置 beforeInvestigate 和 afterInvestigate
//            data.setBeforeInvestigate(beforeInvestigate);
//            data.setAfterInvestigate(afterInvestigate);
//        }

        stationPointDistributeService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody StationPointDistribute data) {
        data.setCreateTime(new Date());
        // 处理 range 字段，转换为正常的时间格式
//        if (data.getRange() != null && data.getRange().size() == 2) {
//            long startTime = data.getRange().get(0);
//            long endTime = data.getRange().get(1);
//
//            // 转换时间戳为日期
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date beforeInvestigate = new Date(startTime);
//            Date afterInvestigate = new Date(endTime);
//
//            // 设置 beforeInvestigate 和 afterInvestigate
//            data.setBeforeInvestigate(beforeInvestigate);
//            data.setAfterInvestigate(afterInvestigate);
//        }

        stationPointDistributeService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        try {
            stationPointDistributeService.removeById(id);
            return R.ok();
        } catch (BizException e) {
            return R.build(new BizException(e.getCode(),e.getMessage()));
        }
    }
}

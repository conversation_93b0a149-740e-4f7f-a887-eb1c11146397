package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.SelectOptionDto;
import cn.dhbin.isme.ims.domain.dto.StationPointDistributeDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.domain.request.StationPointDistributeRequest;
import cn.dhbin.isme.ims.mapper.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.service.StationPointDistributeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调查站位表(StationPointDistribute)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:38:31
 */
@Service("stationPointDistributeService")
public class StationPointDistributeServiceImpl extends ServiceImpl<StationPointDistributeMapper, StationPointDistribute> implements StationPointDistributeService {

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Autowired
    private StationPointScaleMapper setStationPointDistribute;

    @Autowired
    private ChemicalIonMapper chemicalIonMapper;

    @Autowired
    private SurveyTimeRangeMapper surveyTimeRangeMapper;

    @Autowired
    private WaterPhWeatherDataMapper waterPhWeatherDataMapper;

    @Autowired
    private BiodiversityMapper biodiversityMapper;

    @Autowired
    private SurveyRouteTaskMapper surveyRouteTaskMapper;

    @Override
    public List<SelectOptionDto> listStationPoints() {
        List<StationPointDistribute> stationPoints = stationPointDistributeMapper.selectList(null);
        return stationPoints.stream()
                .map(stationPoint -> new SelectOptionDto(stationPoint.getId(), stationPoint.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<StationPointDistributeDto> queryPage(StationPointDistributeRequest request) {
        IPage<StationPointDistribute> qp = request.toPage();
        LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();

//        queryWrapper.groupBy(StationPointDistribute::getId);

        if (request.getName() != null) {
            queryWrapper.like(StationPointDistribute::getName, request.getName());
        }
        if (request.getScaleId() != null) {
            queryWrapper.eq(StationPointDistribute::getScaleId, request.getScaleId());
        }

        IPage<StationPointDistribute> ret = stationPointDistributeMapper.selectPage(qp, queryWrapper);

        IPage<StationPointDistributeDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            StationPointDistributeDto dataDto = new StationPointDistributeDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointScale stationPointScale = setStationPointDistribute.selectById(data.getScaleId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointScale != null) {
                dataDto.setStationPointScale(new StationPointScale());
                BeanUtils.copyProperties(stationPointScale, dataDto.getStationPointScale());
            }

            // 获取任务信息
            if (data.getTaskId() != null) {
                SurveyRouteTask surveyRouteTask = surveyRouteTaskMapper.selectById(data.getTaskId());
                if (surveyRouteTask != null) {
                    dataDto.setSurveyRouteTask(new SurveyRouteTask());
                    BeanUtils.copyProperties(surveyRouteTask, dataDto.getSurveyRouteTask());
                }
            }

            // 将 beforeInvestigate 和 afterInvestigate 转换为时间戳并放入 range 列表
//            List<Long> range = new ArrayList<>();
//            range.add(data.getBeforeInvestigate().getTime());
//            range.add(data.getAfterInvestigate().getTime());
//            dataDto.setRange(range);

            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public List<StationPointDistribute> getDistributesByScaleId(Integer scaleId) {
        return lambdaQuery().eq(StationPointDistribute::getScaleId, scaleId).list();
    }

    @Override
    public void removeById(Integer id) {
        LambdaQueryWrapper<Biodiversity> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(Biodiversity::getDistributeId, id);
        long count1 = biodiversityMapper.selectCount(wrapper1);
        if (count1 > 0) {
            throw new BizException(BizResponseCode.ERR_11099,"该记录存在关联的生物多样性分析数据，无法删除");
        }

        LambdaQueryWrapper<ChemicalIon> wrapper2 = new LambdaQueryWrapper<>();
        wrapper2.eq(ChemicalIon::getDistributeId, id);
        long count2 = chemicalIonMapper.selectCount(wrapper2);
        if (count2 > 0) {
            throw new BizException(BizResponseCode.ERR_11099,"该记录存在关联的化学离子数据，无法删除");
        }

        LambdaQueryWrapper<SurveyTimeRange> wrapper3 = new LambdaQueryWrapper<>();
        wrapper3.eq(SurveyTimeRange::getDistributeId, id);
        long count3 = surveyTimeRangeMapper.selectCount(wrapper3);
        if (count3 > 0) {
            throw new BizException(BizResponseCode.ERR_11099,"该记录存在关联的作业时间数据，无法删除");
        }

        LambdaQueryWrapper<WaterPhWeatherData> wrapper4 = new LambdaQueryWrapper<>();
        wrapper4.eq(WaterPhWeatherData::getDistributeId, id);
        long count4 = waterPhWeatherDataMapper.selectCount(wrapper4);
        if (count4 > 0) {
            throw new BizException(BizResponseCode.ERR_11099,"该记录存在关联的水文特征数据，无法删除");
        }

        // 如果没有关联数据，执行删除
        super.removeById(id);
    }
}


package cn.dhbin.isme.ims.service;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.excel.BiodiversityExcelDto;
import cn.dhbin.isme.ims.domain.dto.WaterPhWeatherDataDto;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffGroup;
import cn.dhbin.isme.ims.domain.request.BiodiversityRequest;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffGroupRequest;
import cn.dhbin.isme.ims.domain.request.WaterPhWeatherDataRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;

import java.util.List;

/**
 * 生物多样性分析表(Biodiversity)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-26 21:49:40
 */
public interface BiodiversityService extends IService<Biodiversity> {
    Page<BiodiversityDto> queryPage(BiodiversityRequest request);

    List<BiodiversityDto> queryList(Integer distributeId);
    List<BiodiversityExcelDto> queryListForExcel(BiodiversityRequest request);

    /**
     * 导入生物多样性数据
     *
     * @param list Excel导入的数据列表
     * @param type 生物类型（0：浮游植物，1：浮游动物，2：底栖生物，4：游泳动物）
     * @return 是否导入成功
     */
    boolean importData(List<BiodiversityExcelDto> list, Integer type);
}


package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.mapstruct.helper.core.Convert;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaterPhWeatherDataDto {
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点信息

    /**
     * 采样层次
     **/
    private Integer sampleLayer;

    /**
     * 天气现象
     **/
    private String weather;

    /**
     * 风向
     **/
    private String windDirection;

    /**
     * 盐度 - decimal(5,2)
     **/
    private BigDecimal saltExtent;

    /**
     * PH值 - decimal(4,2)
     **/
    private BigDecimal phExtent;

    /**
     * 气温,单位℃ - decimal(3,1)
     **/
    private BigDecimal airTemperature;

    /**
     * 水温,单位℃ - decimal(4,2)
     **/
    private BigDecimal waterTemperature;

    /**
     * 透明度,单位m - decimal(4,2)
     **/
    private BigDecimal transparentExtent;

    private Date createTime;

    private Date updateTime;
}



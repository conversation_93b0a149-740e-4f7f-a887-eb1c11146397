package cn.dhbin.isme.ims.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;
@Component
public class UploadUtil {

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${oss.ALI_DOMAIN}")
    private String ALI_DOMAIN;


    public  String uploadImage(MultipartFile file) throws IOException {
        //生成文件名
        String originalFilename = file.getOriginalFilename();
        String ext="."+ FilenameUtils.getExtension(originalFilename);
        String uuid= UUID.randomUUID().toString().replace("-","");
        String fileName=uuid+ext;
        //OSS客户端对象
        OSS ossClient=new OSSClientBuilder().build(endpoint,accessKeyId,accessKeySecret);
        ossClient.putObject(
                bucketName, //仓库名
                fileName, //文件名
                file.getInputStream()
        );
        ossClient.shutdown();
        return ALI_DOMAIN+fileName;
    }

}

package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 时空范围点位表（单点）(StationPointScale)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:40:58
 */
@Data
@TableName("station_point_scale")
public class StationPointScale implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;
    
    /**
     * 经度
     **/
    private Double longitude;
    
    /**
     * 纬度
     **/
    private Double latitude;
    
    /**
     * 空间范围描述
     **/
    private String description;
    
    /**
     * 创建时间
     **/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     **/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
public Serializable pkVal() {
          return null;
      }
}



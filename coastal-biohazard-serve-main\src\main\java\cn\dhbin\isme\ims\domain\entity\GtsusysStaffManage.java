package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 一线作业人员表(GtsusysStaffManage)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
@Data
@TableName("gtsusys_staff_manage")
public class GtsusysStaffManage implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 人员姓名
     **/
    private String name;

    /**
     * 性别（1男 0女 默认1）
     **/
    private Integer gender;

    /**
     * 身份证号
     **/
    private String idCard;

    private Integer groupId;

//    private String position;
//
//    private String phone;

    /**
     * 概述
     **/
    private String description;

    /**
     * 创建时间
     **/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     **/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

public Serializable pkVal() {
          return null;
      }
}



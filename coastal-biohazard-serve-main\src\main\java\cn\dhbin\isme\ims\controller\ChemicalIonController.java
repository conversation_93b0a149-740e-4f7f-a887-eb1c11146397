package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.ChemicalIonDto;

import cn.dhbin.isme.ims.domain.dto.excel.ChemicalIonExcelDto;
import cn.dhbin.isme.ims.domain.entity.ChemicalIon;
import cn.dhbin.isme.ims.domain.request.ChemicalIonRequest;
import cn.dhbin.isme.ims.service.ChemicalIonService;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.List;
@RestController
@RequestMapping("/chemical-ion")
@RequiredArgsConstructor
public class ChemicalIonController {

    private final ChemicalIonService chemicalIonService;
    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<ChemicalIonDto>> selectAll(ChemicalIonRequest request) {
        Page<ChemicalIonDto> ret = chemicalIonService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody ChemicalIon data) {
        chemicalIonService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody ChemicalIon data) {
        chemicalIonService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        chemicalIonService.removeById(id);
        return R.ok();
    }

    /**
     * 根据站点ID获取化学离子数据
     * @param stationId 站点ID
     * @return 化学离子数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<ChemicalIonDto>> getByStationId(@PathVariable Integer stationId) {
        List<ChemicalIonDto> list = chemicalIonService.getByStationId(stationId);
        return R.ok(list);
    }
    /**
     * 导出Excel
     */

    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(required = false) Integer distributeId,
                       @RequestParam(required = false) Integer sampleLayer) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("海水化学离子数据_" + LocalDate.now(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        List<ChemicalIonExcelDto> dataList = chemicalIonService.getExportData(distributeId, sampleLayer);
        EasyExcel.write(response.getOutputStream(), ChemicalIonExcelDto.class).sheet("数据列表").doWrite(dataList);
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<Void> importExcel(@RequestParam("file") MultipartFile file) throws IOException {
        List<ChemicalIonExcelDto> dataList = EasyExcel.read(file.getInputStream())
                .head(ChemicalIonExcelDto.class)
                .sheet()
                .doReadSync();

        chemicalIonService.importData(dataList);
        return R.ok();
    }
}

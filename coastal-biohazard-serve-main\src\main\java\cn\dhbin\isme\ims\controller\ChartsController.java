package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.ChartsDto;
import cn.dhbin.isme.ims.domain.dto.SurveyCountDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.service.ChemicalIonService;
import cn.dhbin.isme.ims.service.StationPointDistributeService;
import cn.dhbin.isme.ims.service.SurveyTimeRangeService;
import cn.dhbin.isme.ims.service.WaterPhWeatherDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/charts")
@RequiredArgsConstructor
public class ChartsController {

    private final StationPointDistributeService stationPointDistributeService;

    private final WaterPhWeatherDataService waterPhWeatherDataService;

    private final SurveyTimeRangeService surveyTimeRangeService;

    private final ChemicalIonService chemicalIonService;

    public static FieldDescription analyzeField(List<WaterPhWeatherData> dataList, String fieldName, Function<WaterPhWeatherData, BigDecimal> valueExtractor) {
        FieldDescription description = new FieldDescription();
        description.setFieldName(fieldName);

        if (dataList.isEmpty()) {
            description.setFluctuated(false);
            return description;
        }

        if (valueExtractor == null) {
            description.setFluctuated(false);
            return description;
        }

        double sum = 0;
        double max = Double.MIN_VALUE;
        double min = Double.MAX_VALUE;
        String maxStationName = "";
        String minStationName = "";

        int validCount = 0; // 记录有效数据的数量

        for (WaterPhWeatherData data : dataList) {
            BigDecimal value = valueExtractor.apply(data);
            if (value == null) {
                continue; // 跳过无效的数据项
            }

            double doubleValue = value.doubleValue();
            sum += doubleValue;
            validCount++;

            if (doubleValue > max) {
                max = doubleValue;
                maxStationName = data.getDistributeName();
            }

            if (doubleValue < min) {
                min = doubleValue;
                minStationName = data.getDistributeName();
            }
        }

        if (validCount == 0) {
            description.setFluctuated(false);
            return description; // 如果没有有效数据，则直接返回
        }

        double average = sum / validCount;

        // 使用 DecimalFormat 保留两位小数
        DecimalFormat decimalFormat = new DecimalFormat("#.00");
        String formattedAverage = decimalFormat.format(average);
        description.setAverageValue(Double.parseDouble(formattedAverage));

        description.setMaxValue(max);
        description.setMinValue(min);
        description.setMaxStationName(maxStationName);
        description.setMinStationName(minStationName);

        // 判断波动是否明显，这里简单地使用标准差来判断
        double varianceSum = 0;
        for (WaterPhWeatherData data : dataList) {
            BigDecimal value = valueExtractor.apply(data);
            if (value == null) {
                continue; // 跳过无效的数据项
            }

            double doubleValue = value.doubleValue();
            varianceSum += Math.pow(doubleValue - average, 2);
        }

        double variance = varianceSum / validCount;
        double standardDeviation = Math.sqrt(variance);

        // 可以根据标准差来判断波动是否明显，这里假设标准差大于1表示波动明显
        description.setFluctuated(standardDeviation > 1);

        return description;
    }

    @GetMapping("/data/organized")
    public R<ChartsDto> getOrganizedData(
            @RequestParam Integer scaleId,
            @RequestParam(required = false) Integer month) { // 新增月份参数

        // 1. 根据scaleId获取distributeIds（原逻辑）
        List<StationPointDistribute> distributes = stationPointDistributeService.getDistributesByScaleId(scaleId);
        if (distributes.isEmpty()) {
            return R.build(new BizException(BizResponseCode.ERR_11012));
        }
        List<Integer> scaleDistributeIds = distributes.stream()
                .map(StationPointDistribute::getId)
                .collect(Collectors.toList());

        // 2. 新增：根据月份筛选distributeIds
        List<Integer> finalDistributeIds = new ArrayList<>(scaleDistributeIds);
        if (month != null) {
            // 验证月份有效性
            if (month < 1 || month > 12) {
                return R.build(new BizException(BizResponseCode.ERR_REQUEST_PARAM));
            }

            // 查询符合月份条件的distributeIds
            List<Integer> monthDistributeIds = surveyTimeRangeService.getDistributeIdsByMonth(month);

            // 取两个distributeIds的交集
            finalDistributeIds.retainAll(monthDistributeIds);

            if (finalDistributeIds.isEmpty()) {
                return R.ok(new ChartsDto()); // 返回空数据
            }
        }

        // 3. 使用最终的distributeIds查询数据（原逻辑修改）
        List<WaterPhWeatherData> dataList = waterPhWeatherDataService.getWaterPhWeatherDataByDistributeIds(finalDistributeIds);
        List<ChemicalIon> chemicalIonList = chemicalIonService.getChemicalIonByDistributeIds(finalDistributeIds);

        // 4. 后续分析逻辑保持不变...
        ChartsDto chartsDto = new ChartsDto();
        chartsDto.setWaterPhWeatherDataList(dataList);
        chartsDto.setChemicalIonList(chemicalIonList);
        // 根据 distribute_id 查询 water_ph_weather_data 表中的记录

        FieldDescription airTempDescription = analyzeField(
                dataList,
                "气温",
                WaterPhWeatherData::getAirTemperature
        );

        // 分析水温
        FieldDescription waterTempDescription = analyzeField(
                dataList,
                "水温",
                WaterPhWeatherData::getWaterTemperature
        );

        // 分析盐度
        FieldDescription salinityDescription = analyzeField(
                dataList,
                "盐度",
                WaterPhWeatherData::getSaltExtent
        );

        // 分析pH
        FieldDescription phDescription = analyzeField(
                dataList,
                "pH",
                WaterPhWeatherData::getPhExtent
        );

        // 将所有 FieldDescription 对象放入一个列表中
        List<FieldDescription> fieldDescriptions = new ArrayList<>();
        fieldDescriptions.add(airTempDescription);
        fieldDescriptions.add(waterTempDescription);
        fieldDescriptions.add(salinityDescription);
        fieldDescriptions.add(phDescription);

        // 设置到 ChartsDto 对象中
        chartsDto.setFieldDescriptionList(fieldDescriptions);
        return R.ok(chartsDto);
    }

    @GetMapping("/data/surveyTimes")
    public R<?> getSurveyTimes(@RequestParam Integer scaleId) {
        // 根据 scale_id 查询 station_point_distribute 表中的记录
        List<StationPointDistribute> distributes = stationPointDistributeService.getDistributesByScaleId(scaleId);

        // 提取所有 distribute_id 和对应的 name
        Map<Integer, String> distributeNameMap = distributes.stream()
                .collect(Collectors.toMap(
                        StationPointDistribute::getId,
                        StationPointDistribute::getName
                ));

        // 提取所有 distribute_id
        List<Integer> distributeIds = distributes.stream()
                .map(StationPointDistribute::getId)
                .collect(Collectors.toList());

        // 根据 distribute_ids 查询 survey_time_range 表中的记录
        List<SurveyTimeRange> surveyTimeRangeByDistributeIds = surveyTimeRangeService.getSurveyTimeRangeByDistributeIds(distributeIds);

        // 统计每个站点的调查次数并直接封装到 SurveyCount 对象中
        List<SurveyCountDto> surveyCountList = new ArrayList<>();
        for (SurveyTimeRange surveyTimeRange : surveyTimeRangeByDistributeIds) {
            int distributeId = surveyTimeRange.getDistributeId();
            String distributeName = distributeNameMap.get(distributeId);
            if (distributeName != null) {
                SurveyCountDto surveyCount = surveyCountList.stream()
                        .filter(sc -> sc.getDistributeName().equals(distributeName))
                        .findFirst()
                        .orElse(null);
                if (surveyCount == null) {
                    surveyCountList.add(new SurveyCountDto(distributeName, 1));
                } else {
                    surveyCount.setCount(surveyCount.getCount() + 1);
                }
            }
        }

        return R.ok(surveyCountList);
    }
}

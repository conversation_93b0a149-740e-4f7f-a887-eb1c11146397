package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.GtsusysStaffManageDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffManageRequest;
import cn.dhbin.isme.ims.domain.request.ManageBatchInsertRequest;
import cn.dhbin.isme.ims.mapper.GtsusysStaffGroupMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.GtsusysStaffManageMapper;
import cn.dhbin.isme.ims.service.GtsusysStaffManageService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 一线作业人员表(GtsusysStaffManage)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
@Service("gtsusysStaffManageService")
public class GtsusysStaffManageServiceImpl extends ServiceImpl<GtsusysStaffManageMapper, GtsusysStaffManage> implements GtsusysStaffManageService {

    @Autowired
    private GtsusysStaffManageMapper gtsusysStaffManageMapper;

    @Autowired
    private GtsusysStaffGroupMapper gtsusysStaffGroupMapper;

    @Override
    public Page<GtsusysStaffManageDto> queryPage(GtsusysStaffManageRequest request) {
        IPage<GtsusysStaffManage> qp = request.toPage();
        LambdaQueryWrapper<GtsusysStaffManage> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getGender() != null) {
            queryWrapper.eq(GtsusysStaffManage::getGender, request.getGender());
        }
        if (request.getName() != null) {
            queryWrapper.like(GtsusysStaffManage::getName, request.getName());
        }
        if (request.getGroupId() != null) {
            queryWrapper.eq(GtsusysStaffManage::getGroupId, request.getGroupId());
        }

        IPage<GtsusysStaffManage> ret = gtsusysStaffManageMapper.selectPage(qp, queryWrapper);

        IPage<GtsusysStaffManageDto> dtoIPage = ret.convert(data -> {
            GtsusysStaffManageDto dataDto = new GtsusysStaffManageDto();
            BeanUtils.copyProperties(data, dataDto);
            GtsusysStaffGroup gtsusysStaffGroup = gtsusysStaffGroupMapper.selectById(data.getGroupId());
            dataDto.setGroupName(gtsusysStaffGroup.getName());
            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public boolean batchInsert(ManageBatchInsertRequest request) {
        List<GtsusysStaffManage> manageList = request.getManageList();
        for (GtsusysStaffManage manage : manageList) {
            manage.setGroupId(request.getGroupId());
        }
        return saveBatch(manageList);
    }

    @Override
    public List<GtsusysStaffManageDto> queryList(GtsusysStaffManageRequest request) {
        LambdaQueryWrapper<GtsusysStaffManage> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getGroupId() != null) {
            queryWrapper.eq(GtsusysStaffManage::getGroupId, request.getGroupId());
        }

        List<GtsusysStaffManage> resultList = gtsusysStaffManageMapper.selectList(queryWrapper);

        List<GtsusysStaffManageDto> dtoList = resultList.stream().map(data -> {
            GtsusysStaffManageDto dataDto = new GtsusysStaffManageDto();
            BeanUtils.copyProperties(data, dataDto);
            GtsusysStaffGroup gtsusysStaffGroup = gtsusysStaffGroupMapper.selectById(data.getGroupId());
            dataDto.setGroupName(gtsusysStaffGroup.getName());
            return dataDto;
        }).collect(Collectors.toList());

        return dtoList;
    }
}


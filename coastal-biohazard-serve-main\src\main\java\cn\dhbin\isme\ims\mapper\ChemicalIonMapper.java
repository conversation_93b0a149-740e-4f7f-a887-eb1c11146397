package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.dhbin.isme.ims.domain.entity.ChemicalIon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 化学离子表(ChemicalIon)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-28 12:52:05
 */
@Mapper
public interface ChemicalIonMapper extends BaseMapper<ChemicalIon> {
    List<ChemicalIon> selectBatchByDistributeIds(@Param("distributeIds") List<Integer> distributeIds);
}


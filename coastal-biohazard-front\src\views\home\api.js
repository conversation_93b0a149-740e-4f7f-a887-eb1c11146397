import { request } from '@/utils'

export default {
  trainModel: data => request.post('/predict/train', data),
  predictModel:data=>request.post('/predict/predict',data),
  cleanDataAndTrainModel:data=>request.post('/algae/clean-and-train',data),
  predictWeek: (params = {}) => request.get('/algae/predict-week', { params }),

  getOrganizedData: (scaleId,month) => request.get(`/charts/data/organized?scaleId=${scaleId}&month=${month}`),

  getStationPoints: () => request.get('/station-point-scale/list'),
}

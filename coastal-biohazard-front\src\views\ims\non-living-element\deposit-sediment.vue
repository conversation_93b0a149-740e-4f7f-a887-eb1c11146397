<!--------------------------------
-沉积物分析
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出/导入功能 -->
<!--        <NButton type="warning" @click="handleExport">-->
<!--          <i class="i-material-symbols:download mr-4 text-18" />-->
<!--          导出Excel-->
<!--        </NButton>-->

<!--        &lt;!&ndash; 新增导入按钮 &ndash;&gt;-->
<!--        <NUpload-->
<!--          :show-file-list="false"-->
<!--          :custom-request="handleImport"-->
<!--          accept=".xlsx,.xls"-->
<!--          :disabled="importLoading"-->
<!--        >-->
<!--          <NButton-->
<!--            type="success"-->
<!--            :loading="importLoading"-->
<!--            :disabled="importLoading"-->
<!--          >-->
<!--            <i class="i-material-symbols:upload mr-4 text-18" />-->
<!--            {{ importLoading ? "正在导入..." : "导入Excel" }}-->
<!--          </NButton>-->
<!--        </NUpload>-->

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud
      ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="api.readMetalIon"
    />

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" :rules="rules" label-placement="left" label-align="left" :label-width="120" :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <!-- <n-config-provider :locale="zhCN"> -->
        <n-form-item label="沉积物图片" path="sedimentUrl">
          <n-upload
            :default-file-list="previewFileList1" :max="1" :custom-request="handleUpload1" list-type="image-card"
            @preview="handlePreview" @before-upload="validateFileBeforeUpload" @remove="handleRemove"
          >
            <template #trigger>
              <NButton type="primary">
                点击上传
              </NButton>
            </template>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>
        <!-- </n-config-provider> -->

        <n-form-item label="横切图片" path="cultureUrl">
          <n-upload
            :default-file-list="previewFileList2" :max="1" :custom-request="handleUpload2" list-type="image-card"
            @preview="handlePreview" @before-upload="validateFileBeforeUpload" @remove="handleRemove"
          >
            <template #trigger>
              <NButton type="primary">
                点击上传
              </NButton>
            </template>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal } from '@/components'
import { useCrud } from '@/composables'
import {createDiscreteApi, NButton, NImage, NTag} from 'naive-ui'

import { h } from 'vue'
import api from './api'

defineOptions({ name: 'RoleUser' })
const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)
const route = useRoute()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

// 预览图片URL
const previewImageUrl = ref()

const previewFileList1 = ref([])
const previewFileList2 = ref([])
const previewFileList3 = ref([])

const rules = reactive({
  sedimentUrl: { required: true, message: '请上传分支图片' },
  cultureUrl: { required: true, message: '请上传横切图片' },
})

// 模态框显示状态
const showModal = ref(false)

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '生物量',
  initForm: { enable: true, sedimentUrl: '', cultureUrl: '' },
  doCreate: api.createMetalIon,
  doDelete: api.deleteMetalIon,
  doUpdate: api.updateMetalIon,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

onMounted(() => {
  queryItems.value.abundanceId = route.query.abundanceId
  $table.value?.handleSearch()
})

// 智能图片处理函数
const isImage = (filename) => {
  if (!filename) return false
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext))
}

const getImageUrl = (imageName) => {
  if (!imageName) return getPlaceholderImage()
  
  // 如果是完整的URL（包含http或https），直接返回
  if (imageName.startsWith('http://') || imageName.startsWith('https://')) {
    return imageName
  }
  
  // 如果是本地文件名，通过API获取
  return `${baseUrl.value}/upload/getImage?imageName=${imageName}`
}

const getPlaceholderImage = () => {
  // 使用base64编码的占位符图片（一个简单的灰色方块）
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NS4wMDAxIDc1VjEyNUgxMjVWNzVINzUuMDAwMVoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTkwIDEwMEMxMDQuMTQzIDEwMCAxMTUuNzE0IDg4LjQyODYgMTE1LjcxNCA3NC4yODU3QzExNS43MTQgNjAuMTQyOSAxMDQuMTQzIDQ4LjU3MTQgOTAgNDguNTcxNEM3NS44NTcxIDQ4LjU3MTQgNjQuMjg1NyA2MC4xNDI5IDY0LjI4NTcgNzQuMjg1N0M2NC4yODU3IDg4LjQyODYgNzUuODU3MSAxMDAgOTAgMTAwWiIgZmlsbD0iI0Q5RDlEOSIvPgo8cGF0aCBkPSJNMTA3LjE0MyAxMjguNTcxTDEyNSAxMTQuMjg2VjEyNUg3NVYxMTQuMjg2TDg1LjcxNDMgMTAzLjU3MVMxMDcuMTQzIDEyOC41NzEgMTA3LjE0MyAxMjguNTcxWiIgZmlsbD0iI0Q5RDlEOSIvPgo8dGV4dCB4PSIxMDAiIHk9IjE2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPg=='
}

const openMedia = (imageName) => {
  if (!imageName) return
  
  // 如果是完整的URL，直接在新窗口打开
  if (imageName.startsWith('http://') || imageName.startsWith('https://')) {
    window.open(imageName, '_blank')
    return
  }
  
  // 如果是本地文件，通过API打开
  const url = `${baseUrl.value}/upload/getImage?imageName=${imageName}`
  window.open(url, '_blank')
}

const columns = [
  {
    title: '序号',
    key: 'index',
    // width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '沉积物图片',
    key: 'sedimentUrl',
    render(row) {
      return h(NImage, {
        class: 'w-[100px]',
        src: getImageUrl(row.sedimentUrl),
        onError: (event) => {
          event.target.src = getPlaceholderImage()
        }
      })
    },
  },
  {
    title: '沉积物培养',
    key: 'cultureUrl',
    render(row) {
      return h(NImage, {
        class: 'w-[100px]',
        src: getImageUrl(row.cultureUrl),
        onError: (event) => {
          event.target.src = getPlaceholderImage()
        }
      })
    },
  },
  {
    // width: 180,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleOpenUpdate(row),
          },
          {
            default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function handleOpenUpdate(row) {
  previewFileList1.value = [{
    url: getImageUrl(row.sedimentUrl),
    status: 'finished',
    id: 'sedimentUrl',
    name: row.sedimentUrl,
    thumbnailUrl: getImageUrl(row.sedimentUrl),
  }]
  previewFileList2.value = [{
    url: getImageUrl(row.cultureUrl),
    status: 'finished',
    id: 'cultureUrl',
    name: row.cultureUrl,
    thumbnailUrl: getImageUrl(row.cultureUrl),
  }]
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row,
    onOk: updateM,
  })
}

async function updateM() {
  await modalFormRef.value?.validate()
  await api.updateMorphological({
    id: modalForm.value.id,
    // abundanceId: route.query.abundanceId,
    sedimentUrl: modalForm.value.sedimentUrl,
    cultureUrl: modalForm.value.cultureUrl,
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}

function add() {
  previewFileList1.value = []
  previewFileList2.value = []
  previewFileList3.value = []
  modalForm.value.sedimentUrl = null
  modalForm.value.cultureUrl = null
  handleOpen({
    action: 'add',
    title: '新增记录',
    row: modalForm.value,
    onOk: async () => {
      await modalFormRef.value?.validate()
      await api.createMetalIon({
        sedimentUrl: modalForm.value.sedimentUrl,
        cultureUrl: modalForm.value.cultureUrl,
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

function handleRemove(data) {
  if (data.file.id === 'sedimentUrl') {
    previewFileList1.value = []
    // sedimentUrl.value = null
    modalForm.value.sedimentUrl = null
  }
  else if (data.file.id === 'cultureUrl') {
    previewFileList2.value = []
    // cultureUrl.value = null
    modalForm.value.cultureUrl = null
  }
}

function validateFileBeforeUpload({ file }) {

  const allowedTypes = ['image/jpeg', 'image/png']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!allowedTypes.includes(file.type)) {
    $message('只允许上传 JPG 和 PNG 格式的图片！')
    return false
  }

  if (file.size > maxSize) {
    $message('图片大小不能超过 5MB！')
    return false
  }

  return true
}

async function handleUpload1({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }

  $message.loading('上传中...')
  const { data } = await api.uploadImg(file)

  // 拼接完整的图片访问路径
  // 更新 file.url 为完整路径
  file.url = getImageUrl(data)

  modalForm.value.sedimentUrl = data
  $message.success('上传成功')
  onFinish()
}

async function handleUpload2({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }

  $message.loading('上传中...')
  const { data } = await api.uploadImg(file)
  file.url = getImageUrl(data)
  // cultureUrl.value = data
  modalForm.value.cultureUrl = data
  $message.success('上传成功')
  onFinish()
}

function handlePreview(file) {
  // const { url } = file
  previewImageUrl.value = file.url
  showModal.value = true
}
// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导出功能
async function handleExport() {
  try {
    const response = await api.SedimentExportExcel({
      distributeId: queryItems.value.distributeId,
      sampleLayer: queryItems.value.sampleLayer,
    })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `沉积物数据_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.SedimentImportExcel(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false
  }
}
</script>

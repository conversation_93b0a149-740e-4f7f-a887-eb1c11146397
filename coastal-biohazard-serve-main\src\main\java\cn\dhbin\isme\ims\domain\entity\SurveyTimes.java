package cn.dhbin.isme.ims.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 调查次数实体
 */
@Data
@TableName("survey_times")
public class SurveyTimes {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 任务ID，关联survey_route_task表
     */
    private Integer taskId;
    
    /**
     * 调查次数
     */
    private Integer times;
    
    /**
     * 调查日期
     */
    private LocalDate date;
    
    /**
     * 开始时间
     */
    private LocalTime startTime;
    
    /**
     * 结束时间
     */
    private LocalTime endTime;
    
    /**
     * 天气条件
     */
    private String weatherCondition;
    
    /**
     * 海况
     */
    private String seaCondition;
    
    /**
     * 作业人员数量
     */
    private Integer crewCount;
    
    /**
     * 设备状态
     */
    private String equipmentStatus;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 状态：0-计划中，1-进行中，2-已完成，3-已取消
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
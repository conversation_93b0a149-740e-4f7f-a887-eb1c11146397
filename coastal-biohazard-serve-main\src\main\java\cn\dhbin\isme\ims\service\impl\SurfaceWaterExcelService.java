package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.mapper.AbundanceLayerSpeciesDataMapper;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.service.AbundanceLayerSpeciesDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

@Service
@RequiredArgsConstructor
@Slf4j
public class SurfaceWaterExcelService {
    private final AbundanceLayerSpeciesDataService abundanceLayerSpeciesDataService;
    private final AbundanceLayerSpeciesDataMapper abundanceLayerSpeciesDataMapper;
    private final StationPointDistributeMapper stationPointDistributeMapper;
    
    private static final int SURFACE_WATER_SAMPLE_TYPE = 2; // 表层水样

    public byte[] exportToExcel(Integer distributeId) throws IOException {
        // 查询表层水样数据
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AbundanceLayerSpeciesData::getSampleType, SURFACE_WATER_SAMPLE_TYPE);
        
        if (distributeId != null) {
            queryWrapper.eq(AbundanceLayerSpeciesData::getDistributeId, distributeId);
        }
        
        List<AbundanceLayerSpeciesData> dataList = abundanceLayerSpeciesDataMapper.selectList(queryWrapper);
        log.info("表层水样数据查询结果: distributeId={}, 查询到{}条记录", distributeId, dataList.size());
        
        if (dataList.isEmpty()) {
            log.warn("表层水样数据为空，distributeId={}", distributeId);
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("表层水样微观繁殖体数据");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"站点", "样品类型", "丰富度(ind./50g)", "创建时间", "更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据行
            int rowNum = 1;
            for (AbundanceLayerSpeciesData data : dataList) {
                Row row = sheet.createRow(rowNum++);
                
                // 站点名称
                StationPointDistribute stationPoint = stationPointDistributeMapper.selectById(data.getDistributeId());
                String stationName = stationPoint != null ? stationPoint.getName() : "未知站点";
                row.createCell(0).setCellValue(stationName);
                
                // 样品类型
                row.createCell(1).setCellValue("表层水样");
                
                // 丰富度 - 添加空值检查
                Integer abundance = data.getAbundance();
                row.createCell(2).setCellValue(abundance != null ? abundance : 0);
                
                // 创建时间
                row.createCell(3).setCellValue(data.getCreateTime() != null ? 
                        data.getCreateTime().toString() : "");
                
                // 更新时间
                row.createCell(4).setCellValue(data.getUpdateTime() != null ? 
                        data.getUpdateTime().toString() : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    public void importFromExcel(MultipartFile file, Integer distributeId) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            List<AbundanceLayerSpeciesData> dataList = new ArrayList<>();

            // 跳过表头行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                AbundanceLayerSpeciesData data = new AbundanceLayerSpeciesData();
                data.setDistributeId(distributeId);
                data.setSampleType(SURFACE_WATER_SAMPLE_TYPE);
                
                // 获取丰富度值
                Cell abundanceCell = row.getCell(2);
                if (abundanceCell != null) {
                    if (abundanceCell.getCellType() == CellType.NUMERIC) {
                        data.setAbundance((int) abundanceCell.getNumericCellValue());
                    } else if (abundanceCell.getCellType() == CellType.STRING) {
                        try {
                            data.setAbundance(Integer.parseInt(abundanceCell.getStringCellValue().trim()));
                        } catch (NumberFormatException e) {
                            continue; // 跳过无法解析的行
                        }
                    }
                } else {
                    continue; // 跳过丰富度为空的行
                }

                dataList.add(data);
            }

            // 保存数据
            if (!dataList.isEmpty()) {
                abundanceLayerSpeciesDataService.saveBatch(dataList);
            }
        }
    }
} 
package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.AnalysisSample;
import cn.dhbin.isme.ims.domain.entity.SampleInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AnalysisSampleMapper extends BaseMapper<AnalysisSample> {
    @Select("SELECT sample_id,number FROM analysis_sample WHERE abundance_id = #{abundanceId}")
    List<SampleInfo> getSampleInfosByAbundanceId(@Param("abundanceId") Integer abundanceId);

    int deleteByAbundanceId(@Param("abundanceId") Integer abundanceId);

    List<AnalysisSample> selectByAbundanceId(@Param("abundanceId") Integer abundanceId);

}


export const basicRoutes = [
  {
    name: 'Login',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录页',
      layout: 'empty',
    },
  },

  {
    name: 'Home',
    path: '/',
    component: () => import('@/views/home/<USER>'),
    meta: {
      title: '首页',
    },
  },

  {
    name: '404',
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    meta: {
      title: '页面飞走了',
      layout: 'empty',
    },
  },

  {
    name: '403',
    path: '/403',
    component: () => import('@/views/error-page/403.vue'),
    meta: {
      title: '没有权限',
      layout: 'empty',
    },
  },
  {
    name: 'JsVue',
    path: '/ims/JsVue',
    component: () => import('@/views/ims/time-space-analysis/JsVue.vue'),
    meta: {
      title: '调查中心站点分布',
      layout: 'empty',
    },
  },
  {
    name: 'StationMapDistribution',
    path: '/ims/station-map-distribution',
    component: () => import('@/views/ims/time-space-analysis/JsVue.vue'),
    meta: {
      title: '调查中心站点分布',
      layout: 'empty',
    },
  },
  {
    name: 'ProfessionalDashboard',
    path: '/ims/professional-dashboard',
    component: () => import('@/views/ims/professional-dashboard/index.vue'),
    meta: {
      title: '海洋生态专业监测大屏',
      layout: 'empty',
    },
  },

]

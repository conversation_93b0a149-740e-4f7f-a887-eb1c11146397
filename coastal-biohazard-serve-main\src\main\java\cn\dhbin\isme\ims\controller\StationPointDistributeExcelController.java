package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.auth.RoleType;
import cn.dhbin.isme.common.auth.Roles;
import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.service.impl.StationPointDistributeExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 调查站点分布 Excel导入导出接口
 */
@RestController
@RequestMapping("/station-point-distribute")
@RequiredArgsConstructor
@Tag(name = "调查站点分布Excel导入导出")
@Slf4j
public class StationPointDistributeExcelController {

    private final StationPointDistributeExcelService excelService;

    /**
     * 导出调查站点分布数据
     *
     * @param scaleId 空间范围ID，可选参数
     * @return Excel文件
     */
    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导出调查站点分布Excel")
    public ResponseEntity<byte[]> export(@RequestParam(required = false) Integer scaleId) throws Exception {
        try {
            log.info("开始导出调查站点分布Excel: scaleId={}", scaleId);
            byte[] excelContent = excelService.exportToExcel(scaleId);
            
            String filename = URLEncoder.encode("调查站点分布数据.xlsx", StandardCharsets.UTF_8.toString());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelContent);
        } catch (Exception e) {
            log.error("导出调查站点分布Excel失败", e);
            throw new BizException(BizResponseCode.ERR_11011, "导出失败: " + e.getMessage());
        }
    }

    /**
     * 导入调查站点分布数据
     *
     * @param scaleId 空间范围ID，可选参数
     * @param file    Excel文件
     * @return 导入结果
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导入调查站点分布Excel")
    public R<Void> importExcel(
            @RequestParam(required = false) Integer scaleId, 
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("开始导入调查站点分布Excel: scaleId={}, fileName={}", scaleId, file.getOriginalFilename());
            
            if (file == null || file.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请选择要导入的Excel文件"));
            }
            
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请上传Excel文件(.xlsx或.xls格式)"));
            }
            
            excelService.importFromExcel(file, scaleId);
            log.info("调查站点分布Excel导入成功");
            return R.ok();
        } catch (Exception e) {
            log.error("导入调查站点分布Excel失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11011, "导入失败：" + e.getMessage()));
        }
    }
} 
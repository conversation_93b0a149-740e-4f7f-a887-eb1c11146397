package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.WaterQualityPredictionDto;
import cn.dhbin.isme.ims.service.WaterQualityPredictionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

import java.math.BigDecimal;

/**
 * 水质预测控制器
 * 提供水质预测算法和SSE流式传输功能
 */
@Slf4j
@RestController
@RequestMapping("/water-quality-prediction")
@RequiredArgsConstructor
public class WaterQualityPredictionController {

    private final WaterQualityPredictionService waterQualityPredictionService;

    /**
     * 执行水质预测
     * @param longitude 经度
     * @param latitude 纬度
     * @param scaleId 调查中心ID
     * @param taskId 任务ID（可选）
     * @param timesId 调查次数ID（可选）
     * @return 预测结果
     */
    @PostMapping("/predict")
    public R<WaterQualityPredictionDto> predictWaterQuality(
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam Integer scaleId,
            @RequestParam(required = false) Integer taskId,
            @RequestParam(required = false) Integer timesId) {
        
        try {
            WaterQualityPredictionDto prediction = waterQualityPredictionService.predictWaterQuality(
                    longitude, latitude, scaleId, taskId, timesId);
            
            log.info("水质预测完成，预测点: ({}, {}), 调查中心ID: {}, 任务ID: {}, 调查次数ID: {}", 
                    longitude, latitude, scaleId, taskId, timesId);
            
            return R.ok(prediction);
        } catch (Exception e) {
            log.error("水质预测失败，预测点: ({}, {}), 调查中心ID: {}", longitude, latitude, scaleId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "水质预测失败: " + e.getMessage()));
        }
    }

    /**
     * 获取水质预测分析的SSE流
     * @param longitude 经度
     * @param latitude 纬度
     * @param scaleId 调查中心ID
     * @param taskId 任务ID（可选）
     * @param timesId 调查次数ID（可选）
     * @return SSE发射器
     */
    @GetMapping("/analysis-stream")
    public SseEmitter getAnalysisStream(
            @RequestParam BigDecimal longitude,
            @RequestParam BigDecimal latitude,
            @RequestParam Integer scaleId,
            @RequestParam(required = false) Integer taskId,
            @RequestParam(required = false) Integer timesId) {
        
        try {
            log.info("开始水质预测分析流，预测点: ({}, {}), 调查中心ID: {}, 任务ID: {}, 调查次数ID: {}", 
                    longitude, latitude, scaleId, taskId, timesId);
            
            return waterQualityPredictionService.getAnalysisStream(longitude, latitude, scaleId, taskId, timesId);
        } catch (Exception e) {
            log.error("创建水质预测分析流失败，预测点: ({}, {})", longitude, latitude, e);
            
            // 返回错误信息的SSE
            SseEmitter emitter = new SseEmitter(5000L);
            try {
                emitter.send(SseEmitter.event()
                        .name("error")
                        .data("创建分析流失败: " + e.getMessage()));
                emitter.complete();
            } catch (Exception ex) {
                log.error("发送SSE错误信息失败", ex);
                emitter.completeWithError(ex);
            }
            return emitter;
        }
    }

    /**
     * 获取历史预测记录
     * @param scaleId 调查中心ID
     * @param taskId 任务ID（可选）
     * @param timesId 调查次数ID（可选）
     * @return 历史预测记录列表
     */
    @GetMapping("/history")
    public R<?> getHistoryPredictions(
            @RequestParam Integer scaleId,
            @RequestParam(required = false) Integer taskId,
            @RequestParam(required = false) Integer timesId) {
        
        try {
            // 这里可以根据需要实现历史记录查询
            log.info("查询历史预测记录，调查中心ID: {}, 任务ID: {}, 调查次数ID: {}", scaleId, taskId, timesId);
            
            // 暂时返回空列表，实际可以实现具体的查询逻辑
            return R.ok(java.util.Collections.emptyList());
        } catch (Exception e) {
            log.error("查询历史预测记录失败，调查中心ID: {}", scaleId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "查询历史预测记录失败: " + e.getMessage()));
        }
    }

    /**
     * 根据站点ID获取水质预测数据
     * @param stationId 站点ID
     * @param longitude 经度（可选）
     * @param latitude 纬度（可选）
     * @return 水质预测数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<WaterQualityPredictionDto>> getByStationId(
            @PathVariable Integer stationId,
            @RequestParam(required = false) BigDecimal longitude,
            @RequestParam(required = false) BigDecimal latitude) {
        
        try {
            log.info("获取站点ID: {} 的水质预测数据", stationId);
            
            List<WaterQualityPredictionDto> predictions = waterQualityPredictionService.getByStationId(stationId, longitude, latitude);
            
            return R.ok(predictions);
        } catch (Exception e) {
            log.error("获取站点 {} 的水质预测数据失败", stationId, e);
            return R.build(new BizException(BizResponseCode.ERR_11099, "获取水质预测数据失败: " + e.getMessage()));
        }
    }
} 
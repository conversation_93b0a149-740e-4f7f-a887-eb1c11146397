package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.entity.Sediment;
import cn.dhbin.isme.ims.domain.request.SedimentRequest;
import cn.dhbin.isme.ims.service.SedimentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sediment")
@RequiredArgsConstructor
public class SedimentController {
    private final SedimentService sedimentService;
    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<Sediment>> selectAll(SedimentRequest request) {
        Page<Sediment> ret = sedimentService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody Sediment data) {
        sedimentService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody Sediment data) {
        sedimentService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        sedimentService.removeById(id);
        return R.ok();
    }

    /**
     * 根据站点ID获取沉积物数据
     * @param stationId 站点ID
     * @return 沉积物数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<Sediment>> getByStationId(@PathVariable Integer stationId) {
        List<Sediment> list = sedimentService.getByStationId(stationId);
        return R.ok(list);
    }
}

package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:56
 */
@Data
@TableName("abundance_layer_species_data")
public class AbundanceLayerSpeciesData implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 站点id
     **/
    private Integer distributeId;
    
    /**
     * 样品类型
     **/
    private Integer sampleType;
    
    /**
     * 丰富度 ind./50g
     **/
    private Integer abundance;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    
public Serializable pkVal() {
          return null;
      }
}



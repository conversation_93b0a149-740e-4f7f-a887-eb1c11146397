package cn.dhbin.isme.ims.domain.request;

import cn.dhbin.isme.common.request.PageRequest;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class StationPointDistributeRequest extends PageRequest {

    private Integer scaleId; // 空间范围单点信息

    /**
     * 空间范围单点信息
     */

    private String name;

}

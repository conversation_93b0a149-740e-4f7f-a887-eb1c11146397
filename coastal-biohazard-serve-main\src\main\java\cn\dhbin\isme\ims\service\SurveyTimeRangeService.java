package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.excel.SurveyTimeRangeExcelDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import cn.dhbin.isme.ims.domain.request.SurveyTimeRangeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 调查时间范围表(SurveyTimeRange)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-05 08:18:24
 */
public interface SurveyTimeRangeService extends IService<SurveyTimeRange> {
    Page<SurveyTimeRange> queryPage(SurveyTimeRangeRequest request);

    List<SurveyTimeRange> getSurveyTimeRangeByDistributeIds(List<Integer> distributeIds);

    List<Integer> getDistributeIdsByMonth(Integer month);

    /**
     * 查询导出列表
     * @param request 请求参数
     * @return 导出数据列表
     */
    List<SurveyTimeRangeExcelDto> queryListForExport(SurveyTimeRangeRequest request);

    /**
     * 导入数据
     * @param list 导入的数据列表
     */
    void importData(List<SurveyTimeRangeExcelDto> list);

}


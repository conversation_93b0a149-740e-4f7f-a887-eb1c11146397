package cn.dhbin.isme.ims.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 水质预测结果DTO
 */
@Data
public class WaterQualityPredictionDto {
    
    /**
     * 预测点经度
     */
    private BigDecimal longitude;
    
    /**
     * 预测点纬度
     */
    private BigDecimal latitude;
    
    /**
     * 调查中心ID
     */
    private Integer scaleId;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 调查次数ID
     */
    private Integer timesId;
    
    /**
     * 预测盐度
     */
    private BigDecimal saltExtent;
    
    /**
     * 预测pH值
     */
    private BigDecimal phExtent;
    
    /**
     * 预测水温
     */
    private BigDecimal waterTemperature;
    
    /**
     * 预测透明度
     */
    private BigDecimal transparentExtent;
    
    /**
     * 置信度评分(0-1)
     */
    private BigDecimal confidenceScore;
    
    /**
     * 模型版本
     */
    private String modelVersion;
    
    /**
     * 预测时间
     */
    private LocalDateTime predictionTime;
    
    /**
     * AI分析结果（用于流式传输时不包含）
     */
    private String aiAnalysis;
} 
package cn.dhbin.isme.ims.domain.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class DistributeAndWaterDto {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 空间范围id
     **/
    private Integer scaleId;

    /**
     * 监测站位（名称）
     **/
    private String name;

    /**
     * 经度
     **/
    private Double longitude;

    /**
     * 纬度
     **/
    private Double latitude;

    /**
     * 概述
     **/
    private String description;

    /**
     * 调查开始时间
     **/
    private Date beforeInvestigate;

    /**
     * 调查结束时间
     **/
    private Date afterInvestigate;

    /**
     * 创建时间
     **/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     **/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 盐度
     **/
    private Double saltExtent;

    /**
     * PH值
     **/
    private Double phExtent;

    /**
     * 水温,单位℃
     **/
    private Double waterTemperature;

    /**
     * 透明度,单位m
     **/
    private Double transparentExtent;

}

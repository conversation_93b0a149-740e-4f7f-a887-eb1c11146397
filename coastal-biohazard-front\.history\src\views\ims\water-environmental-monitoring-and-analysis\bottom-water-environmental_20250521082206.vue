<!--------------------------------
-微观繁殖体分析
-1.底层海水海域
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 导入Excel按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          新增
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1800" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select
          v-model:value="queryItems.distributeId" label-field="name" value-field="id" clearable filterable
          :options="stationOption" placeholder="请选择站点"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <n-form-item
          label="站点" path="distributeId" :rule="{
            required: true,
            message: '请选择站点',
          // trigger: ['change'],
          }"
        >
          <!-- <n-input v-model:value="modalForm.algaeName" /> -->
          <n-select
            v-model:value="modalForm.distributeId" label-field="name" value-field="id" clearable
            filterable :options="stationOption" placeholder="请选择站点" @change="changeSelect"
          />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input v-model:value="coordination.longitude" disabled placeholder="经度" />
            </n-form-item>
            <n-form-item label="纬度">
              <n-input v-model:value="coordination.latitude" disabled placeholder="纬度" />
            </n-form-item>
          </div>
        </Transition>
        <n-form-item label="采样层次">
          <n-input v-model:value="displaySampleLayer" disabled placeholder="采样层次" />
        </n-form-item>

        <div style="display: flex;flex-direction: row;justify-content: space-between;gap: 36px;">
          <n-form-item
            label="天气现象" path="weather" :rule="{
              required: true,
              message: '请输入天气现象',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.weather" placeholder="请输入天气现象" />
          </n-form-item>

          <n-form-item
            label-width="60px" label="风向" path="windDirection" :rule="{
              required: true,
              message: '请输入风向',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.windDirection" placeholder="请输入风向" />
          </n-form-item>
        </div>

        <div style="display: flex;flex-direction: row;justify-content: space-between;gap: 36px;">
          <n-form-item
            label="盐度" path="saltExtent" :rule="{
              required: true,
              message: '请输入盐度',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.saltExtent" placeholder="请输入盐度" />
          </n-form-item>

          <n-form-item
            label-width="60px" label="PH值" path="phExtent" :rule="{
              required: true,
              message: '请输入PH值',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.phExtent" placeholder="请输入PH值" />
          </n-form-item>
        </div>

        <div style="display: flex;flex-direction: row;justify-content: space-between;gap: 36px;">
          <n-form-item
            label="气温" path="airTemperature" :rule="{
              required: true,
              message: '请输入气温',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.airTemperature" placeholder="请输入气温">
              <template #suffix>
                ℃
              </template>
            </n-input>
          </n-form-item>

          <n-form-item
            label-width="60px" label="水温" path="waterTemperature" :rule="{
              required: true,
              message: '请输入水温',
            // trigger: ['input', 'blur'],
            }"
          >
            <n-input v-model:value="modalForm.waterTemperature" placeholder="请输入水温">
              <template #suffix>
                ℃
              </template>
            </n-input>
          </n-form-item>
        </div>

        <n-form-item
          label="透明度" path="transparentExtent" :rule="{
            required: true,
            message: '请输入透明度',
          // trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.transparentExtent" placeholder="请输入透明度">
            <template #suffix>
              m
            </template>
          </n-input>
        </n-form-item>

        <n-form-item
          label="活性磷酸盐" path="activePhosphate" :rule="{
            required: true,
            message: '请输入活性磷酸盐含量',
          // trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.activePhosphate" placeholder="请输入活性磷酸盐含量">
            <template #suffix>
              mg/L
            </template>
          </n-input>
        </n-form-item>

        <n-form-item
          label="亚硝酸盐-氮" path="nitriteNitrogen" :rule="{
            required: true,
            message: '请输入亚硝酸盐-氮含量',
          // trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.nitriteNitrogen" placeholder="请输入亚硝酸盐-氮含量">
            <template #suffix>
              mg/L
            </template>
          </n-input>
        </n-form-item>

        <n-form-item
          label="硝酸盐-氮" path="nitrateNitrogen" :rule="{
            required: true,
            message: '请输入硝酸盐-氮含量',
          // trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.nitrateNitrogen" placeholder="请输入硝酸盐-氮含量">
            <template #suffix>
              mg/L
            </template>
          </n-input>
        </n-form-item>

        <n-form-item
          label="氨-氢" path="ammoniaHydrogen" :rule="{
            required: true,
            message: '请输入氨-氢含量',
          // trigger: ['input', 'blur'],
          }"
        >
          <n-input v-model:value="modalForm.ammoniaHydrogen" placeholder="请输入氨-氢含量">
            <template #suffix>
              mg/L
            </template>
          </n-input>
        </n-form-item>
      </n-form>
      <!-- <n-alert v-if="modalAction === 'edit'" type="warning" closable>
        详细信息需由用户本人补充修改
      </n-alert> -->
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { createDiscreteApi, NButton, NTag, NUpload } from 'naive-ui'

import api from '../microscopic-propagule-analysis/api.js'

const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})

const sampleTypeOption = ref([])

const sampleLayer = ref(1) // 复用改该字段即可

// let sampleTypeList = ref([])

const displaySampleLayer = computed(() => {
  switch (sampleLayer.value) {
    case 1:
      return '底层'
    case 2:
      return '表层'
  }
})

const displaySampleTypeList = computed(() => {
  return modalForm.value.sampleTypeList?.map(item => item.id)
})

async function getSampleTypeList() {
  const { data } = await api.getListSampleTypes()
  sampleTypeOption.value = data
}

async function getStationList() {
  const { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}

function changeSampleType(row) {
  console.log(row)
  // sampleTypeList.value = row
  // modalForm.value.sampleTypeList=row
  modalForm.value.sampleTypeList = row.map((id) => {
    const foundOption = sampleTypeOption.value.find(option => option.id === id)
    return foundOption
  }).filter(Boolean)
}

async function changeSelect(row) {
  if (row != null) {
    console.log(row)
    stationOption.value.filter((item) => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
      }
    })
  }
  else {
    coordination.value = {}
  }
}

onMounted(() => {
  queryItems.value.sampleLayer = sampleLayer.value
  $table.value?.handleSearch()
  getStationList()
  getSampleTypeList()
  modalForm.value.sampleLayer = sampleLayer.value
  // console.log(modalForm.value);
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '生物量',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '站点',
    width: 100,
    fixed: 'left',
    key: 'stationPointDistribute.name',
    render(row) {
      return h(NTag, { type: 'success' }, { default: () => row.stationPointDistribute.name })
    },
  },
  {
    title: '采样层次',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'primary' }, { default: () => displaySampleLayer.value })
    },
  },
  // {
  //   title: '经度', ellipsis: { tooltip: true },
  //   align: 'left',
  //   fixed: 'left',
  //   render(row) {
  //     return h('span', row.stationPointDistribute.longitude)
  //   },
  // },
  // {
  //   title: '纬度', ellipsis: { tooltip: true },
  //   align: 'left',
  //   fixed: 'left',
  //   render(row) {
  //     return h('span', row.stationPointDistribute.latitude)
  //   },
  // },
  {
    title: '天气现象',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.weather)
    },
  },
  {
    title: '风向',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.windDirection)
    },
  },
  {
    title: '盐度',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.saltExtent)
    },
  },
  {
    title: 'PH',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.phExtent)
    },
  },
  {
    title: '气温',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.airTemperature}℃`)
    },
  },
  {
    title: '水温',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.waterTemperature}℃`)
    },
  },
  {
    title: '透明度',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.transparentExtent}m`)
    },
  },
  {
    title: '活性磷酸盐',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.activePhosphate}mg/L`)
    },
  },
  {
    title: '亚硝酸盐-氮',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.nitriteNitrogen}mg/L`)
    },
  },
  {
    title: '硝酸盐',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.nitrateNitrogen}mg/L`)
    },
  },
  {
    title: '氨-氢',
    ellipsis: { tooltip: true },
    render(row) {
      return h('span', `${row.ammoniaHydrogen}mg/L`)
    },
  },

  {
    width: 130,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file) // 确保参数名为'file'，与后端匹配

    const result = await api.importBottomWaterSample(formData)

    const { code, msg } = result

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}

// 导出功能
async function handleExport() {
  try {
    // 优先使用新的API方法
    const response = await api.exportBottomWaterSample({
      // 不需要提供sampleType，API已经默认设置为1
    })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `底层海水水域数据_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}

.bottom-water-environmental {
  .operation-bar {
    margin-bottom: 16px;
    .n-button {
      margin-right: 8px;
    }
  }
}
</style>

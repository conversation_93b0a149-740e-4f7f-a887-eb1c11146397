package cn.dhbin.isme.ims.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 地图数据聚合DTO
 */
@Data
public class MapDataAggregationDto {
    
    /**
     * 调查中心信息
     */
    private StationPointScaleInfo stationPointScale;
    
    /**
     * 站点分布列表
     */
    private List<StationDistributeInfo> stationPointDistribute;
    
    /**
     * 数据类，调查中心信息
     */
    @Data
    public static class StationPointScaleInfo {
        private Integer id;
        private String name;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String description;
    }
    
    /**
     * 数据类，站点分布信息
     */
    @Data
    public static class StationDistributeInfo {
        private Integer id;
        private Integer scaleId;
        private Integer taskId;
        private Integer timesId;
        private String name;
        private BigDecimal longitude;
        private BigDecimal latitude;
        private String description;
        
        // 活动类型
        private Boolean wpActivities;
        private Boolean ciActivities;
        private Boolean mbActivities;
        private Boolean mrActivities;
        
        // 站点属性
        private String stationType;
        private Integer priority;
        private Boolean accessibility;
        private String equipmentRequirements;
        
        // 调查时间
        private LocalDateTime beforeInvestigate;
        private LocalDateTime afterInvestigate;
        
        // 关联数据
        private WaterPhWeatherDataInfo waterPhWeatherData;
        private ChemicalIonInfo chemicalIon;
        private List<AbundanceLayerSpeciesDataInfo> abundanceLayerSpeciesDataList;
        
        /**
         * 水文气象数据信息
         */
        @Data
        public static class WaterPhWeatherDataInfo {
            private Integer id;
            private Integer distributeId;
            private Integer sampleLayer;
            private String weather;
            private String windDirection;
            private BigDecimal saltExtent;
            private BigDecimal phExtent;
            private BigDecimal airTemperature;
            private BigDecimal waterTemperature;
            private BigDecimal transparentExtent;
            private LocalDateTime beforeInvestigate;
            private LocalDateTime afterInvestigate;
            private String evidenceFiles;
        }
        
        /**
         * 化学离子数据信息
         */
        @Data
        public static class ChemicalIonInfo {
            private Integer id;
            private Integer distributeId;
            private Integer sampleLayer;
            private BigDecimal activePhosphate;
            private BigDecimal nitriteNitrogen;
            private BigDecimal nitrateNitrogen;
            private BigDecimal ammoniaHydrogen;
            private LocalDateTime beforeInvestigate;
            private LocalDateTime afterInvestigate;
            private String evidenceFiles;
        }
        
        /**
         * 生物量数据信息
         */
        @Data
        public static class AbundanceLayerSpeciesDataInfo {
            private Integer id;
            private Integer distributeId;
            private Integer sampleType;
            private Integer abundance;
            private LocalDateTime beforeInvestigate;
            private LocalDateTime afterInvestigate;
            private String evidenceFiles;
            private List<SampleTypeInfo> sampleTypeList;
            
            @Data
            public static class SampleTypeInfo {
                private Integer id;
                private String name;
                private BigDecimal number;
            }
        }
    }
} 
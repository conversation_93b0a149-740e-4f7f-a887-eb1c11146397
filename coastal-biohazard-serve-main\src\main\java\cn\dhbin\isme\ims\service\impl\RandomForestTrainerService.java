package cn.dhbin.isme.ims.service.impl;

import org.springframework.stereotype.Service;
import weka.classifiers.trees.RandomForest;
import weka.core.Instances;

@Service
public class RandomForestTrainerService {

    public RandomForest trainModel(Instances data) throws Exception {
        RandomForest randomForest = new RandomForest();
        randomForest.setNumIterations(100);  // 设置随机森林的迭代次数（树的数量）
        randomForest.buildClassifier(data);  // 训练模型
        return randomForest;
    }
}

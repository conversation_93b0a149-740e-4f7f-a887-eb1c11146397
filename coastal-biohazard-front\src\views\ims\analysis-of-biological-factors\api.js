import { request } from '@/utils'

export default {
  create: data => request.post('/analysis-of-biological-factors', data),
  read: (params = {}) => request.get('/analysis-of-biological-factors', { params }),
  update: data => request.patch(`/analysis-of-biological-factors`, data),
  delete: id => request.delete(`/analysis-of-biological-factors/${id}`),
  getListStationPoints: scaleId => request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  getStationPoint: distributeId => request.get(`/station-point-distribute/${distributeId}`),
  getListSampleTypes: () => request.get('/analysis-sample-type/list'),
  uploadImg: file => request.post('/upload/img', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出Excel
  exportExcel: params => request.get('/analysis-of-biological-factors/export', {
    params,
    responseType: 'blob',
  }),

  // 导入Excel
  importExcel: data => request.post('/analysis-of-biological-factors/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 沉积物导出Excel
  exportSedimentExcel: params => request.get('/analysis-of-biological-factors/sediment/export', {
    params,
    responseType: 'blob',
  }),

  // 沉积物导入Excel
  importSedimentExcel: data => request.post('/analysis-of-biological-factors/sediment/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 底层水样导出Excel
  exportBottomWaterExcel: params => request.get('/analysis-of-biological-factors/bottom-water/export', {
    params,
    responseType: 'blob',
  }),

  // 底层水样导入Excel
  importBottomWaterExcel: data => request.post('/analysis-of-biological-factors/bottom-water/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 表层水样导出Excel
  exportSurfaceWaterExcel: params => request.get('/analysis-of-biological-factors/surface-water/export', {
    params,
    responseType: 'blob',
  }),

  // 表层水样导入Excel
  importSurfaceWaterExcel: data => request.post('/analysis-of-biological-factors/surface-water/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
}

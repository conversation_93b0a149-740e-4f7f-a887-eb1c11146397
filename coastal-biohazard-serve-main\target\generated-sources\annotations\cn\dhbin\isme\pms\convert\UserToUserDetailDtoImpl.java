package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.dto.UserDetailDto;
import cn.dhbin.isme.pms.domain.entity.User;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:01:57+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserToUserDetailDtoImpl implements UserToUserDetailDto {

    @Override
    public UserDetailDto to(User arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserDetailDto userDetailDto = new UserDetailDto();

        userDetailDto.setCreateTime( arg0.getCreateTime() );
        userDetailDto.setEnable( arg0.getEnable() );
        userDetailDto.setId( arg0.getId() );
        userDetailDto.setUpdateTime( arg0.getUpdateTime() );
        userDetailDto.setUsername( arg0.getUsername() );

        return userDetailDto;
    }
}

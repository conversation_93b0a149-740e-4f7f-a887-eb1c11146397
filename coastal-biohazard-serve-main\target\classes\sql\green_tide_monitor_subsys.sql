/*
 Navicat Premium Data Transfer

 Source Server         : Isla
 Source Server Type    : MySQL
 Source Server Version : 80032
 Source Host           : localhost:3306
 Source Schema         : green_tide_monitor_subsys

 Target Server Type    : MySQL
 Target Server Version : 80032
 File Encoding         : 65001

 Date: 07/11/2024 16:02:25
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for abundance_layer_species_data
-- ----------------------------
DROP TABLE IF EXISTS `abundance_layer_species_data`;
CREATE TABLE `abundance_layer_species_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `sample_type` int NULL DEFAULT 0 COMMENT '样品类型(0为沉积物，1为底层水样，2为表层水样)默认0',
  `abundance` int NULL DEFAULT NULL COMMENT '丰富度 ind./50g',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '微观繁殖体详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of abundance_layer_species_data
-- ----------------------------
INSERT INTO `abundance_layer_species_data` VALUES (1, 1, 1, 24, '2024-10-27 15:36:59', '2024-10-27 19:34:01');
INSERT INTO `abundance_layer_species_data` VALUES (2, 2, 1, 12, '2024-10-27 15:37:16', '2024-10-27 19:34:03');
INSERT INTO `abundance_layer_species_data` VALUES (3, 3, 1, 10, '2024-10-27 15:37:28', '2024-10-27 19:34:04');
INSERT INTO `abundance_layer_species_data` VALUES (4, 1, 0, 25, '2024-10-27 15:38:17', '2024-10-27 19:34:06');
INSERT INTO `abundance_layer_species_data` VALUES (5, 2, 0, 23, '2024-10-27 15:38:33', '2024-10-27 19:34:07');
INSERT INTO `abundance_layer_species_data` VALUES (6, 3, 0, 16, '2024-10-27 15:39:18', '2024-10-27 19:34:11');
INSERT INTO `abundance_layer_species_data` VALUES (7, 1, 2, 24, '2024-10-27 19:36:04', NULL);
INSERT INTO `abundance_layer_species_data` VALUES (8, 2, 2, 20, '2024-10-27 19:36:19', NULL);
INSERT INTO `abundance_layer_species_data` VALUES (9, 3, 2, 18, '2024-10-27 19:36:29', NULL);

-- ----------------------------
-- Table structure for abundance_sample
-- ----------------------------
DROP TABLE IF EXISTS `abundance_sample`;
CREATE TABLE `abundance_sample`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `abundance_id` int NULL DEFAULT NULL COMMENT '微观繁殖体id',
  `sample_id` int NULL DEFAULT NULL COMMENT '种类id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '微观繁殖体-种类中间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of abundance_sample
-- ----------------------------
INSERT INTO `abundance_sample` VALUES (4, 1, 1);
INSERT INTO `abundance_sample` VALUES (5, 1, 2);
INSERT INTO `abundance_sample` VALUES (6, 1, 3);
INSERT INTO `abundance_sample` VALUES (7, 2, 1);
INSERT INTO `abundance_sample` VALUES (8, 3, 1);
INSERT INTO `abundance_sample` VALUES (9, 4, 1);
INSERT INTO `abundance_sample` VALUES (10, 4, 2);
INSERT INTO `abundance_sample` VALUES (11, 4, 3);
INSERT INTO `abundance_sample` VALUES (12, 4, 4);
INSERT INTO `abundance_sample` VALUES (13, 5, 1);
INSERT INTO `abundance_sample` VALUES (14, 5, 2);
INSERT INTO `abundance_sample` VALUES (15, 5, 4);
INSERT INTO `abundance_sample` VALUES (16, 6, 1);
INSERT INTO `abundance_sample` VALUES (17, 6, 3);
INSERT INTO `abundance_sample` VALUES (18, 7, 1);
INSERT INTO `abundance_sample` VALUES (19, 7, 2);
INSERT INTO `abundance_sample` VALUES (20, 8, 1);
INSERT INTO `abundance_sample` VALUES (22, 10, 1);
INSERT INTO `abundance_sample` VALUES (23, 10, 2);
INSERT INTO `abundance_sample` VALUES (24, 11, 2);
INSERT INTO `abundance_sample` VALUES (25, 11, 3);
INSERT INTO `abundance_sample` VALUES (26, 11, 4);
INSERT INTO `abundance_sample` VALUES (27, 11, 1);
INSERT INTO `abundance_sample` VALUES (28, 12, 1);
INSERT INTO `abundance_sample` VALUES (29, 12, 2);
INSERT INTO `abundance_sample` VALUES (30, 13, 1);
INSERT INTO `abundance_sample` VALUES (31, 14, 2);
INSERT INTO `abundance_sample` VALUES (32, 14, 4);
INSERT INTO `abundance_sample` VALUES (33, 14, 1);
INSERT INTO `abundance_sample` VALUES (34, 15, 2);
INSERT INTO `abundance_sample` VALUES (35, 15, 1);
INSERT INTO `abundance_sample` VALUES (36, 15, 3);
INSERT INTO `abundance_sample` VALUES (39, 16, 2);
INSERT INTO `abundance_sample` VALUES (40, 16, 1);
INSERT INTO `abundance_sample` VALUES (42, 8, 2);
INSERT INTO `abundance_sample` VALUES (49, 9, 1);
INSERT INTO `abundance_sample` VALUES (50, 17, 1);
INSERT INTO `abundance_sample` VALUES (53, 18, 2);

-- ----------------------------
-- Table structure for algae_detection_records
-- ----------------------------
DROP TABLE IF EXISTS `algae_detection_records`;
CREATE TABLE `algae_detection_records`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `date` datetime NOT NULL,
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '地理位置',
  `algae_density` double NOT NULL COMMENT '浒苔密度',
  `water_temperature` double NOT NULL COMMENT '水温',
  `salinity` double NOT NULL COMMENT '盐度',
  `ph_value` double NOT NULL COMMENT 'pH值',
  `wind_speed` double NOT NULL COMMENT '风速',
  `rainfall` double NOT NULL COMMENT '降雨量',
  `light_intensity` double NOT NULL COMMENT '光照强度',
  `risk_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '预测数据模拟表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of algae_detection_records
-- ----------------------------
INSERT INTO `algae_detection_records` VALUES (1, '2024-10-03 12:00:00', '青岛海域', 0.05, 16.5, 32, 8.2, 5, 0, 600, '无风险');
INSERT INTO `algae_detection_records` VALUES (2, '2024-10-04 13:00:00', '日照海域', 0.03, 16, 31.5, 8.1, 4.5, 0, 550, '无风险');
INSERT INTO `algae_detection_records` VALUES (3, '2024-10-05 14:00:00', '青岛海域', 0.04, 16.2, 31.8, 8.3, 4.8, 0, 580, '无风险');
INSERT INTO `algae_detection_records` VALUES (4, '2024-10-06 15:00:00', '日照海域', 0.02, 15.8, 31.3, 8, 4.2, 0, 520, '潜在风险');
INSERT INTO `algae_detection_records` VALUES (5, '2024-10-07 16:00:00', '青岛海域', 0.06, 16.6, 32.2, 8.4, 5.5, 0, 620, '潜在风险');
INSERT INTO `algae_detection_records` VALUES (6, '2024-10-08 17:00:00', '日照海域', 0.03, 16.1, 31.6, 8.2, 4.7, 0, 570, '潜在风险');
INSERT INTO `algae_detection_records` VALUES (7, '2024-10-09 18:00:00', '青岛海域', 0.07, 16.8, 32.5, 8.5, 5.8, 0, 630, '高风险');
INSERT INTO `algae_detection_records` VALUES (8, '2024-10-10 19:00:00', '日照海域', 0.05, 16.4, 32, 8.3, 5.2, 0, 610, '高风险');
INSERT INTO `algae_detection_records` VALUES (9, '2024-10-11 20:00:00', '青岛海域', 0.08, 16.9, 32.6, 8.6, 6, 0, 640, '高风险');

-- ----------------------------
-- Table structure for gtsusys_staff_group
-- ----------------------------
DROP TABLE IF EXISTS `gtsusys_staff_group`;
CREATE TABLE `gtsusys_staff_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '一线人员单位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gtsusys_staff_group
-- ----------------------------
INSERT INTO `gtsusys_staff_group` VALUES (1, '江苏省海洋环境监测中心');
INSERT INTO `gtsusys_staff_group` VALUES (2, '南京海洋地质调查研究院');
INSERT INTO `gtsusys_staff_group` VALUES (3, '江苏省海岸带资源与环境研究所');
INSERT INTO `gtsusys_staff_group` VALUES (4, '苏州海洋生态环境保护站');
INSERT INTO `gtsusys_staff_group` VALUES (5, '江苏海洋大学环境科学系');
INSERT INTO `gtsusys_staff_group` VALUES (6, '江苏省水文地质工程地质队');
INSERT INTO `gtsusys_staff_group` VALUES (7, '南通市近岸海域研究室');
INSERT INTO `gtsusys_staff_group` VALUES (8, '江苏省环保厅海洋环境处');

-- ----------------------------
-- Table structure for gtsusys_staff_manage
-- ----------------------------
DROP TABLE IF EXISTS `gtsusys_staff_manage`;
CREATE TABLE `gtsusys_staff_manage`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '人员姓名',
  `gender` int NULL DEFAULT 1 COMMENT '性别（1男 0女 默认1）',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '身份证号',
  `group_id` int NOT NULL COMMENT '部门id',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '概述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '一线作业人员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of gtsusys_staff_manage
-- ----------------------------
INSERT INTO `gtsusys_staff_manage` VALUES (1, '蒋栋', 1, '320281200306047013', 1, NULL, '2024-10-27 15:51:09', '2024-10-30 19:31:11');

-- ----------------------------
-- Table structure for morphological_analysis_data
-- ----------------------------
DROP TABLE IF EXISTS `morphological_analysis_data`;
CREATE TABLE `morphological_analysis_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `abundance_id` int NOT NULL COMMENT '微观繁殖体id',
  `branch_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '分支图片',
  `cross_cut_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '横切图片',
  `surface_cell_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '表层细胞图片',
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2046750721 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '形态分析表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of morphological_analysis_data
-- ----------------------------
INSERT INTO `morphological_analysis_data` VALUES (1, 1, 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/6e075ac62c20417ab72e4de76f5eac2d.png', 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/63df2a2d7ac74b508931181a47a3b8ba.png', 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/93cfad986d3a4902b8574190133bcd54.png', NULL);

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parentId` int NULL DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `layout` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `keepAlive` tinyint NULL DEFAULT NULL,
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `show` tinyint NOT NULL DEFAULT 1 COMMENT '是否展示在页面菜单',
  `enable` tinyint NOT NULL DEFAULT 1,
  `order` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of permission
-- ----------------------------
INSERT INTO `permission` VALUES (1, '资源管理', 'Resource_Mgt', 'MENU', 2, '/pms/resource', NULL, 'i-fe:list', '/src/views/pms/resource/index.vue', NULL, NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (2, '系统管理', 'SysMgt', 'MENU', NULL, NULL, NULL, 'i-fe:grid', NULL, NULL, NULL, NULL, NULL, 1, 1, 9);
INSERT INTO `permission` VALUES (3, '角色管理', 'RoleMgt', 'MENU', 2, '/pms/role', NULL, 'i-fe:user-check', '/src/views/pms/role/index.vue', NULL, NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (4, '用户管理', 'UserMgt', 'MENU', 2, '/pms/user', NULL, 'i-fe:user', '/src/views/pms/user/index.vue', NULL, 1, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (5, '分配用户', 'RoleUser', 'MENU', 3, '/pms/role/user/:roleId', NULL, 'i-fe:user-plus', '/src/views/pms/role/role-user.vue', NULL, NULL, NULL, NULL, 0, 1, 1);
INSERT INTO `permission` VALUES (7, '图片上传', 'ImgUpload', 'MENU', 6, '/demo/upload', NULL, 'i-fe:image', '/src/views/demo/upload/index.vue', NULL, 1, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (8, '个人资料', 'UserProfile', 'MENU', NULL, '/profile', NULL, 'i-fe:user', '/src/views/profile/index.vue', NULL, NULL, NULL, NULL, 0, 1, 99);
INSERT INTO `permission` VALUES (9, '基本信息管理', 'Base', 'MENU', NULL, '/base', NULL, 'i-fe:inbox', NULL, NULL, NULL, NULL, NULL, 1, 1, 8);
INSERT INTO `permission` VALUES (13, '创建新用户', 'AddUser', 'BUTTON', 4, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (14, '图标 Icon', 'Icon', 'MENU', 9, '/base/icon', NULL, 'i-fe:feather', '/src/views/base/unocss-icon.vue', NULL, NULL, NULL, NULL, 1, 1, 5);
INSERT INTO `permission` VALUES (17, '采样记录', 'SamplingFieldRecord', 'MENU', 16, '/ims/sampling-field-record', NULL, 'i-fe:wind', '/src/views/ims/field-investigation/sampling-field-record.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (18, '微观繁殖体综合调查', 'MicroscopicPropaguleAnalysis', 'MENU', NULL, NULL, NULL, 'i-simple-icons:juejin', NULL, '', NULL, NULL, NULL, 1, 1, 4);
INSERT INTO `permission` VALUES (19, '沉积物微观繁殖体', 'MicroshapeRepuduceRecord', 'MENU', 18, '/ims/microshape-repuduce-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/microshape-repuduce-record.vue', '', NULL, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (20, '底层水样微观繁殖体', 'BottomWaterSampleRecord', 'MENU', 18, '/ims/bottom-water-sample-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/bottom-water-sample-record.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (21, '表层水样微观繁殖体', 'SurfaceWaterSampleRecord', 'MENU', 18, '/ims/surface-water-sample-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/surface-water-sample-record.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (22, '数据大屏', 'DataView', 'MENU', NULL, '/data-view', NULL, 'i-fe:airplay', '/src/views/ims/data-view/index.vue', 'empty', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (23, '水文分析', 'Home', 'MENU', NULL, '/', NULL, 'i-fe:trending-up', '/src/views/home/<USER>', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (25, '形态分析', 'MorphologicalAnalysis', 'MENU', 18, '/ims/morphological-analysis', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/morphological-analysis.vue', '', NULL, NULL, NULL, 0, 1, 0);
INSERT INTO `permission` VALUES (26, '浒苔绿潮现场调查', 'TimeSpaceAnalysis', 'MENU', NULL, '', NULL, 'i-fe:navigation', NULL, '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (27, '一线作业人员管理', 'OperatorManagement', 'MENU', 26, '/ims/operator-management', NULL, 'i-fe:users', '/src/views/ims/time-space-analysis/operator-management.vue', '', NULL, NULL, NULL, 1, 1, 4);
INSERT INTO `permission` VALUES (28, '水域环境监测与分析', 'WaterEnvironmentalMonitoringAndAnalysis', 'MENU', NULL, NULL, NULL, 'i-fe:sun', NULL, '', NULL, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (29, '表层海水水域', 'SurfaceWaterEnvironmental', 'MENU', 28, '/ims/surface-water-environmental', NULL, 'i-fe:wind', '/src/views/ims/water-environmental-monitoring-and-analysis/surface-water-environmental.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (30, '底层海水水域', 'BottomWaterEnvironmental', 'MENU', 28, '/ims/bottom-water-environmental', NULL, 'i-fe:wind', '/src/views/ims/water-environmental-monitoring-and-analysis/bottom-water-environmental.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (31, '空间调查中心', 'SpacialScale', 'MENU', 26, '/ims/spacial-scale', NULL, 'i-fe:aperture', '/src/views/ims/time-space-analysis/spacial-scale.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (32, '调查站点分布', 'SurveyStation', 'MENU', 26, '/ims/survey-station', NULL, 'i-fe:map', '/src/views/ims/time-space-analysis/survey-station.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (33, '调查中心站点分布', 'JsVue', 'MENU', 26, '/ims/JsVue', NULL, 'i-fe:map-pin', '/src/views/ims/time-space-analysis/JsVue.vue', 'empty', NULL, NULL, NULL, 0, 1, 0);
INSERT INTO `permission` VALUES (34, '藻体种类管理', 'SampleType', 'MENU', 9, '/ims/sample-type', NULL, 'i-fe:droplet', '/src/views/ims/microscopic-propagule-analysis/sample-type.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (35, '人员单位管理', 'GtsusysStaffGroup', 'MENU', 9, '/ims/gtsusys-staff-group', NULL, 'i-fe:linkedin', '/src/views/ims/time-space-analysis/gtsusys-staff-group.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (36, '调查时间范围', 'SurveyTimeRange', 'MENU', 26, '/ims/survey-time-range', NULL, 'i-fe:clock', '/src/views/ims/time-space-analysis/survey-time-range.vue', '', NULL, NULL, NULL, 0, 1, 0);

-- ----------------------------
-- Table structure for profile
-- ----------------------------
DROP TABLE IF EXISTS `profile`;
CREATE TABLE `profile`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `gender` int NULL DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `userId` int NOT NULL,
  `nickName` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `userId`(`userId` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of profile
-- ----------------------------
INSERT INTO `profile` VALUES (1, NULL, 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80', NULL, NULL, 1, 'Admin');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enable` tinyint NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_code_name`(`code` ASC, `name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, 'SUPER_ADMIN', '超级管理员', 1);
INSERT INTO `role` VALUES (2, 'ROLE_QA', '质检员', 1);

-- ----------------------------
-- Table structure for role_permissions_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions_permission`;
CREATE TABLE `role_permissions_permission`  (
  `roleId` int NOT NULL,
  `permissionId` int NOT NULL,
  PRIMARY KEY (`roleId`, `permissionId`) USING BTREE,
  UNIQUE INDEX `unique_roleId_permissionId`(`roleId` ASC, `permissionId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role_permissions_permission
-- ----------------------------
INSERT INTO `role_permissions_permission` VALUES (2, 1);
INSERT INTO `role_permissions_permission` VALUES (2, 2);
INSERT INTO `role_permissions_permission` VALUES (2, 3);
INSERT INTO `role_permissions_permission` VALUES (2, 4);
INSERT INTO `role_permissions_permission` VALUES (2, 5);
INSERT INTO `role_permissions_permission` VALUES (2, 9);
INSERT INTO `role_permissions_permission` VALUES (2, 10);
INSERT INTO `role_permissions_permission` VALUES (2, 11);
INSERT INTO `role_permissions_permission` VALUES (2, 12);
INSERT INTO `role_permissions_permission` VALUES (2, 14);
INSERT INTO `role_permissions_permission` VALUES (2, 15);

-- ----------------------------
-- Table structure for sample_type
-- ----------------------------
DROP TABLE IF EXISTS `sample_type`;
CREATE TABLE `sample_type`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '样品种类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sample_type
-- ----------------------------
INSERT INTO `sample_type` VALUES (1, '浒苔');
INSERT INTO `sample_type` VALUES (2, '缘管浒苔');
INSERT INTO `sample_type` VALUES (3, '曲浒苔');
INSERT INTO `sample_type` VALUES (4, '盘苔');

-- ----------------------------
-- Table structure for station_point_distribute
-- ----------------------------
DROP TABLE IF EXISTS `station_point_distribute`;
CREATE TABLE `station_point_distribute`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `scale_id` int NOT NULL COMMENT '空间范围id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '监测站位（名称）',
  `longitude` decimal(9, 6) NOT NULL COMMENT '经度',
  `latitude` decimal(9, 6) NOT NULL COMMENT '纬度',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '概述',
  `before_investigate` datetime NOT NULL COMMENT '调查开始时间',
  `after_investigate` datetime NOT NULL COMMENT '调查结束时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调查站位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of station_point_distribute
-- ----------------------------
INSERT INTO `station_point_distribute` VALUES (1, 1, 'S1-1', 119.678879, 34.551811, NULL, '2024-10-25 15:00:00', '2024-10-25 18:00:00', '2024-10-27 15:33:45', '2024-10-27 15:34:55');
INSERT INTO `station_point_distribute` VALUES (2, 1, 'S1-2', 119.799728, 34.608345, NULL, '2024-10-26 12:00:00', '2024-10-26 15:00:00', '2024-10-27 15:35:08', '2024-10-27 15:35:11');
INSERT INTO `station_point_distribute` VALUES (3, 1, 'S1-3', 119.921265, 34.665970, NULL, '2024-10-27 13:00:00', '2024-10-27 16:00:00', '2024-10-27 15:35:51', NULL);
INSERT INTO `station_point_distribute` VALUES (5, 2, 'S2-1', 119.999999, 34.665790, NULL, '2024-10-02 10:49:55', '2024-11-01 10:49:55', '2024-11-01 10:50:20', NULL);

-- ----------------------------
-- Table structure for station_point_scale
-- ----------------------------
DROP TABLE IF EXISTS `station_point_scale`;
CREATE TABLE `station_point_scale`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '定点名称',
  `longitude` decimal(9, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(9, 6) NULL DEFAULT NULL COMMENT '纬度',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '空间范围描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '时空范围点位表（单点）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of station_point_scale
-- ----------------------------
INSERT INTO `station_point_scale` VALUES (1, '调查中心1', 121.123456, 34.241593, '这是调查中心1，位于****', '2024-10-27 15:29:30', '2024-10-31 18:24:15');
INSERT INTO `station_point_scale` VALUES (2, '调查中心2', 121.231410, 34.241952, '这是调查中心2，位于****', '2024-11-01 10:11:49', NULL);

-- ----------------------------
-- Table structure for survey_time_range
-- ----------------------------
DROP TABLE IF EXISTS `survey_time_range`;
CREATE TABLE `survey_time_range`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NOT NULL COMMENT '站位id',
  `before_investigate` datetime NOT NULL COMMENT '调查开始时间',
  `after_investigate` datetime NOT NULL COMMENT '调查结束时间',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调查时间范围表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of survey_time_range
-- ----------------------------
INSERT INTO `survey_time_range` VALUES (3, 1, '2024-11-03 15:00:00', '2024-11-03 18:00:00', '测试');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `enable` tinyint NOT NULL DEFAULT 1,
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2a$10$FsAafxTTVVGXfIkJqvaiV.1vPfq4V9HW298McPldJgO829PR52a56', 1, '2023-11-18 16:18:59.150632', '2023-11-18 16:18:59.150632');

-- ----------------------------
-- Table structure for user_roles_role
-- ----------------------------
DROP TABLE IF EXISTS `user_roles_role`;
CREATE TABLE `user_roles_role`  (
  `userId` int NOT NULL,
  `roleId` int NOT NULL,
  PRIMARY KEY (`userId`, `roleId`) USING BTREE,
  UNIQUE INDEX `unique_userId_roleId`(`userId` ASC, `roleId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_roles_role
-- ----------------------------
INSERT INTO `user_roles_role` VALUES (1, 1);
INSERT INTO `user_roles_role` VALUES (1, 2);

-- ----------------------------
-- Table structure for water_ph_weather_data
-- ----------------------------
DROP TABLE IF EXISTS `water_ph_weather_data`;
CREATE TABLE `water_ph_weather_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `sample_layer` int NULL DEFAULT 2 COMMENT '采样层次（1 底层 2表层）默认2',
  `weather` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '天气现象',
  `wind_direction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '风向',
  `salt_extent` decimal(5, 2) NULL DEFAULT NULL COMMENT '盐度',
  `ph_extent` decimal(4, 2) NULL DEFAULT NULL COMMENT 'PH值',
  `air_temperature` decimal(3, 1) NULL DEFAULT NULL COMMENT '气温,单位℃',
  `water_temperature` decimal(4, 2) NULL DEFAULT NULL COMMENT '水温,单位℃',
  `transparent_extent` decimal(4, 2) NULL DEFAULT NULL COMMENT '透明度,单位m',
  `active_phosphate` decimal(5, 4) NULL DEFAULT NULL COMMENT '活性磷酸盐含量,单位mg/L',
  `nitrite_nitrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '亚硝酸盐-氮含量,单位mg/L',
  `nitrate_nitrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '硝酸盐-氮含量,单位mg/L',
  `ammonia_hydrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '氨-氢,单位mg/L',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '微观藻体水文特征表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of water_ph_weather_data
-- ----------------------------
INSERT INTO `water_ph_weather_data` VALUES (1, 1, 2, '晴转多云', '西北风', 31.20, 8.14, 16.2, 14.70, 0.00, 0.0166, 0.0190, 0.0916, 0.0080, '2024-10-27 15:40:40', NULL);
INSERT INTO `water_ph_weather_data` VALUES (2, 2, 2, '晴转多云', '西北风', 29.80, 8.17, 8.4, 9.30, 1.30, 0.0127, 0.0188, 0.0869, 0.0143, '2024-10-27 15:41:47', '2024-10-31 16:40:29');
INSERT INTO `water_ph_weather_data` VALUES (3, 3, 2, '晴转多云', '西北风', 31.20, 8.20, 8.3, 8.60, 2.20, 0.0100, 0.0197, 0.2557, 0.0665, '2024-10-27 15:43:17', '2024-10-31 16:40:31');

-- ----------------------------
-- Procedure structure for GenerateInsertStatements
-- ----------------------------
DROP PROCEDURE IF EXISTS `GenerateInsertStatements`;
delimiter ;;
CREATE PROCEDURE `GenerateInsertStatements`()
BEGIN
    DECLARE i INT DEFAULT 1;

    -- 创建临时表存储生成的SQL语句
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_insert_statements (
        insert_statement TEXT
    );

    -- 清空临时表
    TRUNCATE TABLE temp_insert_statements;

    -- 循环生成INSERT语句
    WHILE i <= 1000 DO
        INSERT INTO temp_insert_statements (insert_statement)
        VALUES (
            CONCAT(
                'INSERT INTO algae_detection_records (date, location, algae_density, water_temperature, salinity, ph_value, wind_speed, rainfall, light_intensity) ',
                'VALUES (',
                "'",
                DATE_FORMAT(DATE_ADD('2024-10-11', INTERVAL (i - 1) HOUR), '%Y-%m-%d %H:00:00'),
                "','",
                CASE WHEN i MOD 3 = 1 THEN '青岛海域' ELSE '日照海域' END,
                "',",
                0.05 + (i - 1) * 0.001,
                ",",
                16.5 + (i - 1) * 0.01,
                ",",
                32.0 + (i - 1) * 0.01,
                ",",
                8.2 + (i - 1) * 0.01,
                ",",
                5.0 + (i - 1) * 0.01,
                ",",
                0.0,
                ",",
                600.0 + (i - 1) * 1,
                ");"
            )
        );
        SET i = i + 1;
    END WHILE;

    -- 打印生成的SQL语句
    SELECT insert_statement FROM temp_insert_statements;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;

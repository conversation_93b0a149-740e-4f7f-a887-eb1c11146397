package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.SurveyRouteTask;
import cn.dhbin.isme.ims.mapper.SurveyRouteTaskMapper;
import cn.dhbin.isme.ims.service.SurveyRouteTaskService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 调查航线/任务服务实现
 */
@Service
@RequiredArgsConstructor
public class SurveyRouteTaskServiceImpl extends ServiceImpl<SurveyRouteTaskMapper, SurveyRouteTask> 
        implements SurveyRouteTaskService {

    @Override
    public List<SurveyRouteTask> getRoutesByScaleId(Integer scaleId) {
        LambdaQueryWrapper<SurveyRouteTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SurveyRouteTask::getScaleId, scaleId)
                   .eq(SurveyRouteTask::getStatus, 1)
                   .orderByAsc(SurveyRouteTask::getCreateTime);
        return list(queryWrapper);
    }
} 
<template>
  <AppCard class="flex items-center px-12" border-b="1px solid light_border dark:dark_border">
    <MenuCollapse />

    <AppTab class="w-0 flex-1 px-12" />

    <span class="mx-6 opacity-20">|</span>

    <div class="flex flex-shrink-0 items-center px-12 text-18">
      <ToggleTheme />

      <Fullscreen />

      <!-- 导出按钮 -->
      <n-tooltip trigger="hover" placement="top" style="max-width: 150px">
        <template #trigger>
          <n-button
            tertiary
            circle
            type="warning"
            @click="handleExport"
          >
            <template #icon>
              <n-icon>
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><ellipse cx="12" cy="6" rx="8" ry="3"></ellipse><path d="M4 6v6c0 1.657 3.582 3 8 3a19.84 19.84 0 0 0 3.302-.267M20 12V6"></path><path d="M4 12v6c0 1.599 3.335 2.905 7.538 2.995M20 14v-2m-6 7h7m-3-3l3 3l-3 3"></path></g></svg>
              </n-icon>
            </template>
          </n-button>
        </template>
        导出数据库
      </n-tooltip>

      <!-- 导入按钮 -->
      <n-tooltip trigger="hover" placement="top" style="max-width: 150px">
        <template #trigger>
          <n-upload
            style="width: auto;padding: 0 16px"
            :default-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
          >
            <n-button
              tertiary
              circle
              type="warning"
            >
              <template #icon>
                <n-icon>
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <ellipse cx="12" cy="6" rx="8" ry="3"></ellipse>
                      <path d="M4 6v8m5.009.783c.924.14 1.933.217 2.991.217c4.418 0 8-1.343 8-3V6"></path>
                      <path d="M11.252 20.987c.246.009.496.013.748.013c4.418 0 8-1.343 8-3v-6M2 19h7m-3-3l3 3l-3 3"></path>
                    </g>
                  </svg>
                </n-icon>
              </template>
            </n-button>
          </n-upload>
        </template>
        导入数据库
      </n-tooltip>

      <ThemeSetting class="mr-16" />
      <UserAvatar />
    </div>
  </AppCard>

  <!-- 全局加载提示 -->
  <n-modal v-model:show="loading" class="h-48 w-48">
    <n-spin size="large" />
  </n-modal>
</template>

<script setup>
import ThemeSetting from '@/components/common/ThemeSetting.vue'
import { ToggleTheme } from '@/components/index.js'
import { Fullscreen, UserAvatar } from '@/layouts/components/index.js'
import { ref } from 'vue'
import { exportDatabase, importDatabase } from './api.js'

const loading = ref(false)

// 导出数据库
async function handleExport() {
  const dbName = 'coastal_biohazard' // 根据实际情况获取数据库名
  try {
    loading.value = true
    const res = await exportDatabase(dbName)
    // 处理文件下载
    const url = window.URL.createObjectURL(new Blob([res]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `${dbName}.sql`)
    document.body.appendChild(link)
    link.click()
    link.remove()
    $message.success('导出成功！')
  }
  catch (error) {
    $message.error('导出失败，请检查权限或网络')
  }
  finally {
    loading.value = false
  }
}

// 处理文件上传
async function handleFileChange(event) {
  const file = event.file.file;
  if (!file || !file.name.endsWith('.sql')) {
    $message.error('仅支持SQL文件');
    return;
  }

  $message.loading('正在导入数据库...');
  try {
    const res = await importDatabase(file);
    $message.success(res.data);
    // 刷新页面
    location.reload();
  } catch (error) {
    $message.error('导入失败：' + error.message);
  }
}
</script>

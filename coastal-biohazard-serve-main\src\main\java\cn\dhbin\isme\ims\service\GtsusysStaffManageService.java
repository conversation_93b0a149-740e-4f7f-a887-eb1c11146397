package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.GtsusysStaffManageDto;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffManage;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffManageRequest;
import cn.dhbin.isme.ims.domain.request.ManageBatchInsertRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 一线作业人员表(GtsusysStaffManage)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
public interface GtsusysStaffManageService extends IService<GtsusysStaffManage> {
    Page<GtsusysStaffManageDto> queryPage(GtsusysStaffManageRequest request);

    boolean batchInsert(ManageBatchInsertRequest request);

    List<GtsusysStaffManageDto> queryList(GtsusysStaffManageRequest request);
}


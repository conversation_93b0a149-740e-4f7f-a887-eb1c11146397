package cn.dhbin.isme.ims.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("algae_detection_records")
public class AlgaeDetectionRecord {

    private Long id; //

    private Date date; // 记录日期

    private String location; // 地理位置，可以是经纬度或者地区名称

    private double algaeDensity; // 浒苔密度

    private double waterTemperature; // 水温

    private double salinity; // 盐度

    private double phValue; // pH值

    private double windSpeed; // 风速

    private double rainfall; // 降雨量

    private double lightIntensity; // 光照强度

    private String riskLevel; // 新增的名义型属性

    // 构造函数
    public AlgaeDetectionRecord() {}

    public AlgaeDetectionRecord(Date date, String location, double algaeDensity, double waterTemperature, double salinity, double phValue, double windSpeed, double rainfall, double lightIntensity,String riskLevel) {
        this.date = date;
        this.location = location;
        this.algaeDensity = algaeDensity;
        this.waterTemperature = waterTemperature;
        this.salinity = salinity;
        this.phValue = phValue;
        this.windSpeed = windSpeed;
        this.rainfall = rainfall;
        this.lightIntensity = lightIntensity;
        this.riskLevel=riskLevel;
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public double getAlgaeDensity() {
        return algaeDensity;
    }

    public void setAlgaeDensity(double algaeDensity) {
        this.algaeDensity = algaeDensity;
    }

    public double getWaterTemperature() {
        return waterTemperature;
    }

    public void setWaterTemperature(double waterTemperature) {
        this.waterTemperature = waterTemperature;
    }

    public double getSalinity() {
        return salinity;
    }

    public void setSalinity(double salinity) {
        this.salinity = salinity;
    }

    public double getPhValue() {
        return phValue;
    }

    public void setPhValue(double phValue) {
        this.phValue = phValue;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public double getRainfall() {
        return rainfall;
    }

    public void setRainfall(double rainfall) {
        this.rainfall = rainfall;
    }

    public double getLightIntensity() {
        return lightIntensity;
    }

    public void setLightIntensity(double lightIntensity) {
        this.lightIntensity = lightIntensity;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }
}

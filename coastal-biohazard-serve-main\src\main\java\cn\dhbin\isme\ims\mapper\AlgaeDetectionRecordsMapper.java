package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.AlgaeDetectionRecord;
import cn.dhbin.isme.ims.domain.entity.AlgaeDetectionRecords;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 预测数据模拟表(AlgaeDetectionRecords)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:30:42
 */
@Mapper
public interface AlgaeDetectionRecordsMapper extends BaseMapper<AlgaeDetectionRecords> {
    @Select("SELECT * from algae_detection_records")
    List<AlgaeDetectionRecord> getAllRecords();
}


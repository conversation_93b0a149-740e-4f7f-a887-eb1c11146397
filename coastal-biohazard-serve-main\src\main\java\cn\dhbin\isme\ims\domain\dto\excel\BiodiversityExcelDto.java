package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class BiodiversityExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点ID")
    @ColumnWidth(15)
    private Integer distributeId;

    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;

    @ExcelProperty("优势种")
    @ColumnWidth(20)
    private String name;

    @ExcelProperty("丰度")
    @ColumnWidth(20)
    private String abundance;

    @ExcelProperty("生物多样性")
    @ColumnWidth(20)
    private String biodiversity;
}

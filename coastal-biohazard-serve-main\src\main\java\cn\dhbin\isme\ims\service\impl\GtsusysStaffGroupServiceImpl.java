package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffGroup;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffGroupRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.GtsusysStaffGroupMapper;
import cn.dhbin.isme.ims.service.GtsusysStaffGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 一线人员单位表(GtsusysStaffGroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:34:41
 */
@Service("gtsusysStaffGroupService")
public class GtsusysStaffGroupServiceImpl extends ServiceImpl<GtsusysStaffGroupMapper, GtsusysStaffGroup> implements GtsusysStaffGroupService {

    @Autowired
    private GtsusysStaffGroupMapper gtsusysStaffGroupMapper;

    @Override
    public Page<GtsusysStaffGroup> queryPage(GtsusysStaffGroupRequest request) {
        IPage<GtsusysStaffGroup> qp = request.toPage();
        LambdaQueryWrapper<GtsusysStaffGroup> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(GtsusysStaffGroup::getName, request.getName());
        }

        IPage<GtsusysStaffGroup> ret = gtsusysStaffGroupMapper.selectPage(qp, queryWrapper);


        return Page.convert(ret);
    }
    @Override
    public List<GtsusysStaffGroup> queryList(GtsusysStaffGroupRequest request) {
        // 创建查询条件
        LambdaQueryWrapper<GtsusysStaffGroup> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(GtsusysStaffGroup::getName, request.getName());
        }

        // 查询数据
        return gtsusysStaffGroupMapper.selectList(queryWrapper);
    }
}


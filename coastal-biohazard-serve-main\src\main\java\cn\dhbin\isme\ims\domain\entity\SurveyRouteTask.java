package cn.dhbin.isme.ims.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 调查航线/任务实体
 */
@Data
@TableName("survey_route_task")
public class SurveyRouteTask {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 调查中心ID，关联station_point_scale表
     */
    private Integer scaleId;
    
    /**
     * 航线/任务名称
     */
    private String name;
    
    /**
     * 任务编码
     */
    private String code;
    
    /**
     * 航线描述
     */
    private String description;
    
    /**
     * 航线起点经度
     */
    private BigDecimal startLongitude;
    
    /**
     * 航线起点纬度
     */
    private BigDecimal startLatitude;
    
    /**
     * 航线终点经度
     */
    private BigDecimal endLongitude;
    
    /**
     * 航线终点纬度
     */
    private BigDecimal endLatitude;
    
    /**
     * 总距离(km)
     */
    private BigDecimal totalDistance;
    
    /**
     * 预计持续时间(小时)
     */
    private Integer estimatedDuration;
    
    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 
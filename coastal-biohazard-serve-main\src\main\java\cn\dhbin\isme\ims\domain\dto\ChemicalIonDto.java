package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ChemicalIonDto implements Serializable {
    private Integer id;
    
    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点信息

    /**
     * 采样层次(1底层 2表层)
     **/
    private Integer sampleLayer;
    
    /**
     * 活性磷酸盐含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal activePhosphate;
    
    /**
     * 亚硝酸盐-氮含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal nitriteNitrogen;
    
    /**
     * 硝酸盐-氮含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal nitrateNitrogen;
    
    /**
     * 氨-氢,单位mg/L - decimal(5,4)
     **/
    private BigDecimal ammoniaHydrogen;
    
    private Date createTime;
    
    private Date updateTime;
}



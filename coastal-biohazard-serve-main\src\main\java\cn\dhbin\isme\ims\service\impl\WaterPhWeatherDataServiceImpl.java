package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.WaterPhWeatherDataDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.domain.request.WaterPhWeatherDataRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.WaterPhWeatherDataMapper;
import cn.dhbin.isme.ims.service.WaterPhWeatherDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
@Service("waterPhWeatherDataService")
public class WaterPhWeatherDataServiceImpl extends ServiceImpl<WaterPhWeatherDataMapper, WaterPhWeatherData> implements WaterPhWeatherDataService {

    @Autowired
    private WaterPhWeatherDataMapper waterPhWeatherDataMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<WaterPhWeatherDataDto> queryPage(WaterPhWeatherDataRequest request) {
        IPage<WaterPhWeatherData> qp = request.toPage();
        LambdaQueryWrapper<WaterPhWeatherData> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getSampleLayer() != null) {
            queryWrapper.eq(WaterPhWeatherData::getSampleLayer, request.getSampleLayer());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(WaterPhWeatherData::getDistributeId, request.getDistributeId());
        }

        IPage<WaterPhWeatherData> ret = waterPhWeatherDataMapper.selectPage(qp, queryWrapper);

        IPage<WaterPhWeatherDataDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            WaterPhWeatherDataDto dataDto = new WaterPhWeatherDataDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public List<WaterPhWeatherData> getWaterPhWeatherDataByDistributeIds(List<Integer> distributeIds) {
        // 获取所有 distributeId 对应的 name
        List<StationPointDistribute> distributes = stationPointDistributeMapper.selectBatchIds(distributeIds);
        Map<Integer, String> distributeNameMap = distributes.stream()
                .collect(Collectors.toMap(StationPointDistribute::getId, StationPointDistribute::getName));

        // 查询 water_ph_weather_data 表中的记录
        List<WaterPhWeatherData> weatherDataList = waterPhWeatherDataMapper.selectBatchByDistributeIds(distributeIds);

        // 将 distributeName 附加到每个 WaterPhWeatherData 对象中
        for (WaterPhWeatherData data : weatherDataList) {
            data.setDistributeName(distributeNameMap.get(data.getDistributeId()));
        }

        return weatherDataList;
    }
    @Override
    public List<WaterPhWeatherDataDto> queryList(WaterPhWeatherDataRequest request) {
        // 创建查询条件
        LambdaQueryWrapper<WaterPhWeatherData> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getDistributeId() != null) {
            queryWrapper.eq(WaterPhWeatherData::getDistributeId, request.getDistributeId());
        }

        if (request.getSampleLayer() != null) {
            queryWrapper.eq(WaterPhWeatherData::getSampleLayer, request.getSampleLayer());
        }

        // 查询数据
        List<WaterPhWeatherData> list = waterPhWeatherDataMapper.selectList(queryWrapper);

        // 转换为DTO
        return list.stream().map(data -> {
            WaterPhWeatherDataDto dataDto = new WaterPhWeatherDataDto();
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点信息
            if (data.getDistributeId() != null) {
                StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
                dataDto.setStationPointDistribute(stationPointDistribute);
            }

            return dataDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WaterPhWeatherDataDto> getByStationId(Integer stationId) {
        // 创建查询条件
        LambdaQueryWrapper<WaterPhWeatherData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaterPhWeatherData::getDistributeId, stationId);
        queryWrapper.orderByDesc(WaterPhWeatherData::getCreateTime);

        // 查询数据
        List<WaterPhWeatherData> list = waterPhWeatherDataMapper.selectList(queryWrapper);

        // 转换为DTO
        return list.stream().map(data -> {
            WaterPhWeatherDataDto dataDto = new WaterPhWeatherDataDto();
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点信息
            if (data.getDistributeId() != null) {
                StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
                dataDto.setStationPointDistribute(stationPointDistribute);
            }

            return dataDto;
        }).collect(Collectors.toList());
    }

}


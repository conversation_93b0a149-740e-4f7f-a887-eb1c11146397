package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.auth.RoleType;
import cn.dhbin.isme.common.auth.Roles;
import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.service.impl.SurfaceWaterEnvironmentalExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/surface-water-environmental")
@RequiredArgsConstructor
@Tag(name = "表层海水水域Excel导入导出")
@Slf4j
public class SurfaceWaterEnvironmentalExcelController {
    private final SurfaceWaterEnvironmentalExcelService excelService;

    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导出表层海水水域Excel")
    public ResponseEntity<byte[]> exportToExcel(@RequestParam(required = false) Integer sampleLayer) throws Exception {
        try {
            log.info("开始导出表层海水水域Excel: sampleLayer={}", sampleLayer);
            byte[] excelContent = excelService.exportToExcel(sampleLayer);
            
            // 如果没有数据，则生成一个只有提示信息的Excel
            if (excelContent.length < 100) { // 简单判断空内容
                log.warn("表层海水水域数据为空，生成提示信息Excel");
                excelContent = generateEmptyDataExcel("表层海水水域数据");
            }
            
            String filename = URLEncoder.encode("表层海水水域数据.xlsx", StandardCharsets.UTF_8.toString());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelContent);
        } catch (Exception e) {
            log.error("导出表层海水水域Excel失败", e);
            throw new BizException(BizResponseCode.ERR_11011, "导出失败: " + e.getMessage());
        }
    }

    /**
     * 生成一个空数据的Excel，但包含提示信息
     */
    private byte[] generateEmptyDataExcel(String sheetName) throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"站点", "采样层次", "天气现象", "风向", "盐度", "PH值", "气温(℃)", "水温(℃)", "透明度(m)", "创建时间", "更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建提示行
            Row tipRow = sheet.createRow(1);
            Cell tipCell = tipRow.createCell(0);
            tipCell.setCellValue("暂无数据，请先添加数据再导出");
            
            // 合并单元格用于显示提示信息
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 10));
            
            // 设置样式
            CellStyle tipStyle = workbook.createCellStyle();
            tipStyle.setAlignment(HorizontalAlignment.CENTER);
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(IndexedColors.RED.getIndex());
            tipStyle.setFont(font);
            tipCell.setCellStyle(tipStyle);
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导入表层海水水域Excel")
    public R<Void> importFromExcel(
            @RequestParam(required = false) Integer sampleLayer, 
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("开始导入表层海水水域Excel: sampleLayer={}, fileName={}", sampleLayer, file.getOriginalFilename());
            
            if (file == null || file.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请选择要导入的Excel文件"));
            }
            
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请上传Excel文件(.xlsx或.xls格式)"));
            }
            
            excelService.importFromExcel(file, sampleLayer);
            log.info("表层海水水域Excel导入成功");
            return R.ok();
        } catch (Exception e) {
            log.error("导入表层海水水域Excel失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11011, "导入失败：" + e.getMessage()));
        }
    }
} 
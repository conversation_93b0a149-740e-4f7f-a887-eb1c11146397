package cn.dhbin.isme.ims.domain.entity;


import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 化学离子表(ChemicalIon)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-28 12:52:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("chemical_ion")
public class ChemicalIon implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 站点id
     **/
    private Integer distributeId;

    /**
     * 采样层次(1底层 2表层)
     **/
    private Integer sampleLayer;
    
    /**
     * 活性磷酸盐含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal activePhosphate;
    
    /**
     * 亚硝酸盐-氮含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal nitriteNitrogen;
    
    /**
     * 硝酸盐-氮含量,单位mg/L - decimal(5,4)
     **/
    private BigDecimal nitrateNitrogen;
    
    /**
     * 氨-氢,单位mg/L - decimal(5,4)
     **/
    private BigDecimal ammoniaHydrogen;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private String distributeName;
    
public Serializable pkVal() {
        return null;
    }
}



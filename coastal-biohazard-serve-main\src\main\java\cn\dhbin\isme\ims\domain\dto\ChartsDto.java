package cn.dhbin.isme.ims.domain.dto;

import cn.dhbin.isme.ims.domain.entity.ChemicalIon;
import cn.dhbin.isme.ims.domain.entity.FieldDescription;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChartsDto {
    List<WaterPhWeatherData> waterPhWeatherDataList;
    List<FieldDescription> fieldDescriptionList;
    List<ChemicalIon> chemicalIonList;
}

package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 微观繁殖体丰富度数据 Excel 导入导出DTO
 */
@Data
public class AnalysisOfBiologicalFactorsExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点ID")
    @ColumnWidth(15)
    private Integer distributeId;

    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;

    @ExcelProperty("生物门类及丰度")
    @ColumnWidth(100)
    private String speciesAbundance;
}

package cn.dhbin.isme.ims.domain.dto;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StationPointDto {
    private StationPointScale stationPointScale; //空间范围单点信息

    private List<DistributeAndWaterDto> stationPointDistribute; //调查站位信息
}

package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.AbundanceRequest;
import cn.dhbin.isme.ims.service.AbundanceLayerSpeciesDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/abundance")
@RequiredArgsConstructor
public class AbundanceLayerSpeciesDataController {
    private final AbundanceLayerSpeciesDataService abundanceLayerSpeciesDataService;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<AbundanceLayerSpeciesDataDto>> selectAll(AbundanceLayerSpeciesDataRequest request) {
        Page<AbundanceLayerSpeciesDataDto> ret = abundanceLayerSpeciesDataService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        abundanceLayerSpeciesDataService.removeById(id);
        return R.ok();
    }

    /**
     * 新增
     * @param request
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody AbundanceRequest request) {
        abundanceLayerSpeciesDataService.addAbundance(
                request.getDistributeId(),
                request.getSampleType(),
                request.getAbundance(),
                request.getSampleTypes()
        );
        return R.ok();
    }

    /**
     * 修改
     * @param request
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody AbundanceRequest request) {
        abundanceLayerSpeciesDataService.updateAbundance(request.getId(),
                request.getDistributeId(),
                request.getSampleType(),
                request.getAbundance(),
                request.getSampleTypes());
        return R.ok();
    }

    @GetMapping("list")
    public R<List<AbundanceLayerSpeciesDataDto>> selectList(@RequestParam Integer distributeId) {
        List<AbundanceLayerSpeciesDataDto> abundanceLayerSpeciesDataDtos = abundanceLayerSpeciesDataService.queryList(distributeId);
        return R.ok(abundanceLayerSpeciesDataDtos);
    }

    @GetMapping("/sample-types")
    @Operation(summary = "获取所有样本类型")
    public R<List<Integer>> getAllSampleTypes() {
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();
        List<AbundanceLayerSpeciesData> list = abundanceLayerSpeciesDataService.list(queryWrapper);
        
        List<Integer> sampleTypes = list.stream()
            .map(AbundanceLayerSpeciesData::getSampleType)
            .distinct()
            .collect(Collectors.toList());
            
        return R.ok(sampleTypes);
    }
}

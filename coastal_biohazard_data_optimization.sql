-- =====================================================
-- 海岸带生物灾害监测系统 - QDW站点演示数据脚本
-- 目标：专门为QDW站点添加完整、丰富的监测数据用于演示
-- 站点信息：QDW站点 (distribute_id = 12, scale_id = 4)
-- =====================================================

USE coastal_biohazard;

-- =====================================================
-- 0. 清理QDW站点的现有数据，避免重复插入
-- =====================================================
-- 清理关联数据
DELETE FROM analysis_sample WHERE abundance_id IN (
    SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12
);

-- 清理形态分析数据
DELETE FROM morphological_analysis_data WHERE abundance_id IN (
    SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12
);

-- 清理QDW站点的主要数据
DELETE FROM water_ph_weather_data WHERE distribute_id = 12;
DELETE FROM chemical_ion WHERE distribute_id = 12;
DELETE FROM metal_ion WHERE distribute_id = 12;
DELETE FROM analysis_of_biological_factors WHERE distribute_id = 12;
DELETE FROM biodiversity WHERE distribute_id = 12;
DELETE FROM water_quality_prediction WHERE longitude = 119.366892 AND latitude = 34.760023;
DELETE FROM survey_time_range WHERE distribute_id = 12;

-- 清理沉积物数据（如果需要的话）
-- DELETE FROM sediment WHERE id > 3;

-- =====================================================
-- 1. QDW站点水文气象数据增强（多时间点、多层次）
-- =====================================================
INSERT INTO `water_ph_weather_data` 
(`distribute_id`, `sample_layer`, `weather`, `wind_direction`, `salt_extent`, `ph_extent`, `air_temperature`, `water_temperature`, `transparent_extent`, `create_time`) 
VALUES 
-- 2024-12-15 早晨时段数据
(12, 2, '晴', '西北风', 30.20, 8.12, 12.8, 10.50, 2.10, '2024-12-15 08:00:00'),
(12, 1, '晴', '西北风', 30.65, 7.98, 12.8, 9.95, 1.85, '2024-12-15 08:00:00'),

-- 2024-12-15 上午时段数据
(12, 2, '晴', '西北风', 30.00, 8.01, 13.4, 10.30, 2.00, '2024-12-15 09:30:00'),
(12, 1, '晴', '西北风', 30.50, 7.90, 13.4, 9.80, 1.70, '2024-12-15 09:30:00'),

-- 2024-12-15 中午时段数据
(12, 2, '晴转多云', '西风', 29.85, 8.08, 15.2, 11.20, 2.25, '2024-12-15 12:00:00'),
(12, 1, '晴转多云', '西风', 30.35, 7.95, 15.2, 10.45, 1.95, '2024-12-15 12:00:00'),

-- 2024-12-16 监测数据
(12, 2, '多云', '东南风', 30.45, 8.18, 14.8, 11.80, 2.40, '2024-12-16 09:00:00'),
(12, 1, '多云', '东南风', 30.90, 8.05, 14.8, 11.10, 2.10, '2024-12-16 09:00:00'),

-- 2024-12-17 监测数据
(12, 2, '阴', '北风', 29.75, 7.88, 13.2, 10.10, 1.80, '2024-12-17 10:00:00'),
(12, 1, '阴', '北风', 30.25, 7.75, 13.2, 9.60, 1.55, '2024-12-17 10:00:00');

-- =====================================================
-- 2. QDW站点化学离子数据增强（多时间点、全面检测）
-- =====================================================
INSERT INTO `chemical_ion` 
(`distribute_id`, `sample_layer`, `active_phosphate`, `nitrite_nitrogen`, `nitrate_nitrogen`, `ammonia_hydrogen`, `create_time`) 
VALUES 
-- 2024-12-15 早晨数据
(12, 2, 0.0225, 0.0032, 0.1120, 0.0125, '2024-12-15 08:00:00'),
(12, 1, 0.0248, 0.0042, 0.1285, 0.0152, '2024-12-15 08:00:00'),

-- 2024-12-15 上午数据
(12, 2, 0.0217, 0.0030, 0.1046, 0.0110, '2024-12-15 09:30:00'),
(12, 1, 0.0240, 0.0040, 0.1250, 0.0140, '2024-12-15 09:30:00'),

-- 2024-12-15 中午数据
(12, 2, 0.0205, 0.0028, 0.0985, 0.0098, '2024-12-15 12:00:00'),
(12, 1, 0.0232, 0.0038, 0.1180, 0.0128, '2024-12-15 12:00:00'),

-- 2024-12-16 数据
(12, 2, 0.0195, 0.0025, 0.0920, 0.0085, '2024-12-16 09:00:00'),
(12, 1, 0.0220, 0.0035, 0.1150, 0.0115, '2024-12-16 09:00:00'),

-- 2024-12-17 数据
(12, 2, 0.0185, 0.0022, 0.0855, 0.0078, '2024-12-17 10:00:00'),
(12, 1, 0.0210, 0.0032, 0.1085, 0.0108, '2024-12-17 10:00:00');

-- =====================================================
-- 3. QDW站点金属离子数据增强（全谱分析）
-- =====================================================
INSERT INTO `metal_ion` 
(`distribute_id`, `name`, `num`) 
VALUES 
-- 重金属元素检测
(12, 'Cu', 0.185),    -- 铜
(12, 'Zn', 0.045),    -- 锌
(12, 'Pb', 0.008),    -- 铅
(12, 'Cd', 0.0021),   -- 镉
(12, 'Cr', 0.016),    -- 铬
(12, 'Ni', 0.013),    -- 镍
(12, 'As', 0.0048),   -- 砷
(12, 'Hg', 0.0009),   -- 汞
(12, 'Fe', 2.850),    -- 铁
(12, 'Mn', 0.320),    -- 锰
(12, 'Al', 1.250),    -- 铝
(12, 'Co', 0.0035),   -- 钴
(12, 'V', 0.0125),    -- 钒
(12, 'Mo', 0.0015),   -- 钼
(12, 'Se', 0.0008);   -- 硒

-- =====================================================
-- 4. QDW站点微观繁殖体分析数据增强（多样本类型）
-- =====================================================
INSERT INTO `analysis_of_biological_factors` 
(`distribute_id`, `sample_type`, `abundance`, `create_time`, `report`, `report_time`) 
VALUES 
-- 表层水样微观繁殖体 - 12月15日早晨
(12, 2, 820, '2024-12-15 08:00:00', '表层水样中微观繁殖体丰度较高，以浒苔孢子为主，检测到多种藻类休眠体', '2024-12-15 10:00:00'),

-- 底层水样微观繁殖体 - 12月15日早晨
(12, 1, 580, '2024-12-15 08:00:00', '底层水样中微观繁殖体分布相对稳定，种类多样性良好', '2024-12-15 10:00:00'),

-- 沉积物微观繁殖体 - 12月15日早晨
(12, 0, 420, '2024-12-15 08:00:00', '沉积物中发现大量休眠态繁殖体，为春季爆发提供种源', '2024-12-15 10:00:00'),

-- 藻样微观繁殖体 - 12月15日
(12, 3, 1250, '2024-12-15 09:30:00', '藻体样本中繁殖体密度极高，显示活跃的繁殖状态', '2024-12-15 11:30:00'),

-- 表层水样微观繁殖体 - 12月16日
(12, 2, 950, '2024-12-16 09:00:00', '表层水样繁殖体浓度上升，预示可能的藻华事件', '2024-12-16 11:00:00'),

-- 底层水样微观繁殖体 - 12月16日
(12, 1, 680, '2024-12-16 09:00:00', '底层水样中繁殖体数量增加，垂直分布呈现梯度变化', '2024-12-16 11:00:00');

-- =====================================================
-- 5. QDW站点生物多样性分析数据增强（完整生态群落）
-- =====================================================
INSERT INTO `biodiversity` 
(`distribute_id`, `type`, `name`, `h_index_min`, `h_index_max`, `h_avg`, `j_index_min`, `j_index_max`, `j_avg`, `d_index_min`, `d_index_max`, `d_avg`, `description`, `abundance`, `biodiversity`) 
VALUES 
-- 浮游植物群落
(12, 0, '具槽帕拉藻(Paralia sulcata)、离心海链藻(Thalassiosira excentrica)、斯氏几内亚藻(Guinardia striata)、菱形海线藻(Thalassionema nitzschioides)', 
 1.28, 3.45, 2.38, 0.68, 0.92, 0.81, 0.22, 0.88, 0.55, 
 'QDW站点浮游植物群落结构复杂，以大型链状硅藻为优势种群，春季繁殖活跃，生物多样性指数高，群落稳定性良好。检测到多种指示性藻种，显示海域营养状态适中。', 
 '125.8×10^4', 2.150),

-- 浮游动物群落
(12, 1, '桡足类(哲水蚤属、纺锤水蚤属)、毛颚类(箭虫属)、枝角类、糠虾类、水母类(小水母属)', 
 1.15, 3.22, 2.28, 0.62, 0.89, 0.76, 0.18, 0.82, 0.51, 
 'QDW站点浮游动物群落以小型桡足类为主导，毛颚类和糠虾类为重要组成部分。群落垂直分布明显，昼夜迁移活跃。生物量季节变化显著，与浮游植物群落耦合度高。', 
 '0.485', NULL),

-- 底栖生物群落
(12, 2, '环节动物多毛类(沙蚕科、海稚虫科)、节肢动物甲壳类(端足类、等足类)、软体动物(双壳类、腹足类)、棘皮动物(海胆类、海参类)', 
 1.32, 3.18, 2.42, 0.71, 0.91, 0.82, 0.25, 0.85, 0.58, 
 'QDW站点底栖生物群落以环节动物为绝对优势，甲壳类和软体动物次之。群落结构稳定，功能群完整，具有典型的温带海域底栖群落特征。生物扰动活跃，对沉积物再悬浮有重要影响。', 
 '2150.35', NULL),

-- 游泳动物群落
(12, 4, '硬骨鱼类(鲱科、鲽科、虾虎鱼科)、软骨鱼类、头足类(枪乌贼属)、大型甲壳类(对虾科、蟹科)', 
 0.95, 2.85, 1.92, 0.48, 0.78, 0.63, 0.15, 0.72, 0.44, 
 'QDW站点游泳动物群落以小型经济鱼类为主，季节性洄游种类丰富。群落结构受水团性质影响明显，渔业资源状况良好，生态系统功能完整。', 
 '85.6', NULL);

-- =====================================================
-- 6. 为QDW站点生成微观繁殖体种类关联数据
-- =====================================================
-- 获取QDW站点的微观繁殖体记录ID (使用LIMIT 1避免多行问题)
SET @qdw_surface_1 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 2 AND create_time = '2024-12-15 08:00:00' LIMIT 1);
SET @qdw_bottom_1 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 1 AND create_time = '2024-12-15 08:00:00' LIMIT 1);
SET @qdw_sediment_1 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 0 AND create_time = '2024-12-15 08:00:00' LIMIT 1);
SET @qdw_algae_1 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 3 AND create_time = '2024-12-15 09:30:00' LIMIT 1);
SET @qdw_surface_2 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 2 AND create_time = '2024-12-16 09:00:00' LIMIT 1);
SET @qdw_bottom_2 = (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12 AND sample_type = 1 AND create_time = '2024-12-16 09:00:00' LIMIT 1);

-- QDW站点微观繁殖体种类分布数据
INSERT INTO `analysis_sample` (`abundance_id`, `sample_id`, `number`) VALUES 
-- 12月15日早晨表层水样
(@qdw_surface_1, 1, 8.20),   -- 浒苔
(@qdw_surface_1, 2, 2.50),   -- 微观繁殖体
(@qdw_surface_1, 3, 1.35),   -- 微生物
(@qdw_surface_1, 4, 0.85),   -- 病毒

-- 12月15日早晨底层水样
(@qdw_bottom_1, 1, 5.80),    -- 浒苔
(@qdw_bottom_1, 2, 1.85),    -- 微观繁殖体
(@qdw_bottom_1, 3, 0.95),    -- 微生物
(@qdw_bottom_1, 4, 0.55),    -- 病毒

-- 12月15日早晨沉积物
(@qdw_sediment_1, 1, 4.20),  -- 浒苔
(@qdw_sediment_1, 2, 1.25),  -- 微观繁殖体
(@qdw_sediment_1, 3, 0.68),  -- 微生物
(@qdw_sediment_1, 4, 0.32),  -- 病毒

-- 12月15日藻样
(@qdw_algae_1, 1, 12.50),    -- 浒苔
(@qdw_algae_1, 2, 4.80),     -- 微观繁殖体
(@qdw_algae_1, 3, 2.95),     -- 微生物
(@qdw_algae_1, 4, 1.75),     -- 病毒

-- 12月16日表层水样
(@qdw_surface_2, 1, 9.50),   -- 浒苔
(@qdw_surface_2, 2, 3.20),   -- 微观繁殖体
(@qdw_surface_2, 3, 1.85),   -- 微生物
(@qdw_surface_2, 4, 1.25),   -- 病毒

-- 12月16日底层水样
(@qdw_bottom_2, 1, 6.80),    -- 浒苔
(@qdw_bottom_2, 2, 2.35),    -- 微观繁殖体
(@qdw_bottom_2, 3, 1.15),    -- 微生物
(@qdw_bottom_2, 4, 0.78);    -- 病毒

-- =====================================================
-- 7. QDW站点形态分析数据（详细显微图像）
-- =====================================================
INSERT INTO `morphological_analysis_data` 
(`abundance_id`, `branch_url`, `cross_cut_url`, `surface_cell_url`, `create_time`) 
VALUES 
-- 12月15日藻样的详细形态分析
(@qdw_algae_1, 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_branch_20241215.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_cross_20241215.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_algae_surface_20241215.png', 
 '2024-12-15 11:30:00'),

-- 12月15日表层水样的形态分析
(@qdw_surface_1, 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_branch_20241215.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_cross_20241215.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_cell_20241215.png', 
 '2024-12-15 10:00:00'),

-- 12月16日表层水样的形态分析
(@qdw_surface_2, 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_branch_20241216.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_cross_20241216.png', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_surface_cell_20241216.png', 
 '2024-12-16 11:00:00');

-- =====================================================
-- 8. QDW站点沉积物分析数据
-- =====================================================
INSERT INTO `sediment` 
(`sediment_url`, `culture_url`) 
VALUES 
('https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_sample_20241215.jpg', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_culture_20241215.jpg'),
 
('https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_sample_20241216.jpg', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_culture_20241216.jpg'),
 
('https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_sample_20241217.jpg', 
 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/qdw_sediment_culture_20241217.jpg');

-- =====================================================
-- 9. QDW站点水质预测数据（多时间点预测）
-- =====================================================
INSERT INTO `water_quality_prediction` 
(`longitude`, `latitude`, `scale_id`, `task_id`, `times_id`, `salt_extent`, `ph_extent`, `water_temperature`, `transparent_extent`, `confidence_score`, `model_version`, `prediction_time`, `ai_analysis`) 
VALUES 
-- 当前时刻水质评估
(119.366892, 34.760023, 4, 7, 8, 30.18, 8.15, 10.65, 2.15, 0.9250, 'v2.3.0', '2024-12-15 09:30:00', 
 'AI分析：QDW站点当前水质状况良好。盐度30.18‰处于正常范围，pH值8.15显示弱碱性环境，有利于浮游植物光合作用。水温10.65℃适宜冬季生物活动，透明度2.15m表明水体较清澈。营养盐浓度适中，预测未来6小时内水质将保持稳定。建议持续监测磷酸盐浓度变化。'),

-- 2小时后预测
(119.366892, 34.760023, 4, 7, 8, 30.05, 8.22, 11.25, 2.28, 0.9150, 'v2.3.0', '2024-12-15 11:30:00', 
 'AI分析：预测2小时后，QDW站点水质将轻微改善。盐度略降至30.05‰，pH值上升至8.22，水温升至11.25℃，透明度增加至2.28m。环境条件更有利于浮游植物生长，但需警惕可能的藻华风险。建议加强生物监测频次。'),

-- 6小时后预测
(119.366892, 34.760023, 4, 7, 8, 29.92, 8.28, 12.10, 2.45, 0.8950, 'v2.3.0', '2024-12-15 15:30:00', 
 'AI分析：预测6小时后，QDW站点水质继续向好发展。盐度降至29.92‰，pH值达到8.28，水温升至12.10℃，透明度提升至2.45m。水体营养状态良好，光照条件充足，预测浮游植物活动将更加活跃。建议关注叶绿素a浓度变化趋势。'),

-- 24小时后预测
(119.366892, 34.760023, 4, 7, 8, 30.35, 8.18, 10.88, 2.05, 0.8750, 'v2.3.0', '2024-12-16 09:30:00', 
 'AI分析：预测24小时后，QDW站点水质参数将有所波动。受潮汐和气象条件影响，盐度回升至30.35‰，pH值回落至8.18，水温降至10.88℃，透明度减少至2.05m。整体水质仍处于良好状态，但需关注潮汐周期对水质的周期性影响。'),

-- 48小时后预测
(119.366892, 34.760023, 4, 7, 8, 30.15, 8.08, 11.45, 2.20, 0.8650, 'v2.3.0', '2024-12-17 09:30:00', 
 'AI分析：预测48小时后，QDW站点水质将趋于稳定。各项指标接近历史平均值，生态系统处于相对平衡状态。建议此时进行综合生态调查，获取基线数据。同时关注气象预报，准备应对可能的天气变化对水质的影响。');

-- =====================================================
-- 10. QDW站点调查时间范围数据增强
-- =====================================================
INSERT INTO `survey_time_range` (`distribute_id`, `before_investigate`, `after_investigate`, `description`) VALUES 
(12, '2024-12-15 08:00:00', '2024-12-15 13:00:00', 'QDW站点冬季基线调查 - 完整5小时连续监测'),
(12, '2024-12-16 08:30:00', '2024-12-16 12:30:00', 'QDW站点跟踪调查 - 4小时密集采样'),
(12, '2024-12-17 09:00:00', '2024-12-17 15:00:00', 'QDW站点综合生态调查 - 6小时全面监测'),
(12, '2024-12-18 07:00:00', '2024-12-18 11:00:00', 'QDW站点早潮调查 - 4小时潮汐监测');

-- =====================================================
-- 11. 数据完整性验证和统计
-- =====================================================
SELECT 
    'QDW站点数据增强完成' as status,
    NOW() as completion_time,
    CONCAT(
        '已为QDW站点添加：',
        (SELECT COUNT(*) FROM water_ph_weather_data WHERE distribute_id = 12), '条水文气象记录，',
        (SELECT COUNT(*) FROM chemical_ion WHERE distribute_id = 12), '条化学离子记录，',
        (SELECT COUNT(*) FROM metal_ion WHERE distribute_id = 12), '条金属离子记录，',
        (SELECT COUNT(*) FROM analysis_of_biological_factors WHERE distribute_id = 12), '条微观繁殖体记录，',
        (SELECT COUNT(*) FROM biodiversity WHERE distribute_id = 12), '条生物多样性记录，',
        (SELECT COUNT(*) FROM water_quality_prediction WHERE longitude = 119.366892 AND latitude = 34.760023), '条水质预测记录，',
        (SELECT COUNT(*) FROM survey_time_range WHERE distribute_id = 12), '条调查时间记录'
    ) as detailed_summary;

-- =====================================================
-- 12. QDW站点数据完整性检查
-- =====================================================
SELECT 
    'QDW站点数据完整性检查' as check_type,
    '完整' as data_status,
    CONCAT(
        '水文气象数据：', (SELECT COUNT(*) FROM water_ph_weather_data WHERE distribute_id = 12), '条 | ',
        '化学离子数据：', (SELECT COUNT(*) FROM chemical_ion WHERE distribute_id = 12), '条 | ',
        '金属离子数据：', (SELECT COUNT(*) FROM metal_ion WHERE distribute_id = 12), '条 | ',
        '微观繁殖体数据：', (SELECT COUNT(*) FROM analysis_of_biological_factors WHERE distribute_id = 12), '条 | ',
        '生物多样性数据：', (SELECT COUNT(*) FROM biodiversity WHERE distribute_id = 12), '条 | ',
        '形态分析数据：', (SELECT COUNT(*) FROM morphological_analysis_data WHERE abundance_id IN (SELECT id FROM analysis_of_biological_factors WHERE distribute_id = 12)), '条 | ',
        '水质预测数据：', (SELECT COUNT(*) FROM water_quality_prediction WHERE longitude = 119.366892 AND latitude = 34.760023), '条 | ',
        '沉积物数据：', (SELECT COUNT(*) FROM sediment), '条 | ',
        '调查时间数据：', (SELECT COUNT(*) FROM survey_time_range WHERE distribute_id = 12), '条'
    ) as data_summary; 
package cn.dhbin.isme.ims.domain.dto;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class BiodiversityDto {
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点信息

    /**
     * (0浮游植物 1浮游动物 2底栖生物 4游泳动物) 默认0
     **/
    private Integer type;

    /**
     * 优势种名称
     **/
    private String name;

    /**
     * 生物多样性指数最低值 - decimal(10,2)
     **/
    private BigDecimal hIndexMin;

    /**
     * 生物多样性指数最高值 - decimal(10,2)
     **/
    private BigDecimal hIndexMax;

    /**
     * 生物多样性指数平均值 - decimal(10,2)
     **/
    private BigDecimal hAvg;

    /**
     * 均匀度指数最低值 - decimal(10,2)
     **/
    private BigDecimal jIndexMin;

    /**
     * 均匀度指数最高值 - decimal(10,2)
     **/
    private BigDecimal jIndexMax;

    /**
     * 均匀度指数平均值 - decimal(10,2)
     **/
    private BigDecimal jAvg;

    /**
     * 丰富度指数最低值 - decimal(10,2)
     **/
    private BigDecimal dIndexMin;

    /**
     * 丰富度指数最高值 - decimal(10,2)
     **/
    private BigDecimal dIndexMax;

    /**
     * 丰富度指数平均值 - decimal(10,2)
     **/
    private BigDecimal dAvg;

    /**
     * 分析概述
     **/
    private String description;

    /**
     * 丰度
     **/
    private String abundance;

    /**
     * 生物多样性 - decimal(5,3)
     **/
    private BigDecimal biodiversity;

}

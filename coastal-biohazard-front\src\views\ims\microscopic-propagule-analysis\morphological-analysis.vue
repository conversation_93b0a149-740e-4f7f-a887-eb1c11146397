<!--------------------------------
-微观繁殖体分析
-形态分析
-createBy：isla
--------------------------------->
<!--------------------------------
 - @Author: <PERSON>
 - @LastEditor: <PERSON>
 - @LastEditTime: 2023/12/05 21:29:43
 - @Email: <EMAIL>
 - Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 --------------------------------->

<template>
  <CommonPage back>
    <template #title-suffix>
      <NTag class="ml-12" type="warning">
        站点 {{ route.query.distributeName }}
      </NTag>
    </template>
    <template #action>
      <NButton type="primary" @click="add()">
        <i class="i-material-symbols:add mr-4 text-18" />
        创建新记录
      </NButton>
    </template>

    <MeCrud
      ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="api.readMorphological"
    />

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" :rules="rules" label-placement="left" label-align="left" :label-width="120" :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <!-- <n-config-provider :locale="zhCN"> -->
        <n-form-item label="分支图片" path="branchUrl">
          <n-upload
            :default-file-list="previewFileList1" :max="1" :custom-request="handleUpload1" list-type="image-card"
            @preview="handlePreview" @before-upload="validateFileBeforeUpload" @remove="handleRemove"
          >
            <template #trigger>
              <NButton type="primary">
                点击上传
              </NButton>
            </template>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>
        <!-- </n-config-provider> -->

        <n-form-item label="横切图片" path="crossCutUrl">
          <n-upload
            :default-file-list="previewFileList2" :max="1" :custom-request="handleUpload2" list-type="image-card"
            @preview="handlePreview" @before-upload="validateFileBeforeUpload" @remove="handleRemove"
          >
            <template #trigger>
              <NButton type="primary">
                点击上传
              </NButton>
            </template>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>

        <n-form-item label="表层细胞图片" path="surfaceCellUrl">
          <n-upload
            :default-file-list="previewFileList3" :max="1" :custom-request="handleUpload3" list-type="image-card"
            @preview="handlePreview" @before-upload="validateFileBeforeUpload" @remove="handleRemove"
          >
            <template #trigger>
              <NButton type="primary">
                点击上传
              </NButton>
            </template>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NImage, NTag } from 'naive-ui'

import { h } from 'vue'
import api from './api'

defineOptions({ name: 'RoleUser' })
const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)
const route = useRoute()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

// 预览图片URL
const previewImageUrl = ref()

const previewFileList1 = ref([])
const previewFileList2 = ref([])
const previewFileList3 = ref([])

// 智能图片处理函数
const getImageUrl = (imageName) => {
  if (!imageName) return getPlaceholderImage()
  
  // 如果是完整的URL（包含http或https），直接返回
  if (imageName.startsWith('http://') || imageName.startsWith('https://')) {
    return imageName
  }
  
  // 如果是本地文件名，通过API获取
  return `${baseUrl.value}/upload/getImage?imageName=${imageName}`
}

const getPlaceholderImage = () => {
  // 使用base64编码的占位符图片（一个简单的灰色方块）
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NS4wMDAxIDc1VjEyNUgxMjVWNzVINzUuMDAwMVoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTkwIDEwMEMxMDQuMTQzIDEwMCAxMTUuNzE0IDg4LjQyODYgMTE1LjcxNCA3NC4yODU3QzExNS43MTQgNjAuMTQyOSAxMDQuMTQzIDQ4LjU3MTQgOTAgNDguNTcxNEM3NS44NTcxIDQ4LjU3MTQgNjQuMjg1NyA2MC4xNDI5IDY0LjI4NTcgNzQuMjg1N0M2NC4yODU3IDg4LjQyODYgNzUuODU3MSAxMDAgOTAgMTAwWiIgZmlsbD0iI0Q5RDlEOSIvPgo8cGF0aCBkPSJNMTA3LjE0MyAxMjguNTcxTDEyNSAxMTQuMjg2VjEyNUg3NVYxMTQuMjg2TDg1LjcxNDMgMTAzLjU3MVMxMDcuMTQzIDEyOC41NzEgMTA3LjE0MyAxMjguNTcxWiIgZmlsbD0iI0Q5RDlEOSIvPgo8dGV4dCB4PSIxMDAiIHk9IjE2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPg=='
}

const rules = reactive({
  branchUrl: { required: true, message: '请上传分支图片' },
  crossCutUrl: { required: true, message: '请上传横切图片' },
  surfaceCellUrl: { required: true, message: '请上传表层细胞图片' },
})

// let branchUrl = ref()
// let crossCutUrl = ref()
// let surfaceCellUrl = ref()

// 模态框显示状态
const showModal = ref(false)

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '生物量',
  initForm: { enable: true, branchUrl: '', crossCutUrl: '', surfaceCellUrl: '' },
  doCreate: api.createMorphological,
  doDelete: api.deleteMorphological,
  doUpdate: api.updateMorphological,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

onMounted(() => {
  queryItems.value.abundanceId = route.query.abundanceId
  $table.value?.handleSearch()
})
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '分支图片',
    key: 'branchUrl',
    render(row) {
      return h(NImage, { 
        class: 'w-[100px]', 
        src: getImageUrl(row.branchUrl),
        onError: (event) => {
          event.target.src = getPlaceholderImage()
        }
      })
    },
  },
  {
    title: '横切图片',
    key: 'crossCutUrl',
    render(row) {
      return h(NImage, { 
        class: 'w-[100px]', 
        src: getImageUrl(row.crossCutUrl),
        onError: (event) => {
          event.target.src = getPlaceholderImage()
        }
      })
    },
  },
  {
    title: '表层细胞图片',
    key: 'surfaceCellUrl',
    render(row) {
      return h(NImage, { 
        class: 'w-[100px]', 
        src: getImageUrl(row.surfaceCellUrl),
        onError: (event) => {
          event.target.src = getPlaceholderImage()
        }
      })
    },
  },
  // {
  //   title: '创建时间',
  //   key: 'createDate',
  //   width: 180,
  //   render(row) {
  //     return h('span', formatDateTime(row.createTime))
  //   },
  // },
  {
    width: 180,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleOpenUpdate(row),
          },
          {
            default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function handleOpenUpdate(row) {
  previewFileList1.value = [{ url: row.branchUrl, status: 'finished', id: 'branchUrl' }]
  previewFileList2.value = [{ url: row.crossCutUrl, status: 'finished', id: 'crossCutUrl' }]
  previewFileList3.value = [{ url: row.surfaceCellUrl, status: 'finished', id: 'surfaceCellUrl' }]
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row,
    onOk: updateM,
  })
}

async function updateM() {
  console.log(modalForm.value)
  await modalFormRef.value?.validate()
  await api.updateMorphological({
    id: modalForm.value.id,
    // abundanceId: route.query.abundanceId,
    branchUrl: modalForm.value.branchUrl,
    crossCutUrl: modalForm.value.crossCutUrl,
    surfaceCellUrl: modalForm.value.surfaceCellUrl,
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}

function add() {
  previewFileList1.value = []
  previewFileList2.value = []
  previewFileList3.value = []
  modalForm.value.branchUrl = null
  modalForm.value.crossCutUrl = null
  modalForm.value.surfaceCellUrl = null
  handleOpen({
    action: 'add',
    title: '新增记录',
    row: modalForm.value,
    onOk: async () => {
      await modalFormRef.value?.validate()
      await api.createMorphological({
        abundanceId: route.query.abundanceId,
        branchUrl: modalForm.value.branchUrl,
        crossCutUrl: modalForm.value.crossCutUrl,
        surfaceCellUrl: modalForm.value.surfaceCellUrl,
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

// const removeImg = (options) => {
//   const { file, fileList, index } = options;

//   // 显示确认对话框
//   const confirmRemove = async () => {
//     const confirmed = await confirm('确定要移除这张图片吗？');

//     if (confirmed) {
//       // 更新预览文件列表
//       previewFileList1.value = [...fileList.filter((f, i) => i !== index)];
//       // 清空预览图片 URL
//       previewImageUrl.value = '';
//       // 关闭模态框
//       showModal.value = false;
//       message.info('图片已移除');
//       return true;
//     }

//     return false;
//   };

//   return confirmRemove();
// };
function handleRemove(data) {
  console.log(data, '触发删除')
  if (data.file.id === 'branchUrl') {
    previewFileList1.value = []
    // branchUrl.value = null
    modalForm.value.branchUrl = null
  }
  else if (data.file.id === 'crossCutUrl') {
    previewFileList2.value = []
    // crossCutUrl.value = null
    modalForm.value.crossCutUrl = null
  }
  else if (data.file.id === 'surfaceCellUrl') {
    previewFileList3.value = []
    // surfaceCellUrl.value = null
    modalForm.value.surfaceCellUrl = null
  }
}

function validateFileBeforeUpload({ file }) {
  console.log(file)

  const allowedTypes = ['image/jpeg', 'image/png']
  const maxSize = 5 * 1024 * 1024 // 5MB

  if (!allowedTypes.includes(file.type)) {
    alert('只允许上传 JPG 和 PNG 格式的图片！')
    return false
  }

  if (file.size > maxSize) {
    alert('图片大小不能超过 5MB！')
    return false
  }

  return true
}

async function handleUpload1({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }

  console.log(file)
  $message.loading('上传中...')
  const { data } = await api.uploadImg(file)
  file.url = data
  // branchUrl.value = data
  modalForm.value.branchUrl = data
  $message.success('上传成功')
  onFinish()
}

async function handleUpload2({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }

  console.log(file)
  $message.loading('上传中...')
  const { data } = await api.uploadImg(file)
  file.url = data
  // crossCutUrl.value = data
  modalForm.value.crossCutUrl = data
  $message.success('上传成功')
  onFinish()
}

async function handleUpload3({ file, onFinish }) {
  if (!file || !file.type) {
    $message.error('请选择文件')
  }

  console.log(file)
  $message.loading('上传中...')
  const { data } = await api.uploadImg(file)
  file.url = data
  modalForm.value.surfaceCellUrl = data
  // surfaceCellUrl.value = data
  $message.success('上传成功')
  onFinish()
}

function handlePreview(file) {
  console.log(file)

  const { url } = file
  previewImageUrl.value = url
  showModal.value = true
}
</script>

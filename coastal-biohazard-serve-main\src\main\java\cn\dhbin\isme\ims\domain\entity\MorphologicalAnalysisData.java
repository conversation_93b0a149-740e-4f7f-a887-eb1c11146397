package cn.dhbin.isme.ims.domain.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 形态分析表(MorphologicalAnalysisData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-29 12:50:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("morphological_analysis_data")
public class MorphologicalAnalysisData implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 微观繁殖体id
     **/
    private Integer abundanceId;
    
    /**
     * 分支图片
     **/
    private String branchUrl;
    
    /**
     * 横切图片
     **/
    private String crossCutUrl;
    
    /**
     * 表层细胞图片
     **/
    private String surfaceCellUrl;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
public Serializable pkVal() {
          return null;
      }
}



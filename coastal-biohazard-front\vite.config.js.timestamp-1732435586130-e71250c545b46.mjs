// vite.config.js
import path2 from "node:path";
import Vue from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/@vitejs+plugin-vue@5.1.3_vite@5.4.3_vue@3.5.3/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import VueJsx from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.1_vite@5.4.3_vue@3.5.3/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import Unocss from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/unocss@0.62.3_postcss@8.4.49_vite@5.4.3/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/unplugin-auto-import@0.18.2_@vueuse+core@11.0.3/node_modules/unplugin-auto-import/dist/vite.js";
import { NaiveUiResolver } from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/unplugin-vue-components@0.27.4_vue@3.5.3/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/unplugin-vue-components@0.27.4_vue@3.5.3/node_modules/unplugin-vue-components/dist/vite.js";
import { defineConfig, loadEnv } from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/vite@5.4.3_sass@1.78.0/node_modules/vite/dist/node/index.js";
import removeNoMatch from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/vite-plugin-router-warn@1.0.0/node_modules/vite-plugin-router-warn/dist/index.mjs";
import VueDevTools from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/vite-plugin-vue-devtools@7.4.4_vite@5.4.3_vue@3.5.3/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// build/index.js
import path from "node:path";
import { globSync } from "file:///D:/%E9%A1%B9%E7%9B%AE%E7%BB%8F%E9%AA%8C/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/1.%E9%BB%84%E6%B5%B7%E6%B5%92%E8%8B%94/Yellow-Sea-Font/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/index.js";

// src/assets/icons/dynamic-icons.js
var dynamic_icons_default = ["i-simple-icons:juejin"];

// build/index.js
function getIcons() {
  const feFiles = globSync("src/assets/icons/feather/*.svg", { nodir: true, strict: true });
  const meFiles = globSync("src/assets/icons/isme/*.svg", { nodir: true, strict: true });
  const feIcons = feFiles.map((filePath) => {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.parse(fileName).name;
    return `i-fe:${fileNameWithoutExt}`;
  });
  const meIcons = meFiles.map((filePath) => {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.parse(fileName).name;
    return `i-me:${fileNameWithoutExt}`;
  });
  return [...dynamic_icons_default, ...feIcons, ...meIcons];
}
function getPagePathes() {
  const files = globSync("src/views/**/*.vue");
  return files.map((item) => `/${path.normalize(item).replace(/\\/g, "/")}`);
}

// build/plugin-isme/icons.js
var PLUGIN_ICONS_ID = "isme:icons";
function pluginIcons() {
  return {
    name: "isme:icons",
    resolveId(id) {
      if (id === PLUGIN_ICONS_ID)
        return `\0${PLUGIN_ICONS_ID}`;
    },
    load(id) {
      if (id === `\0${PLUGIN_ICONS_ID}`) {
        return `export default ${JSON.stringify(getIcons())}`;
      }
    }
  };
}

// build/plugin-isme/page-pathes.js
var PLUGIN_PAGE_PATHES_ID = "isme:page-pathes";
function pluginPagePathes() {
  return {
    name: "isme:page-pathes",
    resolveId(id) {
      if (id === PLUGIN_PAGE_PATHES_ID)
        return `\0${PLUGIN_PAGE_PATHES_ID}`;
    },
    load(id) {
      if (id === `\0${PLUGIN_PAGE_PATHES_ID}`) {
        return `export default ${JSON.stringify(getPagePathes())}`;
      }
    }
  };
}

// vite.config.js
var vite_config_default = defineConfig(({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd());
  const { VITE_PUBLIC_PATH, VITE_PROXY_TARGET } = viteEnv;
  return {
    base: VITE_PUBLIC_PATH || "/",
    plugins: [
      Vue(),
      VueJsx(),
      VueDevTools(),
      Unocss(),
      AutoImport({
        imports: ["vue", "vue-router"],
        dts: false
      }),
      Components({
        resolvers: [NaiveUiResolver()],
        dts: false
      }),
      // 自定义插件，用于生成页面文件的path，并添加到虚拟模块
      pluginPagePathes(),
      // 自定义插件，用于生成自定义icon，并添加到虚拟模块
      pluginIcons(),
      // 移除非必要的vue-router动态路由警告: No match found for location with path
      removeNoMatch()
    ],
    resolve: {
      alias: {
        "@": path2.resolve(process.cwd(), "src"),
        "~": path2.resolve(process.cwd())
      }
    },
    server: {
      host: "0.0.0.0",
      port: 3200,
      open: false,
      proxy: {
        "/api": {
          target: VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: (path3) => path3.replace(/^\/api/, ""),
          secure: false,
          configure: (proxy, options) => {
            proxy.on("proxyRes", (proxyRes, req) => {
              proxyRes.headers["x-real-url"] = new URL(req.url || "", options.target)?.href || "";
            });
          }
        }
      }
    },
    build: {
      chunkSizeWarningLimit: 1024
      // chunk 大小警告的限制（单位kb）
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.excel.BiodiversityExcelDto;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.domain.request.BiodiversityRequest;
import cn.dhbin.isme.ims.service.BiodiversityService;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/biodiversity")
@RequiredArgsConstructor
public class BiodiversityController {

    private final BiodiversityService biodiversityService;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<BiodiversityDto>> selectAll(BiodiversityRequest request) {
        Page<BiodiversityDto> ret = biodiversityService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody Biodiversity data) {
//        String description = generateDescription(data);
//        data.setDescription(description);
        biodiversityService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody Biodiversity data) {
//        String description = generateDescription(data);
//        data.setDescription(description);
        biodiversityService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        biodiversityService.removeById(id);
        return R.ok();
    }

    @GetMapping("list")
    public R<List<BiodiversityDto>> selectList(@RequestParam Integer distributeId) {
        List<BiodiversityDto> biodiversityDtos = biodiversityService.queryList(distributeId);
        return R.ok(biodiversityDtos);
    }

    /**
     * 根据站点ID获取生物多样性数据
     * @param stationId 站点ID
     * @return 生物多样性数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<BiodiversityDto>> getByStationId(@PathVariable Integer stationId) {
        List<BiodiversityDto> list = biodiversityService.queryList(stationId);
        return R.ok(list);
    }

    public static String generateDescription(Biodiversity biodiversity) {
        StringBuilder description = new StringBuilder();
        String name="浮游植物";
        if (biodiversity.getType()==1){
            name="浮游动物";
        } else if (biodiversity.getType()==2) {
            name="底栖生物";
        }

        // 生物多样性的描述
        if (biodiversity.getHAvg().compareTo(BigDecimal.valueOf(1.0)) < 0) {
            description.append("该海域"+name+"群落显示出了较低的多样性水平，");
        } else if (biodiversity.getHAvg().compareTo(BigDecimal.valueOf(1.0)) >= 0 && biodiversity.getHAvg().compareTo(BigDecimal.valueOf(2.0)) <= 0) {
            description.append("该海域"+name+"群落显示出了中等程度的多样性水平，");
        } else {
            description.append("该海域"+name+"群落显示出了较高的多样性水平，");
        }

        // 均匀度的描述
        if (biodiversity.getJAvg().compareTo(BigDecimal.valueOf(0.5)) < 0) {
            description.append("种类分布不均，");
        } else if (biodiversity.getJAvg().compareTo(BigDecimal.valueOf(0.5)) >= 0 && biodiversity.getJAvg().compareTo(BigDecimal.valueOf(0.8)) <= 0) {
            description.append("种类分布相对均匀，");
        } else {
            description.append("种类分布非常均匀，");
        }

        // 丰富度的描述
        if (biodiversity.getDAvg().compareTo(BigDecimal.valueOf(0.3)) < 0) {
            description.append("整体丰富度较低。");
        } else if (biodiversity.getDAvg().compareTo(BigDecimal.valueOf(0.3)) >= 0 && biodiversity.getDAvg().compareTo(BigDecimal.valueOf(0.6)) <= 0) {
            description.append("整体丰富度处于中等水平。");
        } else {
            description.append("整体丰富度较高。");
        }

        return description.toString();
    }
    /**
     * 通用导入Excel方法
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file, @RequestParam(required = false) Integer type) {
        try {
            // 读取Excel文件
            List<BiodiversityExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(BiodiversityExcelDto.class)
                    .sheet()
                    .doReadSync();

            biodiversityService.importData(list, type);
            return R.ok("导入成功");
        } catch (BizException e) {
            return R.build(e);
        } catch (Exception e) {
            e.printStackTrace();
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }

    @GetMapping("/export")
    public void exportExcel(BiodiversityRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<BiodiversityExcelDto> list=biodiversityService.queryListForExcel(request);
            // 设置响应头
            String typeName="浮游植物";
            if (request.getType()==1){
                typeName="浮游动物";
            } else if (request.getType()==2) {
                typeName="底栖生物";
            } else if (request.getType()==4) {
                typeName="游泳动物";
            }
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = typeName+"多样性数据";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, BiodiversityExcelDto.class)
                    .autoCloseStream(true)
                    .sheet(typeName+"多样性数据")
                    .doWrite(list);
        }catch (Exception e){
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    // 浮游植物
    @GetMapping("/phytolankton/export")
    public void phytolanktonExport(BiodiversityRequest request, HttpServletResponse response) throws IOException {
        request.setType(0);
        exportExcel(request, response);
    }

    // 浮游植物导入
    @PostMapping("/phytolankton/import")
    public R<String> phytolanktonImport(@RequestParam("file") MultipartFile file) {
        return importExcel(file, 0);
    }

    // 浮游动物
    @GetMapping("/zooplankter/export")
    public void zooplankterExport(BiodiversityRequest request, HttpServletResponse response) throws IOException {
        request.setType(1);
        exportExcel(request, response);
    }

    // 浮游动物导入
    @PostMapping("/zooplankter/import")
    public R<String> zooplankterImport(@RequestParam("file") MultipartFile file) {
        return importExcel(file, 1);
    }

    // 底栖生物
    @GetMapping("/benthos/export")
    public void benthosExport(BiodiversityRequest request, HttpServletResponse response) throws IOException {
        request.setType(2);
        exportExcel(request, response);
    }

    // 底栖生物导入
    @PostMapping("/benthos/import")
    public R<String> benthosImport(@RequestParam("file") MultipartFile file) {
        return importExcel(file, 2);
    }

    // 游泳动物
    @GetMapping("/necton/export")
    public void nectonExport(BiodiversityRequest request, HttpServletResponse response) throws IOException {
        request.setType(4);
        exportExcel(request, response);
    }

    // 游泳动物导入
    @PostMapping("/necton/import")
    public R<String> nectonImport(@RequestParam("file") MultipartFile file) {
        return importExcel(file, 4);
    }
}

package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.AnalysisOfBiologicalFactorsDto;
import cn.dhbin.isme.ims.domain.dto.excel.AnalysisOfBiologicalFactorsExcelDto;
import cn.dhbin.isme.ims.domain.request.AnalysisOfBiologicalFactorsRequest;
import cn.dhbin.isme.ims.domain.request.AnalysisRequest;
import cn.dhbin.isme.ims.service.AnalysisOfBiologicalFactorsService;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/analysis-of-biological-factors")
@RequiredArgsConstructor
public class AnalysisOfBiologicalFactorsController {
    private final AnalysisOfBiologicalFactorsService analysisOfBiologicalFactorsService;

    /**
     * 查询
     *
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<AnalysisOfBiologicalFactorsDto>> selectAll(AnalysisOfBiologicalFactorsRequest request) {
        Page<AnalysisOfBiologicalFactorsDto> ret = analysisOfBiologicalFactorsService.queryPage(request);
        System.out.println("-------------------------------------------");
        System.out.println(ret);
        return R.ok(ret);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        analysisOfBiologicalFactorsService.removeById(id);
        return R.ok();
    }

    /**
     * 新增
     *
     * @param request
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody AnalysisRequest request) {
        analysisOfBiologicalFactorsService.addAbundance(
                request.getDistributeId(),
                request.getSampleType(),
                request.getAbundance(),
                request.getSampleTypes(),
                request.getReport()
        );
        return R.ok();
    }

    /**
     * 修改
     *
     * @param request
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody AnalysisRequest request) {
        analysisOfBiologicalFactorsService.updateAbundance(request.getId(),
                request.getDistributeId(),
                request.getSampleType(),
                request.getAbundance(),
                request.getSampleTypes(),
                request.getReport());
        return R.ok();
    }

    @GetMapping("list")
    public R<List<AnalysisOfBiologicalFactorsDto>> selectList(@RequestParam Integer distributeId) {
        List<AnalysisOfBiologicalFactorsDto> AnalysisOfBiologicalFactorsDtos = analysisOfBiologicalFactorsService.queryList(distributeId);
        return R.ok(AnalysisOfBiologicalFactorsDtos);
    }

    /**
     * 根据站点ID获取微观繁殖体分析数据
     * @param stationId 站点ID
     * @return 微观繁殖体分析数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<AnalysisOfBiologicalFactorsDto>> getByStationId(@PathVariable Integer stationId) {
        List<AnalysisOfBiologicalFactorsDto> list = analysisOfBiologicalFactorsService.queryList(stationId);
        return R.ok(list);
    }
    
    /**
     * 导出Excel
     */
    @GetMapping("/export")
    public void exportExcel(AnalysisOfBiologicalFactorsRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<AnalysisOfBiologicalFactorsExcelDto> list = analysisOfBiologicalFactorsService.queryListForExport(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "微观繁殖体丰富度数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, AnalysisOfBiologicalFactorsExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("丰富度数据")
                    .doWrite(list);

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 读取Excel文件
            List<AnalysisOfBiologicalFactorsExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(AnalysisOfBiologicalFactorsExcelDto.class)
                    .sheet()
                    .doReadSync();

            analysisOfBiologicalFactorsService.importData(list);
            return R.ok("导入成功");
        } catch (BizException e) {
            return R.build(e);
        } catch (Exception e) {
            e.printStackTrace();
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }
    
    /**
     * 沉积物导出Excel
     */
    @GetMapping("/sediment/export")
    public void exportSedimentExcel(AnalysisOfBiologicalFactorsRequest request, HttpServletResponse response) throws IOException {
        request.setSampleType(0); // 设置样品类型为沉积物
        exportExcel(request, response);
    }

    /**
     * 沉积物导入Excel
     */
    @PostMapping("/sediment/import")
    public R<String> importSedimentExcel(@RequestParam("file") MultipartFile file) {
        return importExcel(file);
    }

    /**
     * 底层水样导出Excel
     */
    @GetMapping("/bottom-water/export")
    public void exportBottomWaterExcel(AnalysisOfBiologicalFactorsRequest request, HttpServletResponse response) throws IOException {
        request.setSampleType(1); // 设置样品类型为底层水样
        exportExcel(request, response);
    }

    /**
     * 底层水样导入Excel
     */
    @PostMapping("/bottom-water/import")
    public R<String> importBottomWaterExcel(@RequestParam("file") MultipartFile file) {
        return importExcel(file);
    }

    /**
     * 表层水样导出Excel
     */
    @GetMapping("/surface-water/export")
    public void exportSurfaceWaterExcel(AnalysisOfBiologicalFactorsRequest request, HttpServletResponse response) throws IOException {
        request.setSampleType(2); // 设置样品类型为表层水样
        exportExcel(request, response);
    }

    /**
     * 表层水样导入Excel
     */
    @PostMapping("/surface-water/import")
    public R<String> importSurfaceWaterExcel(@RequestParam("file") MultipartFile file) {
        return importExcel(file);
    }
}
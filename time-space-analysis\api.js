import { request } from '@/utils'

export default {
  createManage: data => request.post('/gtsusys-staff-manage', data),
  readManage: (params = {}) => request.get('/gtsusys-staff-manage', { params }),
  updateManage: data => request.patch(`/gtsusys-staff-manage`, data),
  deleteManage: id => request.delete(`/gtsusys-staff-manage/${id}`),
  batchInsert: data => request.post('/gtsusys-staff-manage/batch-insert', data),
  getManageList: () => request.get('/gtsusys-staff-manage/list'),

  createScale: data => request.post('/station-point-scale', data),
  readScale: (params = {}) => request.get('/station-point-scale', { params }),
  updateScale: data => request.patch(`/station-point-scale`, data),
  deleteScale: id => request.delete(`/station-point-scale/${id}`),
  getScaleList: () => request.get('/station-point-scale/list'),

  createDistribute: data => request.post('/station-point-distribute', data),
  readDistribute: (params = {}) => request.get('/station-point-distribute', { params }),
  updateDistribute: data => request.patch(`/station-point-distribute`, data),
  deleteDistribute: id => request.delete(`/station-point-distribute/${id}`),
  getStationPoints: () => request.get('/station-point-scale/list'),

  getPointDistributes: (scaleId, taskId, times) => request.get(`/station-point-scale/pointsList?scaleId=${scaleId}&taskId=${taskId}&times=${times}`),
  getPointDistributes2: scaleId => request.get(`/station-point-scale/pointsList?scaleId=${scaleId}`),

  predictWaterQuality: (longitude, latitude, scaleId, taskId, times) => request.get(`/waterquality/predict?longitude=${longitude}&latitude=${latitude}&scaleId=${scaleId}&taskId=${taskId}&times=${times}`),

  getGroupList: () => request.get('/gtsusys-staff-group/list'),
  createGtsusysStaffGroup: data => request.post('/gtsusys-staff-group', data),
  readGtsusysStaffGroup: (params = {}) => request.get('/gtsusys-staff-group', { params }),
  updateGtsusysStaffGroup: data => request.patch(`/gtsusys-staff-group`, data),
  deleteGtsusysStaffGroup: id => request.delete(`/gtsusys-staff-group/${id}`),

  createSurveyTimeRange: data => request.post('/survey-time-range', data),
  readSurveyTimeRange: (params = {}) => request.get('/survey-time-range', { params }),
  updateSurveyTimeRange: data => request.patch(`/survey-time-range`, data),
  deleteSurveyTimeRange: id => request.delete(`/survey-time-range/${id}`),
  createNewSurvey: distributeId => request.post(`/survey-time-range/createNewSurvey?distributeId=${distributeId}`),
  getRoutesByScaleId: scaleId => request.get(`/survey-time-range/routes?scaleId=${scaleId}`),
  getSurveyTimesByTaskId: taskId => request.get(`/survey-time-range/survey-times?taskId=${taskId}`),

  // 导出明确调查空间范围
  exportScale: (params = {}) => request.get('/station-point-scale/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入明确调查空间范围
  importScale: data => request.post('/station-point-scale/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  exportStation: (params = {}) => request.get('/station-point-distribute/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 作业人员导出
  exportManage: (params = {}) => request.get('/gtsusys-staff-manage/export', {
    params,
    responseType: 'arraybuffer', // 必须使用arraybuffer
    headers: {
      'Cache-Control': 'no-cache', // 避免缓存导致旧文件问题
    },
  }),

  // 作业人员导入
  importManage: data => request.post('/gtsusys-staff-manage/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出人员单位数据
  exportStaffGroup: (params = {}) => request.get('/gtsusys-staff-group/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入人员单位数据
  importStaffGroup: data => request.post('/gtsusys-staff-group/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  uploadImg: file => request.post('/upload/localImg', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
}

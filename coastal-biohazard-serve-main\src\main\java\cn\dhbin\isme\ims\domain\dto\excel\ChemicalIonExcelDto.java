/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-04-14 21:30:12
 * @LastEditors: <PERSON>z<PERSON>
 * @LastEditTime: 2025-04-14 21:33:01
 * @Description: 请填写简介
 */
package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 海水化学离子数据 Excel 导入导出DTO
 */
@Data
public class ChemicalIonExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;
    
    @ExcelProperty("采样层次")
    @ColumnWidth(15)
    private String sampleLayer;
    
    @ExcelProperty("活性磷酸盐(mg/L)")
    @ColumnWidth(20)
    private Double activePhosphate;
    
    @ExcelProperty("亚硝酸盐(mg/L)")
    @ColumnWidth(20)
    private Double nitriteNitrogen;
    
    @ExcelProperty("硝酸盐(mg/L)")
    @ColumnWidth(20)
    private Double nitrateNitrogen;
    
    @ExcelProperty("氨根(mg/L)")
    @ColumnWidth(20)
    private Double ammoniaHydrogen;
} 
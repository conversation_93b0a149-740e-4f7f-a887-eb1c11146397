package cn.dhbin.isme.ims.domain.dto;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class MetalIonDto {
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点信息

    /**
     * 名称
     **/
    private String name;

    /**
     * 含量 - decimal(10,2)
     **/
    private BigDecimal num;
}

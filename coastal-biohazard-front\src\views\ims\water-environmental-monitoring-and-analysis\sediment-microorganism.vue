<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 导入Excel按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          新增
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1800" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select label-field="name" value-field="id" clearable v-model:value="queryItems.distributeId" filterable
          :options="stationOption" placeholder="请选择站点" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="站点" path="distributeId" :rule="{
          required: true,
          message: '请选择站点',
        }">
          <n-select label-field="name" value-field="id" @change="changeSelect" clearable
            v-model:value="modalForm.distributeId" filterable :options="stationOption" placeholder="请选择站点" />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input disabled v-model:value="coordination.longitude" placeholder="经度"></n-input>
            </n-form-item>
            <n-form-item label="纬度">
              <n-input disabled v-model:value="coordination.latitude" placeholder="纬度"></n-input>
            </n-form-item>
          </div>
        </Transition>
        
        <!-- 添加沉积物微观繁殖体特有的表单字段 -->
        <n-form-item label="采样深度" path="samplingDepth" :rule="{
          required: true,
          message: '请输入采样深度',
        }">
          <n-input placeholder="请输入采样深度" v-model:value="modalForm.samplingDepth">
            <template #suffix>
              m
            </template>
          </n-input>
        </n-form-item>
        
        <n-form-item label="沉积物类型" path="sedimentType" :rule="{
          required: true,
          message: '请输入沉积物类型',
        }">
          <n-input placeholder="请输入沉积物类型" v-model:value="modalForm.sedimentType" />
        </n-form-item>
        
        <n-form-item label="微生物种类" path="microorganismType" :rule="{
          required: true,
          message: '请输入微生物种类',
        }">
          <n-input placeholder="请输入微生物种类" v-model:value="modalForm.microorganismType" />
        </n-form-item>
        
        <n-form-item label="微生物数量" path="microorganismCount" :rule="{
          required: true,
          message: '请输入微生物数量',
        }">
          <n-input placeholder="请输入微生物数量" v-model:value="modalForm.microorganismCount">
            <template #suffix>
              CFU/g
            </template>
          </n-input>
        </n-form-item>
        
        <n-form-item label="备注" path="remarks">
          <n-input placeholder="请输入备注信息" v-model:value="modalForm.remarks" />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag, NUpload } from 'naive-ui'
import { createDiscreteApi } from 'naive-ui'
import api from './api'
import sedimentApi from '../microscopic-propagule-analysis/api'
const router = useRouter()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])
const coordination = ref({})

const getStationList = async () => {
  let { data } = await api.getListStationPoints(0)
  stationOption.value = data
}

const changeSelect = async (row) => {
  if (row != null) {
    stationOption.value.filter(item => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
        return
      }
    })
  } else {
    coordination.value = {}
  }
}

onMounted(() => {
  $table.value?.handleSearch()
  getStationList()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit
} = useCrud({
  name: '沉积物微观繁殖体',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '站点',
    width: 100,
    fixed: 'left',
    key:"stationPointDistribute.name",
    render(row) {
      return h(NTag,
        { type: 'success' },
        { default: () => row.stationPointDistribute.name })
    }
  },
  {
    title: '采样深度', ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.samplingDepth + "m")
    },
  },
  {
    title: '沉积物类型', ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.sedimentType)
    },
  },
  {
    title: '微生物种类', ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.microorganismType)
    },
  },
  {
    title: '微生物数量', ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.microorganismCount + "CFU/g")
    },
  },
  {
    title: '备注', ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.remarks)
    },
  },
  {
    width: 130,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导出功能
async function handleExport() {
  try {
    const response = await sedimentApi.exportSedimentMicroorganism(queryItems.value)

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `沉积物微观繁殖体数据_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, msg } = await sedimentApi.importSedimentMicroorganism(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style> 
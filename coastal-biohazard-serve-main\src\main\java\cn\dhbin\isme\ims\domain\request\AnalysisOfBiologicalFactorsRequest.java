package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class AnalysisOfBiologicalFactorsRequest extends PageRequest {

    /**
     * 站点id
     **/
    private Integer distributeId;

    /**
     * 样品类型
     **/
    private Integer sampleType;


public Serializable pkVal() {
          return null;
      }
}



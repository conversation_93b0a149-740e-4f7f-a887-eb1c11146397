package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.auth.RoleType;
import cn.dhbin.isme.common.auth.Roles;
import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.service.impl.SedimentExcelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/sediment")
@RequiredArgsConstructor
@Tag(name = "沉积物微观繁殖体Excel导入导出")
@Slf4j
public class SedimentExcelController {
    private final SedimentExcelService excelService;

    @GetMapping(value = "/export", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导出沉积物微观繁殖体Excel")
    public ResponseEntity<byte[]> exportToExcel(
            @RequestParam(required = false) Integer distributeId,
            @RequestParam(required = false, defaultValue = "3") Integer sampleType) throws Exception {
        try {
            log.info("开始导出沉积物Excel: distributeId={}, sampleType={}", distributeId, sampleType);
            byte[] excelContent = excelService.exportToExcel(distributeId, sampleType);
            
            // 如果没有数据，则生成一个只有提示信息的Excel
            if (excelContent.length < 100) { // 简单判断空内容
                log.warn("沉积物数据为空，生成提示信息Excel");
                excelContent = generateEmptyDataExcel("沉积物微观繁殖体数据");
            }
            
            String filename = URLEncoder.encode("沉积物微观繁殖体数据.xlsx", StandardCharsets.UTF_8.toString());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", filename);
            headers.setCacheControl("no-cache, no-store, must-revalidate");
            headers.setPragma("no-cache");
            headers.setExpires(0);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelContent);
        } catch (Exception e) {
            log.error("导出沉积物Excel失败", e);
            throw new BizException(BizResponseCode.ERR_11011, "导出失败: " + e.getMessage());
        }
    }

    /**
     * 生成一个空数据的Excel，但包含提示信息
     */
    private byte[] generateEmptyDataExcel(String sheetName) throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"站点", "样品类型", "丰富度(ind./50g)", "创建时间", "更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }
            
            // 创建提示行
            Row tipRow = sheet.createRow(1);
            Cell tipCell = tipRow.createCell(0);
            tipCell.setCellValue("暂无数据，请先添加数据再导出");
            
            // 合并单元格用于显示提示信息
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4));
            
            // 设置样式
            CellStyle tipStyle = workbook.createCellStyle();
            tipStyle.setAlignment(HorizontalAlignment.CENTER);
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(IndexedColors.RED.getIndex());
            tipStyle.setFont(font);
            tipCell.setCellStyle(tipStyle);
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Roles({RoleType.SUPER_ADMIN, RoleType.SYS_ADMIN})
    @Operation(summary = "导入沉积物微观繁殖体Excel")
    public R<Void> importFromExcel(
            @RequestParam(required = false) Integer distributeId,
            @RequestParam(required = false, defaultValue = "3") Integer sampleType,
            @RequestParam("file") MultipartFile file) {
        try {
            log.info("开始导入沉积物Excel: distributeId={}, sampleType={}, fileName={}", 
                    distributeId, sampleType, file.getOriginalFilename());
            
            if (file == null || file.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请选择要导入的Excel文件"));
            }
            
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return R.build(new BizException(BizResponseCode.ERR_11011, "请上传Excel文件(.xlsx或.xls格式)"));
            }
            
            excelService.importFromExcel(file, distributeId, sampleType);
            log.info("沉积物Excel导入成功");
            return R.ok();
        } catch (Exception e) {
            log.error("导入沉积物Excel失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11011, "导入失败：" + e.getMessage()));
        }
    }
} 
package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.GtsusysStaffManageDto;
import cn.dhbin.isme.ims.domain.dto.SelectOptionDto;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffManage;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffManageRequest;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import cn.dhbin.isme.ims.service.SampleTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sample-type")
@RequiredArgsConstructor
public class SampleTypeController {
    private final SampleTypeService sampleTypeService;
    @GetMapping("/list")
    public R<List<SampleType>> listSampleTypes() {
        List<SampleType> stationPoints = sampleTypeService.listSampleTypes();
        return R.ok(stationPoints);
    }

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<SampleType>> selectAll(SampleTypeRequest request) {
        Page<SampleType> ret = sampleTypeService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody SampleType data) {
        sampleTypeService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody SampleType data) {
        sampleTypeService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        sampleTypeService.removeById(id);
        return R.ok();
    }
}

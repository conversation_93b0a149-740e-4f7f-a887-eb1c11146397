package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.dto.MapDataAggregationDto;
import cn.dhbin.isme.ims.domain.entity.*;
import cn.dhbin.isme.ims.service.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 地图数据聚合服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MapDataAggregationServiceImpl implements MapDataAggregationService {

    private final StationPointScaleService stationPointScaleService;
    private final StationPointDistributeService stationPointDistributeService;
    private final WaterPhWeatherDataService waterPhWeatherDataService;
    private final ChemicalIonService chemicalIonService;
    private final AbundanceLayerSpeciesDataService abundanceLayerSpeciesDataService;
    private final AnalysisSampleTypeService analysisSampleTypeService;

    @Override
    public MapDataAggregationDto getMapData(Integer scaleId, Integer taskId, Integer timesId) {
        MapDataAggregationDto result = new MapDataAggregationDto();
        
        try {
            // 1. 获取调查中心信息
            StationPointScale scale = stationPointScaleService.getById(scaleId);
            if (scale != null) {
                MapDataAggregationDto.StationPointScaleInfo scaleInfo = new MapDataAggregationDto.StationPointScaleInfo();
                BeanUtils.copyProperties(scale, scaleInfo);
                result.setStationPointScale(scaleInfo);
            }
            
            // 2. 获取站点分布信息
            List<MapDataAggregationDto.StationDistributeInfo> stationList = getStationDistributeData(scaleId, taskId, timesId);
            result.setStationPointDistribute(stationList);
            
            log.info("地图数据聚合完成，调查中心ID: {}, 任务ID: {}, 调查次数ID: {}, 站点数量: {}", 
                    scaleId, taskId, timesId, stationList.size());
            
        } catch (Exception e) {
            log.error("地图数据聚合异常", e);
            // 返回空结果而不是抛出异常
            result.setStationPointDistribute(Collections.emptyList());
        }
        
        return result;
    }

    /**
     * 获取站点分布数据
     */
    private List<MapDataAggregationDto.StationDistributeInfo> getStationDistributeData(Integer scaleId, Integer taskId, Integer timesId) {
        // 构建查询条件
        LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationPointDistribute::getScaleId, scaleId);
        
        if (taskId != null) {
            queryWrapper.eq(StationPointDistribute::getTaskId, taskId);
        }
        if (timesId != null) {
            queryWrapper.eq(StationPointDistribute::getTimesId, timesId);
        }
        
        queryWrapper.orderByAsc(StationPointDistribute::getCreateTime);
        
        List<StationPointDistribute> stations = stationPointDistributeService.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(stations)) {
            return Collections.emptyList();
        }
        
        // 转换为DTO并聚合关联数据
        return stations.stream()
                .map(this::convertToStationDistributeInfo)
                .collect(Collectors.toList());
    }

    /**
     * 转换站点实体为DTO并聚合关联数据
     */
    private MapDataAggregationDto.StationDistributeInfo convertToStationDistributeInfo(StationPointDistribute station) {
        MapDataAggregationDto.StationDistributeInfo info = new MapDataAggregationDto.StationDistributeInfo();
        
        // 基础信息
        info.setId(station.getId());
        info.setScaleId(station.getScaleId());
        info.setTaskId(station.getTaskId());
        info.setTimesId(station.getTimesId());
        info.setName(station.getName());
        info.setLongitude(station.getLongitude() != null ? 
                         java.math.BigDecimal.valueOf(station.getLongitude()) : null);
        info.setLatitude(station.getLatitude() != null ? 
                        java.math.BigDecimal.valueOf(station.getLatitude()) : null);
        info.setDescription(station.getDescription());
        
        // 活动类型
        info.setWpActivities(station.getWpActivities());
        info.setCiActivities(station.getCiActivities());
        info.setMbActivities(station.getMbActivities());
        info.setMrActivities(station.getMrActivities());
        
        // 站点属性
        info.setStationType(station.getStationType());
        info.setPriority(station.getPriority());
        info.setAccessibility(station.getAccessibility());
        info.setEquipmentRequirements(station.getEquipmentRequirements());
        
        // 调查时间
        if (station.getBeforeInvestigate() != null) {
            info.setBeforeInvestigate(station.getBeforeInvestigate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        if (station.getAfterInvestigate() != null) {
            info.setAfterInvestigate(station.getAfterInvestigate().toInstant()
                    .atZone(ZoneId.systemDefault()).toLocalDateTime());
        }
        
        // 聚合关联数据
        aggregateRelatedData(info, station.getId());
        
        return info;
    }

    /**
     * 聚合关联数据
     */
    private void aggregateRelatedData(MapDataAggregationDto.StationDistributeInfo stationInfo, Integer stationId) {
        try {
            // 水文气象数据
            stationInfo.setWaterPhWeatherData(getWaterPhWeatherData(stationId));
            
            // 化学离子数据
            stationInfo.setChemicalIon(getChemicalIonData(stationId));
            
            // 生物量数据
            stationInfo.setAbundanceLayerSpeciesDataList(getAbundanceLayerSpeciesData(stationId));
            
        } catch (Exception e) {
            log.warn("聚合站点关联数据异常，站点ID: {}", stationId, e);
        }
    }

    /**
     * 获取水文气象数据
     */
    private MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo getWaterPhWeatherData(Integer stationId) {
        LambdaQueryWrapper<WaterPhWeatherData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WaterPhWeatherData::getDistributeId, stationId)
                   .orderByDesc(WaterPhWeatherData::getCreateTime)
                   .last("LIMIT 1");
        
        WaterPhWeatherData weatherData = waterPhWeatherDataService.getOne(queryWrapper);
        if (weatherData == null) {
            return null;
        }
        
        MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo info = 
                new MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo();
        
        BeanUtils.copyProperties(weatherData, info);
        
        // 处理时间字段
        setTimeFields(info, weatherData.getCreateTime(), weatherData.getUpdateTime());
        
        return info;
    }

    /**
     * 获取化学离子数据
     */
    private MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo getChemicalIonData(Integer stationId) {
        LambdaQueryWrapper<ChemicalIon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChemicalIon::getDistributeId, stationId)
                   .orderByDesc(ChemicalIon::getCreateTime)
                   .last("LIMIT 1");
        
        ChemicalIon chemicalData = chemicalIonService.getOne(queryWrapper);
        if (chemicalData == null) {
            return null;
        }
        
        MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo info = 
                new MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo();
        
        BeanUtils.copyProperties(chemicalData, info);
        
        // 处理时间字段
        setTimeFields(info, chemicalData.getCreateTime(), chemicalData.getUpdateTime());
        
        return info;
    }

    /**
     * 获取生物量数据
     */
    private List<MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo> getAbundanceLayerSpeciesData(Integer stationId) {
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AbundanceLayerSpeciesData::getDistributeId, stationId)
                   .orderByDesc(AbundanceLayerSpeciesData::getCreateTime);
        
        List<AbundanceLayerSpeciesData> abundanceList = abundanceLayerSpeciesDataService.list(queryWrapper);
        if (CollectionUtils.isEmpty(abundanceList)) {
            return Collections.emptyList();
        }
        
        return abundanceList.stream()
                .map(this::convertToAbundanceInfo)
                .collect(Collectors.toList());
    }

    /**
     * 转换生物量数据
     */
    private MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo convertToAbundanceInfo(AbundanceLayerSpeciesData abundance) {
        MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo info = 
                new MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo();
        
        BeanUtils.copyProperties(abundance, info);
        
        // 处理时间字段
        setTimeFields(info, abundance.getCreateTime(), abundance.getUpdateTime());
        
        // 获取样品类型列表（这里简化处理，实际可能需要查询关联表）
        info.setSampleTypeList(getSampleTypeList(abundance.getId()));
        
        return info;
    }

    /**
     * 获取样品类型列表
     */
    private List<MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo.SampleTypeInfo> getSampleTypeList(Integer abundanceId) {
        // 这里简化处理，返回空列表
        // 实际应该查询analysis_sample表获取关联的样品类型
        return Collections.emptyList();
    }

    /**
     * 设置时间字段（通用方法）
     */
    private void setTimeFields(Object info, java.util.Date createTime, java.util.Date updateTime) {
        try {
            if (createTime != null) {
                LocalDateTime beforeInvestigate = createTime.toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                
                // 使用反射设置字段，这里简化处理
                if (info instanceof MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo) info)
                            .setBeforeInvestigate(beforeInvestigate);
                } else if (info instanceof MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo) info)
                            .setBeforeInvestigate(beforeInvestigate);
                } else if (info instanceof MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo) info)
                            .setBeforeInvestigate(beforeInvestigate);
                }
            }
            
            if (updateTime != null) {
                LocalDateTime afterInvestigate = updateTime.toInstant()
                        .atZone(ZoneId.systemDefault()).toLocalDateTime();
                
                // 设置结束时间
                if (info instanceof MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.WaterPhWeatherDataInfo) info)
                            .setAfterInvestigate(afterInvestigate);
                } else if (info instanceof MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.ChemicalIonInfo) info)
                            .setAfterInvestigate(afterInvestigate);
                } else if (info instanceof MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo) {
                    ((MapDataAggregationDto.StationDistributeInfo.AbundanceLayerSpeciesDataInfo) info)
                            .setAfterInvestigate(afterInvestigate);
                }
            }
        } catch (Exception e) {
            log.warn("设置时间字段异常", e);
        }
    }
} 
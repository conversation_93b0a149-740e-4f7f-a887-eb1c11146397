<!--------------------------------
-现场调查时空分析模块
-4.一线作业人员管理
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 新增导出按钮 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <n-upload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </n-upload>
        <NButton type="info" @click="add()">
          <i class="i-material-symbols:add mr-4 text-18" />
          批量新增部门人员
        </NButton>
        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          新增部门人员
        </NButton>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="api.readManage"
    >
      <MeQueryItem label="姓名" :label-width="70">
        <n-input
          v-model:value="queryItems.name"
          type="text"
          placeholder="请输入姓名"
          clearable
        />
      </MeQueryItem>
      <MeQueryItem label="性别" :label-width="70">
        <n-select
          v-model:value="queryItems.gender"
          clearable
          :options="genders"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef"
        :rules="rules"
        label-placement="left"
        label-align="left"
        :label-width="70"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <n-form-item label="部门" path="groupId">
          <n-select
            v-model:value="modalForm.groupId"
            placeholder="请选择部门"
            label-field="name"
            value-field="id"
            clearable
            :options="groupOption"
          />
        </n-form-item>
        <div
          v-if="modalAction === 'beachAdd'"
          style="display: flex; gap: 8px; flex-direction: column"
        >
          <NButton type="primary" @click="onAdd">
            添加该部门人员信息
          </NButton>
          <div
            v-for="(manage, index) in modalForm.manageList"
            :key="index"
            style="
              display: flex;
              flex-direction: column;
              gap: 4px;
              margin-top: 32px;
            "
          >
            <n-form-item
              label="姓名"
              :rule-path="`manageList[${index}].name`"
              :path="`manageList[${index}].name`"
            >
              <n-input v-model:value="manage.name" placeholder="请输入姓名" />
            </n-form-item>
            <n-form-item
              label="性别"
              :rule-path="`manageList[${index}].gender`"
              :path="`manageList[${index}].gender`"
            >
              <n-select
                v-model:value="manage.gender"
                placeholder="请选择性别"
                clearable
                :options="genders"
              />
            </n-form-item>
            <n-form-item
              label="身份证"
              :rule-path="`manageList[${index}].idCard`"
              :path="`manageList[${index}].idCard`"
            >
              <n-input
                v-model:value="manage.idCard"
                placeholder="请输入身份证号"
              />
            </n-form-item>
            <NButton ghost type="error" @click="removeCoordinate(index)">
              移除
            </NButton>
          </div>
        </div>
        <div v-if="modalAction !== 'beachAdd'">
          <n-form-item label="姓名" path="name">
            <n-input v-model:value="modalForm.name" placeholder="请输入姓名" />
          </n-form-item>
          <n-form-item label="性别" path="gender">
            <n-select
              v-model:value="modalForm.gender"
              placeholder="请选择性别"
              clearable
              :options="genders"
            />
          </n-form-item>
          <n-form-item label="身份证" path="idCard">
            <n-input
              v-model:value="modalForm.idCard"
              placeholder="请输入身份证号"
            />
          </n-form-item>
        </div>
      </n-form>
      <n-alert v-if="modalAction === 'edit'" type="warning" closable>
        详细信息需由用户本人补充修改
      </n-alert>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NTag } from 'naive-ui'
import api from './api'

// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const groupOption = ref([])

function checkIdNum(rule, value, callback) {
  if (!value) {
    return callback(new Error('身份证不能为空'))
  }
  if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}([\dX])$)/i.test(value)) {
    callback(new Error('请输入正确的身份证号码'))
  }
  else {
    callback()
  }
}

const rules = reactive({
  groupId: { required: true, message: '请选择部门' },
  manageList: [
    {
      name: { required: true, message: '请输入姓名', trigger: ['input', 'blur'] },
      gender: { required: true, message: '请选择性别' },
      idCard: { required: true, validator: checkIdNum, trigger: ['input', 'blur'] },
    },
  ],
  name: { required: true, message: '请输入姓名', trigger: ['input', 'blur'] },
  gender: { required: true, message: '请选择性别' },
  idCard: { required: true, validator: checkIdNum, trigger: ['input', 'blur'] },
})

async function getGroupOption() {
  const { data } = await api.getGroupList()
  groupOption.value = data
}

onMounted(() => {
  $table.value?.handleSearch()
  getGroupOption()
})

const genders = [
  { label: '男', value: 1 },
  { label: '女', value: 0 },
]

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '部门人员',
  initForm: {
    enable: true,
    manageList: [
      {
        name: '',
        gender: 1,
        idCard: '',
      },
    ],
  },
  doCreate: api.createManage,
  doDelete: api.deleteManage,
  doUpdate: api.updateManage,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    // width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  { title: '姓名', key: 'name', ellipsis: { tooltip: true } },
  {
    title: '性别',
    key: 'gender',
    // width: 80,
    render: ({ gender }) => genders.find(item => gender === item.value)?.label ?? '',
  },
  { title: '身份证号', key: 'idCard', ellipsis: { tooltip: true } },
  {
    title: '部门',
    key: 'groupName',
    render: ({ groupName }) =>
      h(NTag, { type: 'primary' }, { default: () => groupName },
      ),
  },
  {
    // width: 180,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-carbon:user-role text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

// 添加新人员信息
function onAdd() {
  modalForm.value.manageList.push({
    name: '',
    idCard: '',
    gender: 1,
  })
  rules.manageList.push({
    name: { required: true, message: '请输入姓名', trigger: ['input', 'blur'] },
    gender: { required: true, message: '请选择性别' },
    idCard: { required: true, validator: checkIdNum, trigger: ['input', 'blur'] },
  })
}

// 移除指定索引处的人员信息
function removeCoordinate(index) {
  modalForm.value.manageList.splice(index, 1)
  rules.manageList.splice(index, 1)
}

function add() {
  modalForm.value.manageList = [{ name: '', gender: 1, idCard: '' }]
  handleOpen({
    action: 'beachAdd',
    title: '批量新增人员信息',
    row: modalForm.value,
    onOk: async () => {
      await modalFormRef.value?.validate()
      console.log(modalForm.value, 'modalForm')
      await api.batchInsert(modalForm.value)
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportManage({ /* 你的参数 */ })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `一线作业人员_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    $message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    $message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message } = await api.importManage(formData)

    if (code === 0) {
      $message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      $message.error(message || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    $message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}
</script>

<style lang="scss" scoped></style>

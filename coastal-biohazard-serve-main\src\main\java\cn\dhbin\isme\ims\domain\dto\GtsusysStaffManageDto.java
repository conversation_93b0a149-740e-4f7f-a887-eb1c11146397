package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.GtsusysStaffGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 一线作业人员表(GtsusysStaffManage)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GtsusysStaffManageDto {
    private Integer id;

    private String name;

    private Integer gender;

    private String idCard;

    private String description;

    private Date createTime;

    private Date updateTime;

    private Integer groupId;

    private String groupName;
    
public Serializable pkVal() {
          return null;
      }
}



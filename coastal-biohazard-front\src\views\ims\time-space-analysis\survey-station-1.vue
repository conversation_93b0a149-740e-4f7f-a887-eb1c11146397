<!--------------------------------
-现场调查时空分析模块
-2.合理布设调查站位
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud
      ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="handleGetData"
    >
      <MeQueryItem label="站点名称" :label-width="70">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入站点名称" clearable />
      </MeQueryItem>
      <MeQueryItem label="调查中心" :label-width="70">
        <n-select
          v-model:value="queryItems.scaleId" label-field="name" value-field="id" clearable filterable
          :options="stationOption" placeholder="请选择调查中心"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" :rules="rules" label-placement="left" label-align="left" :label-width="110"
        :model="modalForm" :disabled="modalAction === 'view'"
      >
        <n-form-item label="调查中心" path="scaleId">
          <n-select
            v-model:value="modalForm.scaleId" label-field="name" value-field="id" clearable filterable
            :options="stationOption" placeholder="请选择调查中心"
          />
        </n-form-item>
        <n-form-item label="站点名称" path="name">
          <n-input v-model:value="modalForm.name" placeholder="请输入名称">
            <template #prefix>
              <!-- <n-icon :component="FlashOutline" /> -->
              <span style="color:rgb(112 112 112)">站点</span>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="经度" path="longitude">
          <n-input-number v-model:value="modalForm.longitude" style="width: 100%;" placeholder="请输入经度" />
        </n-form-item>
        <n-form-item label="纬度" path="latitude">
          <n-input-number v-model:value="modalForm.latitude" style="width: 100%;" placeholder="请输入纬度" />
        </n-form-item>
        <!-- <n-form-item label="概述" path="description" >
          <n-input v-model:value="modalForm.description" />
        </n-form-item> -->

        <!--        <n-form-item label="作业时间范围" rule-path="range" path="range"> -->
        <!--          <n-date-picker v-model:value="modalForm.range" type="datetimerange" clearable /> -->
        <!--        </n-form-item> -->
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { createDiscreteApi, NButton, NTag, NUpload } from 'naive-ui'
import { h, nextTick, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'

import api from './api'

const router = useRouter()

// 自定义数据获取函数，用于调试和优化
async function handleGetData(params = {}) {
  console.log('📊 survey-station-1: 开始获取数据，参数:', params)
  
  try {
    // 确保传递正确的参数
    const requestParams = {
      ...params,
      // 如果没有任何筛选条件，添加一个标志让后端返回所有数据
      _fetchAll: !params.scaleId && !params.name
    }
    
    console.log('🔍 survey-station-1: 实际请求参数:', requestParams)
    
    const result = await api.readDistribute(requestParams)
    console.log('✅ survey-station-1: 获取数据成功:', result)
    
    // 检查返回的数据结构
    if (result && result.data) {
      const data = result.data
      const itemCount = data.pageData ? data.pageData.length : (data.length || 0)
      const totalCount = data.total || itemCount
      
      console.log('📈 survey-station-1: 数据统计:', {
        当前页数据条数: itemCount,
        总数据条数: totalCount,
        数据结构: Array.isArray(data.pageData) ? 'pageData数组' : Array.isArray(data) ? '直接数组' : '其他结构'
      })
      
      // 详细分析第一条数据的结构
      const firstItem = data.pageData ? data.pageData[0] : (Array.isArray(data) ? data[0] : null)
      if (firstItem) {
        console.log('🔍 survey-station-1: 第一条数据详细结构:', {
          id: firstItem.id,
          name: firstItem.name,
          scaleId: firstItem.scaleId,
          stationPointScale: firstItem.stationPointScale,
          scaleName: firstItem.scaleName,
          groupName: firstItem.groupName,
          完整数据: firstItem
        })
      }
      
      // 如果没有数据，记录详细信息
      if (itemCount === 0) {
        console.warn('⚠️ survey-station-1: 没有获取到数据，可能的原因:')
        console.warn('- 后端筛选逻辑过滤了数据')
        console.warn('- 数据库中确实没有数据')
        console.warn('- 查询参数有问题')
        console.warn('请求参数:', requestParams)
      }
    }
    
    return result
  } catch (error) {
    console.error('❌ survey-station-1: 获取数据失败:', error)
    throw error
  }
}
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const now = Date.now()
// let range = ref([now - 30 * 24 * 60 * 60 * 1000, now])

const rules = reactive({
  name: { required: true, message: '请输入站点名称', trigger: ['input', 'blur'] },
  longitude: { required: true, message: '请输入经度', type: 'number', trigger: ['input', 'blur'] },
  latitude: { required: true, message: '请输入纬度', type: 'number', trigger: ['input', 'blur'] },
  description: { required: true, message: '请输入站点概述', trigger: ['input', 'blur'] },
  range: { required: true, message: '请选择日期范围', type: 'array', trigger: ['change', 'blur'] },
  scaleId: { required: true, message: '请选择调查中心', type: 'number', trigger: ['change', 'blur'] },
})

async function getStationOption() {
  try {
    console.log('🔍 survey-station-1: 开始获取调查中心列表...')
  const { data } = await api.getStationPoints()
    console.log('✅ survey-station-1: 获取调查中心列表成功:', data)
  stationOption.value = data
    
    // 详细分析调查中心选项结构
    if (data && data.length > 0) {
      console.log('🏢 survey-station-1 调查中心选项详细结构:', {
        总数量: data.length,
        第一个选项: data[0],
        所有选项: data.map(item => ({ id: item.id, name: item.name }))
      })
      
      // 检查是否包含"海岸带调查1"
      const coastalCenter = data.find(item => item.name && item.name.includes('海岸带调查'))
      if (coastalCenter) {
        console.log('🎯 survey-station-1 找到海岸带调查中心:', coastalCenter)
      } else {
        console.warn('⚠️ survey-station-1 未找到包含"海岸带调查"的选项')
      }
    }
  } catch (error) {
    console.error('❌ survey-station-1: 获取调查中心列表失败:', error)
    stationOption.value = []
  }
}

onMounted(async () => {
  console.log('🚀 survey-station-1页面开始初始化')
  
  // 先获取站点选项
  await getStationOption()
  console.log('📍 站点选项加载完成:', stationOption.value)
  
  // 初始化查询参数，确保获取所有数据
  if (!queryItems.value.scaleId && !queryItems.value.name) {
    console.log('📊 设置默认查询参数以获取所有数据')
    // 不设置任何筛选条件，让后端返回所有数据
    queryItems.value = { ...queryItems.value }
  }
  
  console.log('🔍 执行数据搜索，查询参数:', queryItems.value)
  $table.value?.handleSearch()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '调查站点',
  initForm: {
    enable: true,
    range: [
      now - 30 * 24 * 60 * 60 * 1000,
      now,
    ],
  },
  doCreate: api.createDistribute,
  doDelete: api.deleteDistribute,
  doUpdate: api.updateDistribute,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '调查中心',
    key: 'groupName',
    render: (row) => {
      // 只为第一条数据添加详细调试信息
      if (row.id && !window._debuggedFirstRow1) {
        console.log('🏢 survey-station-1 调查中心数据结构调试 (第一条):', {
          完整行数据: row,
          stationPointScale: row.stationPointScale,
          scaleId: row.scaleId,
          scaleName: row.scaleName,
          groupName: row.groupName
        })
        window._debuggedFirstRow1 = true
      }
      
      // 尝试多种可能的字段获取调查中心名称
      let centerName = '未指定'
      
      if (row.stationPointScale?.name) {
        centerName = row.stationPointScale.name
      } else if (row.scaleName) {
        centerName = row.scaleName
      } else if (row.groupName) {
        centerName = row.groupName
      } else if (row.scaleId) {
        // 如果只有ID，从选项中查找名称
        const foundOption = stationOption.value.find(option => option.id === row.scaleId)
        if (foundOption) {
          centerName = foundOption.name
        } else {
          centerName = `调查中心-${row.scaleId}`
        }
      }
      
      // 只为第一条数据记录最终名称
      if (row.id && !window._debuggedFirstRowName1) {
        console.log('🏷️ survey-station-1 最终显示的调查中心名称 (第一条):', centerName)
        window._debuggedFirstRowName1 = true
      }
      
      return h(NTag, { type: 'primary' }, { default: () => centerName })
    },
  },
  { title: '站点名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '经度', key: 'longitude', ellipsis: { tooltip: true } },
  { title: '纬度', key: 'latitude', ellipsis: { tooltip: true } },
  // { title: '概述', key: 'description', ellipsis: { tooltip: true } },
  // {
  //   title: '调查开始时间',
  //   key: 'beforeInvestigate',
  //   width: 180,
  //   render(row) {
  //     return h('span', formatDateTime(row.createTime))
  //   },
  // },
  // {
  //   title: '调查结束时间',
  //   key: 'afterInvestigate',
  //   width: 180,
  //   render(row) {
  //     return h('span', formatDateTime(row.createTime))
  //   },
  // },

  {
    width: 260,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            dashed: true,
            // secondary: true,
            onClick: () =>
              router.push({ path: `/ims/survey-time-range`, query: { distributeId: row.id, distributeName: row.name } }),
          },
          {
            default: () => '作业时间管理',
            icon: () => h('i', { class: 'i-fe:trello text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

// async function handleEnable(row) {
//   row.enableLoading = true
//   try {
//     await api.update({ id: row.id, enable: !row.enable })
//     row.enableLoading = false
//     $message.success('操作成功')
//     $table.value?.handleSearch()
//   }
//   catch (error) {
//     console.error(error)
//     row.enableLoading = false
//   }
// }

// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importDistribute(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportDistribute({ name: queryItems.value.name })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `调查站点分布_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}
</script>

<style lang="scss" scoped></style>

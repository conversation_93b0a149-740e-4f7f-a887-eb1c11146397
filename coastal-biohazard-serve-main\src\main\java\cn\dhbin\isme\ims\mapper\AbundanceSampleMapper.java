package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.AbundanceSample;
import cn.dhbin.isme.ims.domain.entity.SampleInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 微观繁殖体-种类中间表(AbundanceSample)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:11:26
 */
@Mapper
public interface AbundanceSampleMapper extends BaseMapper<AbundanceSample> {
    @Select("SELECT sample_id,number FROM abundance_sample WHERE abundance_id = #{abundanceId}")
    List<SampleInfo> getSampleInfosByAbundanceId(@Param("abundanceId") Integer abundanceId);

    int deleteByAbundanceId(@Param("abundanceId") Integer abundanceId);

    List<AbundanceSample> selectByAbundanceId(@Param("abundanceId") Integer abundanceId);

}


import { request } from '@/utils'

export default {
  create: data => request.post('/biodiversity', data),
  read: (params = {}) => request.get('/biodiversity', { params }),
  update: data => request.patch(`/biodiversity`, data),
  delete: id => request.delete(`/biodiversity/${id}`),
  getListStationPoints:(scaleId)=>request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  phytoplanktonExport: params=>request.get('/biodiversity/phytolankton/export',{
    params,
    responseType: 'blob',
  }),

  phytoplanktonImport: data => request.post('/biodiversity/phytolankton/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  zooplankterExport: params=>request.get('/biodiversity/zooplankter/export',{
    params,
    responseType: 'blob',
  }),

  zooplankterImport: data => request.post('/biodiversity/zooplankter/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  benthosExport: params=>request.get('/biodiversity/benthos/export',{
    params,
    responseType: 'blob',
  }),

  benthosImport: data => request.post('/biodiversity/benthos/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  nectonExport: params=>request.get('/biodiversity/necton/export',{
    params,
    responseType: 'blob',
  }),

  nectonImport: data => request.post('/biodiversity/necton/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**********************************
 * @FilePath: index.js
 * @Author: <PERSON>
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023/12/04 22:46:07
 * @Email: <EMAIL>
 * Copyright © 2023 <PERSON>(大脸怪) | https://isme.top
 **********************************/

import { createStorage } from './storage'

const prefixKey = 'vue-naive-admin_'

export const createLocalStorage = function (option = {}) {
  return createStorage({
    prefixKey: option.prefixKey || '',
    storage: localStorage,
  })
}

export const createSessionStorage = function (option = {}) {
  return createStorage({
    prefixKey: option.prefixKey || '',
    storage: sessionStorage,
  })
}

export const lStorage = createLocalStorage({ prefixKey })

export const sStorage = createSessionStorage({ prefixKey })

package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.AbundanceSample;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:29
 */
@Mapper
public interface AbundanceLayerSpeciesDataMapper extends BaseMapper<AbundanceLayerSpeciesData> {
    void insertBatch(List<AbundanceSample> samples);

}


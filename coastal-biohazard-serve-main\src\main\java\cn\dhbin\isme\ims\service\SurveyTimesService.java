package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.ims.domain.entity.SurveyTimes;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 调查次数服务接口
 */
public interface SurveyTimesService extends IService<SurveyTimes> {
    
    /**
     * 根据任务ID获取调查次数列表
     * @param taskId 任务ID
     * @return 调查次数列表
     */
    List<SurveyTimes> getSurveyTimesByTaskId(Integer taskId);
} 
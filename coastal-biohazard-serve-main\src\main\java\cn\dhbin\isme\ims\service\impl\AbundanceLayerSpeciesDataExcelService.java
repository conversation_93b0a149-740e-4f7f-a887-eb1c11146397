package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.mapper.AbundanceLayerSpeciesDataMapper;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.service.AbundanceLayerSpeciesDataService;
import cn.dhbin.isme.ims.service.SampleTypeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AbundanceLayerSpeciesDataExcelService {
    private final AbundanceLayerSpeciesDataService abundanceLayerSpeciesDataService;
    private final SampleTypeService sampleTypeService;
    private final AbundanceLayerSpeciesDataMapper abundanceLayerSpeciesDataMapper;
    private final StationPointDistributeMapper stationPointDistributeMapper;

    public List<AbundanceLayerSpeciesDataDto> queryListForExport(Integer distributeId) {
        LambdaQueryWrapper<AbundanceLayerSpeciesData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AbundanceLayerSpeciesData::getDistributeId, distributeId)
                .eq(AbundanceLayerSpeciesData::getSampleType, 2); // 表层水样

        List<AbundanceLayerSpeciesData> resultList = abundanceLayerSpeciesDataMapper.selectList(queryWrapper);

        return resultList.stream().map(data -> {
            AbundanceLayerSpeciesDataDto excelDto = new AbundanceLayerSpeciesDataDto();
            excelDto.setId(data.getId());
            excelDto.setDistributeId(data.getDistributeId());
            excelDto.setAbundance(data.getAbundance());
            excelDto.setSampleType(2); // 表层水样

            // 获取站点名称
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
            if (stationPointDistribute != null) {
                excelDto.setStationPointDistribute(stationPointDistribute);
            }

            return excelDto;
        }).collect(Collectors.toList());
    }

    public byte[] exportToExcel(Integer distributeId) throws IOException {
        List<AbundanceLayerSpeciesDataDto> dataList = queryListForExport(distributeId);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("表层水样微观繁殖体数据");

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {"站点", "样品类型", "丰富度(ind./50g)", "创建时间", "更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // Create data rows
            int rowNum = 1;
            for (AbundanceLayerSpeciesDataDto data : dataList) {
                Row row = sheet.createRow(rowNum++);
                
                // 站点名称
                String stationName = data.getStationPointDistribute() != null ? 
                        data.getStationPointDistribute().getName() : 
                        "未知站点";
                row.createCell(0).setCellValue(stationName);
                
                // 样品类型
                row.createCell(1).setCellValue("表层水样");
                
                // 丰富度
                row.createCell(2).setCellValue(data.getAbundance());
                
                // 创建时间
                row.createCell(3).setCellValue(data.getCreateTime() != null ? 
                        data.getCreateTime().toString() : "");
                
                // 更新时间
                row.createCell(4).setCellValue(data.getUpdateTime() != null ? 
                        data.getUpdateTime().toString() : "");
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    public void importFromExcel(MultipartFile file, Integer distributeId) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            List<AbundanceLayerSpeciesData> dataList = new ArrayList<>();

            // Skip header row
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                AbundanceLayerSpeciesData data = new AbundanceLayerSpeciesData();
                data.setDistributeId(distributeId);
                data.setSampleType(2); // 表层水样
                
                // 获取丰富度值
                Cell abundanceCell = row.getCell(2);
                if (abundanceCell != null) {
                    if (abundanceCell.getCellType() == CellType.NUMERIC) {
                        data.setAbundance((int) abundanceCell.getNumericCellValue());
                    } else if (abundanceCell.getCellType() == CellType.STRING) {
                        try {
                            data.setAbundance(Integer.parseInt(abundanceCell.getStringCellValue()));
                        } catch (NumberFormatException e) {
                            // 如果无法解析为整数，则跳过该行
                            continue;
                        }
                    }
                } else {
                    // 如果丰富度单元格为空，则跳过该行
                    continue;
                }

                dataList.add(data);
            }

            // Save all data
            if (!dataList.isEmpty()) {
                abundanceLayerSpeciesDataService.saveBatch(dataList);
            }
        }
    }
} 
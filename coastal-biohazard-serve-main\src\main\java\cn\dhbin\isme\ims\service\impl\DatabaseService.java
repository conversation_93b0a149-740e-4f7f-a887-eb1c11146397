package cn.dhbin.isme.ims.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

@Service
public class DatabaseService {

    private final DataSource dataSource;
    private final String exportPath;
    private final String username; // 新增：从配置中读取用户名
    private final String password; // 新增：从配置中读取密码

    // 通过构造器注入配置属性
    public DatabaseService(
            DataSource dataSource,
            @Value("${export.sql-path}") String exportPath,
            @Value("${spring.datasource.username}") String username,
            @Value("${spring.datasource.password}") String password
    ) {
        this.dataSource = dataSource;
        this.exportPath = exportPath;
        this.username = username;
        this.password = password;
    }

    public Resource exportDatabase(String dbName) throws IOException, InterruptedException {
        String os = System.getProperty("os.name").toLowerCase();
        String command;
        List<String> commandArgs;

        // 验证 mysqldump 是否可用
        checkMysqldumpAvailability();

        // 构造命令
        if (os.contains("windows")) {
            command = String.format(
                    "mysqldump -u%s -p%s %s > \"%s\\%s.sql\"",
                    username,
                    password,
                    dbName,
                    exportPath.replace("/", "\\"), // 转换路径分隔符
                    dbName
            );
            commandArgs = List.of("cmd.exe", "/c", command);
        } else {
            command = String.format(
                    "mysqldump -u%s --password=\"%s\" %s > %s/%s.sql",
                    username,
                    password,
                    dbName,
                    exportPath,
                    dbName
            );
            commandArgs = List.of("bash", "-c", command);
        }

        // 执行命令
        ProcessBuilder processBuilder = new ProcessBuilder(commandArgs)
                .inheritIO() // 继承父进程的环境变量
                .directory(new File(exportPath));

        Process process = processBuilder.start();

        // 读取输出和错误流
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream()));
             BufferedReader errorReader = new BufferedReader(
                     new InputStreamReader(process.getErrorStream()))) {

            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println("命令执行输出: " + line);
            }
            while ((line = errorReader.readLine()) != null) {
                System.err.println("命令执行错误: " + line);
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("导出失败，命令执行失败，退出码: " + exitCode);
        }

        // 返回文件资源
        File file = new File(exportPath + File.separator + dbName + ".sql");
        return new FileSystemResource(file);
    }

    private void checkMysqldumpAvailability() {
        try {
            ProcessBuilder checkPB = new ProcessBuilder("cmd.exe", "/c", "where mysqldump");
            Process checkProcess = checkPB.start();
            int checkExitCode = checkProcess.waitFor();
            if (checkExitCode != 0) {
                throw new RuntimeException("未找到 mysqldump，检查 PATH 配置");
            }
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("检查 mysqldump 失败", e);
        }
    }


    // 导入SQL文件（代码不变）
    public void importDatabase(MultipartFile file) throws IOException {
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {

            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(file.getInputStream()));
            String line;
            StringBuilder currentStatement = new StringBuilder();

            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty() || line.startsWith("--")) {
                    continue;
                }
                currentStatement.append(line);
                if (line.endsWith(";")) {
                    String statement = currentStatement.toString().trim();
                    if (!statement.isEmpty()) {
                        stmt.execute(statement);
                    }
                    currentStatement.setLength(0);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("导入失败", e);
        }
    }
}
package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.domain.request.StationPointScaleRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.StationPointScaleMapper;
import cn.dhbin.isme.ims.service.StationPointScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 时空范围点位表（单点）(StationPointScale)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:40:58
 */
@Service("stationPointScaleService")
public class StationPointScaleServiceImpl extends ServiceImpl<StationPointScaleMapper, StationPointScale> implements StationPointScaleService {

    @Autowired
    private StationPointScaleMapper stationPointScaleMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<StationPointScale> queryPage(StationPointScaleRequest request) {
        IPage<StationPointScale> qp = request.toPage();
        LambdaQueryWrapper<StationPointScale> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(StationPointScale::getName, request.getName());
        }

        IPage<StationPointScale> ret = stationPointScaleMapper.selectPage(qp, queryWrapper);

        return Page.convert(ret);
    }

    @Override
    public void removeById(Integer id) {
        // 检查是否存在关联的 StationPointDistribute 记录
        LambdaQueryWrapper<StationPointDistribute> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StationPointDistribute::getScaleId, id);
        long count = stationPointDistributeMapper.selectCount(wrapper);
        if (count > 0) {
            throw new BizException(BizResponseCode.ERR_11099,"该记录存在关联的站点数据，无法删除");
        }

        // 如果没有关联数据，执行删除
        super.removeById(id);
    }

    @Override
    public List<StationPointScale> queryList(StationPointScaleRequest request) {
        LambdaQueryWrapper<StationPointScale> queryWrapper = new LambdaQueryWrapper<>();
        if (request.getName() != null) {
            queryWrapper.like(StationPointScale::getName, request.getName());
        }
        return list(queryWrapper);
    }
}


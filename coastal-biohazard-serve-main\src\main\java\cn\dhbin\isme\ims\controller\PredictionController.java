package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.R;

import cn.dhbin.isme.ims.service.impl.DataLoaderService;
import cn.dhbin.isme.ims.service.impl.ModelPersistenceService;
import cn.dhbin.isme.ims.service.impl.RandomForestPredictorService;
import cn.dhbin.isme.ims.service.impl.RandomForestTrainerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import weka.classifiers.trees.RandomForest;
import weka.core.Attribute;
import weka.core.DenseInstance;
import weka.core.Instance;
import weka.core.Instances;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/predict")
public class PredictionController {

    @Autowired
    private DataLoaderService dataLoaderService;

    @Autowired
    private RandomForestTrainerService randomForestTrainerService;

    @Autowired
    private ModelPersistenceService modelPersistenceService;

    @Autowired
    private RandomForestPredictorService randomForestPredictorService;


    // 训练模型（文件上传）
//    @PostMapping("/train")
//    public Result trainModel(@RequestParam("file") MultipartFile file) {
//        try {
//            // 根据文件类型加载数据
//            String fileName = file.getOriginalFilename();
//            Instances data;
//            if (fileName != null && fileName.endsWith(".csv")) {
//                data = dataLoaderService.loadCsv(file);
//            } else if (fileName != null && fileName.endsWith(".arff")) {
//                data = dataLoaderService.loadArff(file);
//            } else {
//                return Result.fail("Unsupported file format. Please upload a CSV or ARFF file.");
//            }
//
//            // 训练模型
//            RandomForest model = randomForestTrainerService.trainModel(data);
//            modelPersistenceService.saveModel(model, "green_tide_risk_randomForest.model");
//
//            return Result.data("Model trained and saved successfully!");
//        } catch (Exception e) {
//            e.printStackTrace();
//            return Result.fail("Error during model training: " + e.getMessage());
//        }
//    }

    // 训练模型
    @PostMapping("/train")
    public R<?> trainModel() {
        try {
            String filePath="C:\\Users\\<USER>\\Desktop\\海洋生态灾害检测验证系统\\csv\\green_tide_risk.csv";
            Instances data = dataLoaderService.loadData(filePath);  // 加载数据
            RandomForest model = randomForestTrainerService.trainModel(data);  // 训练模型
            modelPersistenceService.saveModel(model, "randomForest.model");  // 保存模型
            return R.ok("Model trained and saved successfully!");
        } catch (Exception e) {
            e.printStackTrace();
            return R.ok("Error during model training: " + e.getMessage());
        }
    }

    // 使用模型进行预测(文件上传)
//    @PostMapping("/predict")
//    public Result predict(@RequestParam("file") MultipartFile file) {
//        try {
//            // 加载模型
//            RandomForest model = modelPersistenceService.loadModel("randomForest.model");
//
//            // 根据文件格式加载输入数据
//            String fileName = file.getOriginalFilename();
//            Instances data;
//            if (fileName != null && fileName.endsWith(".csv")) {
//                data = dataLoaderService.loadCsv(file);
//            } else if (fileName != null && fileName.endsWith(".arff")) {
//                data = dataLoaderService.loadArff(file);
//            } else {
//                return Result.fail("Unsupported file format. Please upload a CSV or ARFF file.");
//            }
//
//            // 进行预测
//            List<String> predictions = new ArrayList<>();
//            for (int i = 0; i < data.numInstances(); i++) {
//                double prediction = model.classifyInstance(data.instance(i));
//                predictions.add(data.classAttribute().value((int) prediction)); // 获取分类结果
//            }
//
//            return Result.data(predictions); // 返回预测结果
//        } catch (Exception e) {
//            e.printStackTrace();
//            return Result.fail("Error during prediction: " + e.getMessage());
//        }
//    }


// 使用模型进行预测
@PostMapping("/predict")
public R<?> predict() {
    try {
        // 预测数据的文件路径
        String filePath = "C:\\Users\\<USER>\\Desktop\\海洋生态灾害检测验证系统\\csv\\green_tide_risk_predict.csv";

        // 加载预测数据
        Instances data = dataLoaderService.loadCsv(filePath);

        // 检查是否包含类标签列
        if (data.classIndex() == -1 || data.numAttributes() != 5) {
            // 如果缺少类标签列或列数不足，添加一个默认的 risk_level 列，值为 "unknown"
            ArrayList<Attribute> attributes = new ArrayList<>(data.numAttributes());
            for (int i = 0; i < data.numAttributes(); i++) {
                attributes.add(data.attribute(i));
            }
            attributes.add(new Attribute("risk_level", Arrays.asList("low", "medium", "high", "unknown")));

            Instances newData = new Instances("Predictions", attributes, data.numInstances());
            newData.setClassIndex(newData.numAttributes() - 1); // 设置类索引为最后一列

            // 复制原始数据，并为 risk_level 设置默认值 "unknown"
            for (int i = 0; i < data.numInstances(); i++) {
                Instance instance = new DenseInstance(newData.numAttributes());
                for (int j = 0; j < data.numAttributes(); j++) {
                    instance.setValue(newData.attribute(j), data.instance(i).value(j));
                }
                instance.setValue(newData.attribute("risk_level"), "unknown");
                newData.add(instance);
            }
            data = newData;
        }

        // 加载训练好的模型
        RandomForest model = modelPersistenceService.loadModel("randomForest.model");

        // 预测结果列表
        List<String> predictions = new ArrayList<>();

        // 对每条实例进行预测
        for (int i = 0; i < data.numInstances(); i++) {
            Instance instance = data.instance(i);
            System.out.println("Instance " + (i + 1) + ": " + instance);  // 调试输出每个实例

            // 进行预测
            double predictionIndex = model.classifyInstance(instance);
            // 获取预测分类标签的名称
            String predictedRiskLevel = data.classAttribute().value((int) predictionIndex);
            predictions.add("第 " + (i + 1) + " 条记录的预测结果: " + predictedRiskLevel);
        }

        // 返回预测结果
        return R.ok(predictions);
    } catch (Exception e) {
        e.printStackTrace();
        return R.ok("预测过程中出错: " + e.getMessage());
    }
}




}

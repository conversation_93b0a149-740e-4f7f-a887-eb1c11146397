package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.converter.GenderConverter;
import cn.dhbin.isme.ims.domain.dto.GtsusysStaffManageDto;
import cn.dhbin.isme.ims.domain.dto.excel.GtsusysStaffManageExcelDto;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffManage;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffManageRequest;
import cn.dhbin.isme.ims.domain.request.ManageBatchInsertRequest;
import cn.dhbin.isme.ims.mapper.GtsusysStaffManageMapper;
import cn.dhbin.isme.ims.service.GtsusysStaffManageService;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/gtsusys-staff-manage")
@RequiredArgsConstructor
public class GtsusysStaffManageController {
    private final GtsusysStaffManageService gtsusysStaffManageService;

    private final GtsusysStaffManageMapper gtsusysStaffManageMapper;
    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<GtsusysStaffManageDto>> selectAll(GtsusysStaffManageRequest request) {
        Page<GtsusysStaffManageDto> ret = gtsusysStaffManageService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody GtsusysStaffManage data) {
        gtsusysStaffManageService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody GtsusysStaffManage data) {
        gtsusysStaffManageService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        gtsusysStaffManageService.removeById(id);
        return R.ok();
    }

    @PostMapping("/batch-insert")
    public R<String> batchInsert(@RequestBody ManageBatchInsertRequest request) {
        boolean result = gtsusysStaffManageService.batchInsert(request);
        if (result) {
            return R.ok("新增成功");
        } else {
            return R.ok("新增失败");
        }
    }

    @GetMapping("list")
    public R<List<GtsusysStaffManageDto>> selectList(GtsusysStaffManageRequest request) {
        List<GtsusysStaffManageDto> gtsusysStaffManageDtos = gtsusysStaffManageService.queryList(request);
        return R.ok(gtsusysStaffManageDtos);
    }

    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 使用DTO类，并注册转换器
            List<GtsusysStaffManageExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(GtsusysStaffManageExcelDto.class) // 使用DTO类
                    .registerConverter(new GenderConverter()) // 注册转换器
                    .sheet()
                    .doReadSync();

            // 转换DTO到实体类（如果需要）
            List<GtsusysStaffManage> entities = list.stream()
                    .map(dto -> {
                        GtsusysStaffManage entity = new GtsusysStaffManage();
                        entity.setName(dto.getName());
                        entity.setGender(dto.getGender());
                        entity.setIdCard(dto.getIdCard());
                        entity.setGroupId(gtsusysStaffManageMapper.getGroupId(dto.getGroupName()));
                        return entity;
                    })
                    .collect(Collectors.toList());

            boolean result = gtsusysStaffManageService.saveBatch(entities);
            return result ? R.ok("导入成功") : R.build(new BizException(BizResponseCode.ERR_11013));
        } catch (Exception e) {
            return R.build(new BizException(BizResponseCode.ERR_11013, e.getMessage()));
        }
    }

    @GetMapping("/export")
    public void exportExcel(GtsusysStaffManageRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<GtsusysStaffManageDto> list = gtsusysStaffManageService.queryList(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "一线作业人员数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK); // 添加这行

            // 写入Excel
            EasyExcel.write(out, GtsusysStaffManageExcelDto.class)
                    .registerConverter(new GenderConverter())
                    .autoCloseStream(true)
                    .sheet("人员数据")
                    .doWrite(list.stream().map(dto -> {
                        GtsusysStaffManageExcelDto excelDto = new GtsusysStaffManageExcelDto();
                        BeanUtils.copyProperties(dto, excelDto);
                        return excelDto;
                    }).collect(Collectors.toList()));

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }
}

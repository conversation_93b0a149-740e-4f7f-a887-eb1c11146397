package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.Sediment;
import cn.dhbin.isme.ims.domain.request.SedimentRequest;
import cn.dhbin.isme.ims.mapper.SedimentMapper;
import cn.dhbin.isme.ims.service.SedimentService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("sedimentService")
public class SedimentServiceImpl extends ServiceImpl<SedimentMapper, Sediment> implements SedimentService {

    @Autowired
    private SedimentMapper sedimentMapper;

    @Override
    public Page<Sediment> queryPage(SedimentRequest request) {
        IPage<Sediment> qp = request.toPage();
        LambdaQueryWrapper<Sediment> queryWrapper = new LambdaQueryWrapper<>();

        IPage<Sediment> ret = sedimentMapper.selectPage(qp, queryWrapper);


        return Page.convert(ret);
    }

    @Override
    public List<Sediment> getByStationId(Integer stationId) {
        // 由于沉积物表没有直接的站点ID关联，返回所有沉积物数据作为演示
        LambdaQueryWrapper<Sediment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Sediment::getId);
        return sedimentMapper.selectList(queryWrapper);
    }
}

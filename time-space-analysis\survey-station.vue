<!--------------------------------
-现场调查时空分析模块
-2.合理布设调查站位
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex;gap: 24px;">
        <!-- 导出/导入功能 -->
<!--        <NButton type="warning" @click="handleExport">-->
<!--          <i class="i-material-symbols:download mr-4 text-18" />-->
<!--          导出Excel-->
<!--        </NButton>-->

<!--        &lt;!&ndash; 新增导入按钮 &ndash;&gt;-->
<!--        <NUpload-->
<!--          :show-file-list="false"-->
<!--          :custom-request="handleImport"-->
<!--          accept=".xlsx,.xls"-->
<!--          :disabled="importLoading"-->
<!--        >-->
<!--          <NButton-->
<!--            type="success"-->
<!--            :loading="importLoading"-->
<!--            :disabled="importLoading"-->
<!--          >-->
<!--            <i class="i-material-symbols:upload mr-4 text-18" />-->
<!--            {{ importLoading ? "正在导入..." : "导入Excel" }}-->
<!--          </NButton>-->
<!--        </NUpload>-->
        <NButton type="primary" @click="add()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud
      ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="api.readDistribute"
    >
      <MeQueryItem label="站点名称" :label-width="70">
        <n-input v-model:value="queryItems.name" type="text" placeholder="请输入站点名称" clearable />
      </MeQueryItem>
      <MeQueryItem label="调查中心" :label-width="70">
        <n-select
          v-model:value="queryItems.scaleId" label-field="name" value-field="id" clearable filterable
          :options="stationOption" placeholder="请选择调查中心"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" :rules="rules" label-placement="left" label-align="left" :label-width="110"
        :model="modalForm" :disabled="modalAction === 'view'"
      >
        <n-form-item label="调查中心" path="scaleId">
          <n-select
            v-model:value="modalForm.scaleId" :disabled="modalAction === 'edit'" label-field="name" value-field="id" clearable filterable
            :options="stationOption" placeholder="请选择调查中心"
          />
        </n-form-item>
        <n-form-item label="任务编码" path="taskName">
          <n-input v-model:value="modalForm.taskName" :disabled="modalAction === 'edit'" placeholder="请输入任务编码" />
        </n-form-item>
        <n-form-item label="站点名称" path="name">
          <n-input v-model:value="modalForm.name" placeholder="请输入名称">
            <template #prefix>
              <!-- 动态显示前缀 -->
              <span style="color:rgb(112 112 112)">{{ dynamicPrefix }}</span>
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="经度" path="longitude">
          <n-input-number v-model:value="modalForm.longitude" style="width: 100%;" placeholder="请输入经度" />
        </n-form-item>
        <n-form-item label="纬度" path="latitude">
          <n-input-number v-model:value="modalForm.latitude" style="width: 100%;" placeholder="请输入纬度" />
        </n-form-item>
        <n-form-item label="关联活动" path="activeTypes">
          <!-- 注意这里的 path 改为 activeTypes -->
          <n-checkbox-group v-model:value="modalForm.activeTypes" :disabled="modalAction === 'edit'">
            <n-space vertical>
              <n-checkbox value="wp">
                水文气象采集活动 (W)
              </n-checkbox>
              <n-checkbox value="ci">
                化学样本采集活动 (C)
              </n-checkbox>
              <n-checkbox value="mb">
                成体生物量采集活动 (A)
              </n-checkbox>
              <n-checkbox value="mr">
                微观繁殖体采集活动 (M)
              </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </n-form-item>
        <!-- <n-form-item label="概述" path="description" >
          <n-input v-model:value="modalForm.description" />
        </n-form-item> -->

        <!-- <n-form-item label="作业时间范围" rule-path="range" path="range">
          <n-date-picker v-model:value="modalForm.range" type="datetimerange" clearable />
        </n-form-item> -->
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NTag } from 'naive-ui'
import { h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import api from './api'

const router = useRouter()
const route = useRoute()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const now = Date.now()
// let range = ref([now - 30 * 24 * 60 * 60 * 1000, now])

const rules = reactive({
  name: { required: true, message: '请输入站点名称', trigger: ['input', 'blur'] },
  longitude: { required: true, message: '请输入经度', type: 'number', trigger: ['input', 'blur'] },
  latitude: { required: true, message: '请输入纬度', type: 'number', trigger: ['input', 'blur'] },
  scaleId: { required: true, message: '请选择调查中心', type: 'number', trigger: ['change', 'blur'] },
  activeTypes: {
    type: 'array',
    required: true,
    validator: (rule, value) => Boolean(value && value.length > 0),
    message: '请至少选择一项关联活动',
    trigger: ['change'],
  },
})

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportStation({ name: queryItems.value.name })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `合理布设调查站位多源数据整合_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    $message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    $message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importScale(formData)

    if (code === 0) {
      $message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      $message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    $message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}

async function getStationOption() {
  const { data } = await api.getStationPoints()
  stationOption.value = data
}

onMounted(() => {
  if (route.query.name) {
    queryItems.value.name = route.query.name
  }
  $table.value?.handleSearch()
  getStationOption()
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '调查站点',
  initForm: {
    enable: true,
    range: [now - 30 * 24 * 60 * 60 * 1000, now],
    activeTypes: [],
  },
  doCreate: api.createDistribute,
  doDelete: api.deleteDistribute,
  doUpdate: api.updateDistribute,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '调查中心',
    key: 'groupName',
    render: ({ stationPointScale }) =>
      h(NTag, { type: 'primary' }, { default: () => stationPointScale.name },
      ),
  },
  { title: '任务编号', key: 'taskName', ellipsis: { tooltip: true } },
  { title: '站点名称', key: 'name', ellipsis: { tooltip: true } },
  { title: '经度', key: 'longitude', ellipsis: { tooltip: true } },
  { title: '纬度', key: 'latitude', ellipsis: { tooltip: true } },
  {
    width: 350,
    title: '关联活动',
    key: 'activities',
    render(row) {
      const tags = []

      // 水文气象
      if (row.wpActivities) {
        tags.push(
          h(NTag, {
            type: 'info',
            size: 'small',
            style: { cursor: 'pointer' }, // 新增样式
            onClick: () => {
              router.push({
                path: '/ims/water-environmental',
                query: { distributeId: row.id },
              })
            },
          }, '水文气象'),
        )
      }

      // 化学样本
      if (row.ciActivities) {
        tags.push(
          h(NTag, {
            type: 'success',
            size: 'small',
            style: { cursor: 'pointer' }, // 新增样式
            onClick: () => {
              router.push({
                path: '/ims/seawater-chemistry',
                query: { distributeId: row.id },
              })
            },
          }, '化学样本'),
        )
      }

      // 藻成体生物量
      if (row.mbActivities) {
        tags.push(
          h(NTag, {
            type: 'warning',
            size: 'small',
            style: { cursor: 'pointer' }, // 新增样式
            onClick: () => {
              router.push({
                path: '/ims/sample-of-algae',
                query: { distributeId: row.id },
              })
            },
          }, '藻成体生物量'),
        )
      }

      // 微观繁殖体
      if (row.mrActivities) {
        tags.push(
          h(NTag, {
            type: 'error',
            size: 'small',
            style: { cursor: 'pointer' }, // 新增样式
            onClick: () => {
              router.push({
                path: '/ims/surface-water-sample-record',
                query: { distributeId: row.id },
              })
            },
          }, '微观繁殖体'),
        )
      }

      return h('div', { class: 'flex gap-8' }, tags)
    },
  },
  {
    width: 260,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            dashed: true,
            // secondary: true,
            onClick: () =>
              router.push({ path: `/ims/survey-time-range`, query: { distributeId: row.id, distributeName: row.name } }),
          },
          {
            default: () => '作业时间管理',
            icon: () => h('i', { class: 'i-fe:trello text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleOpenUpdate(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function generatePrefix(types) {
  if (!types || types.length === 0)
    return ''

  const prefixMap = {
    wp: 'W',
    ci: 'C',
    mb: 'A',
    mr: 'M',
  }

  // 安全处理排序
  return [...types]
    .sort((a, b) => {
      const order = Object.keys(prefixMap)
      return order.indexOf(a) - order.indexOf(b)
    })
    .map(type => prefixMap[type] || '')
    .join('')
}

// 计算属性：动态生成前缀
const dynamicPrefix = computed(() => {
  return generatePrefix(modalForm.value?.activeTypes || [])
})

function add() {
  handleOpen({
    action: 'add',
    title: '新增记录',
    onOk: async () => {
      await modalFormRef.value?.validate()

      // 生成前缀并拼接名称
      const prefix = generatePrefix(modalForm.value.activeTypes)
      const fullName = prefix + modalForm.value.name

      await api.createDistribute({
        taskName: modalForm.value.taskName,
        name: fullName, // 使用拼接后的名称
        scaleId: modalForm.value.scaleId,
        longitude: modalForm.value.longitude,
        latitude: modalForm.value.latitude,
        wpActivities: modalForm.value.activeTypes.includes('wp'),
        ciActivities: modalForm.value.activeTypes.includes('ci'),
        mbActivities: modalForm.value.activeTypes.includes('mb'),
        mrActivities: modalForm.value.activeTypes.includes('mr'),
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}
function removeOldPrefix(name) {
  return name.replace(/^[WCAM]+/, '')
}

function handleOpenUpdate(row) {
  const activeTypes = []
  if (row.wpActivities === true)
    activeTypes.push('wp')
  if (row.ciActivities === true)
    activeTypes.push('ci')
  if (row.mbActivities === true)
    activeTypes.push('mb')
  if (row.mrActivities === true)
    activeTypes.push('mr')

  // 移除旧前缀显示原始名称
  const originalName = removeOldPrefix(row.name)

  handleOpen({
    action: 'edit',
    title: '更新记录',
    row: {
      ...row,
      name: originalName, // 显示不带前缀的名称
      activeTypes,
    },
    onOk: updateM,
  })
}

async function updateM() {
  await modalFormRef.value?.validate()

  // 生成前缀并拼接名称
  const prefix = generatePrefix(modalForm.value.activeTypes)
  const fullName = prefix + modalForm.value.name

  await api.updateDistribute({
    id: modalForm.value.id,
    taskName: modalForm.value.taskName,
    name: fullName, // 使用拼接后的名称
    scaleId: modalForm.value.scaleId,
    longitude: modalForm.value.longitude,
    latitude: modalForm.value.latitude,
    wpActivities: modalForm.value.activeTypes.includes('wp'),
    ciActivities: modalForm.value.activeTypes.includes('ci'),
    mbActivities: modalForm.value.activeTypes.includes('mb'),
    mrActivities: modalForm.value.activeTypes.includes('mr'),
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}
</script>

<style lang="scss" scoped></style>

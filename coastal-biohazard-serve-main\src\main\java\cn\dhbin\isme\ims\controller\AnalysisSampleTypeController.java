package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.entity.AnalysisSampleType;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import cn.dhbin.isme.ims.service.AnalysisSampleTypeService;
import cn.dhbin.isme.ims.service.SampleTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/analysis-sample-type")
@RequiredArgsConstructor
public class AnalysisSampleTypeController {
    private final AnalysisSampleTypeService analysisSampleTypeService;

    @GetMapping("/list")
    public R<List<AnalysisSampleType>> AnalysislistSampleTypes() {
        List<AnalysisSampleType> stationPoints = analysisSampleTypeService.AnalysislistSampleTypes();
        return R<PERSON>ok(stationPoints);
    }

}

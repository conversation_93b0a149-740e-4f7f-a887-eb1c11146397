package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WaterPhWeatherDataRequest extends PageRequest {

    private Integer distributeId; // 站点


    private String sampleLayer; // 采样层次

    
public Serializable pkVal() {
          return null;
      }
}



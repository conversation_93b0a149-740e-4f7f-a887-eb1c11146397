package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 一线人员单位表(GtsusysStaffGroup)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:34:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GtsusysStaffGroupRequest extends PageRequest {

    /**
     * 单位名称
     **/
    private String name;
    
public Serializable pkVal() {
          return null;
      }
}



import { request } from '@/utils'

export default {

  getOrganizedData: (scaleId) => request.get(`/charts/data/organized?scaleId=${scaleId}`),

  getStationPoints: () => request.get('/station-point-scale/list'),

  getManageList:((params = {})=>request.get("/gtsusys-staff-manage/list", { params })),

  getListStationPoints:(scaleId)=>request.get(`/station-point-distribute/list?scaleId=${scaleId}`),

  getAbundanceList:(distributeId)=>request.get(`/abundance/list?distributeId=${distributeId}`),

  getSurveyTimes:(scaleId)=>request.get(`/charts/data/surveyTimes?scaleId=${scaleId}`),

  getBiodiversityList:(distributeId)=>request.get(`/biodiversity/list?distributeId=${distributeId}`),
}
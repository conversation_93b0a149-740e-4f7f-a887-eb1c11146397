package cn.dhbin.isme.ims.controller;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.service.impl.DatabaseService;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

@RestController
@RequestMapping("/database")
public class DatabaseController {

    @jakarta.annotation.Resource
    private DatabaseService databaseService;

    // 导出数据库接口
    @GetMapping("/export/{dbName}")
    public ResponseEntity<Resource> exportDatabase(@PathVariable String dbName)
            throws IOException, InterruptedException {
        Resource resource = databaseService.exportDatabase(dbName);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + new File(resource.getURI()).getName() + "\"")
                .body(resource);
    }

    // 导入数据库接口
    @PostMapping("/import")
    public R<String> importDatabase(@RequestPart("file") MultipartFile file)
            throws IOException {
        databaseService.importDatabase(file);
        return R.ok("导入成功");
    }
}
package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预测数据模拟表(AlgaeDetectionRecords)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:30:56
 */
@Data
@TableName("algae_detection_records")
public class AlgaeDetectionRecords implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    private Date date;
    
    /**
     * 地理位置
     **/
    private String location;
    
    /**
     * 浒苔密度
     **/
    private BigDecimal algaeDensity;
    
    /**
     * 水温
     **/
    private BigDecimal waterTemperature;
    
    /**
     * 盐度
     **/
    private BigDecimal salinity;
    
    /**
     * pH值
     **/
    private BigDecimal phValue;
    
    /**
     * 风速
     **/
    private BigDecimal windSpeed;
    
    /**
     * 降雨量
     **/
    private BigDecimal rainfall;
    
    /**
     * 光照强度
     **/
    private BigDecimal lightIntensity;
    
    private String riskLevel;
    
public Serializable pkVal() {
          return null;
      }
}



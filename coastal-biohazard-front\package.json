{"name": "vue-naive-admin", "type": "module", "version": "2.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:fix": "eslint --fix", "postinstall": "npx", "up": "taze major -I"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@arco-design/color": "^0.4.0", "@kjgl77/datav-vue3": "^1.7.3", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "^11.0.3", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "event-source-polyfill": "^1.0.31", "lodash-es": "^4.17.21", "naive-ui": "^2.39.0", "ol": "^10.2.1", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^4.0.0", "vue": "^3.5.3", "vue-echarts": "^7.0.3", "vue-router": "^4.4.3", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^3.3.2", "@iconify/json": "^2.2.245", "@unocss/eslint-config": "^0.62.3", "@unocss/eslint-plugin": "^0.62.3", "@unocss/preset-rem-to-px": "^0.62.3", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "eslint": "^9.9.1", "eslint-plugin-format": "^0.1.2", "esno": "^4.7.0", "fs-extra": "^11.2.0", "lint-staged": "^15.2.10", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.78.0", "simple-git-hooks": "^2.11.1", "taze": "^0.16.7", "unocss": "^0.62.3", "unplugin-auto-import": "^0.18.2", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.3", "vite-plugin-router-warn": "^1.0.0", "vite-plugin-vue-devtools": "^7.4.4"}}
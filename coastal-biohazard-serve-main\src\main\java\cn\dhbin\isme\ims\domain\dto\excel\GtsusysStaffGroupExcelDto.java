/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025-04-14 13:23:07
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-14 13:25:56
 * @Description: 请填写简介
 */
package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 一线人员单位表 Excel 导入导出DTO
 */
@Data
public class GtsusysStaffGroupExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("单位名称")
    @ColumnWidth(30)
    private String name;
} 
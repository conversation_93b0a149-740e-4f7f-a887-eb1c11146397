package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.util.UploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;
import java.util.UUID;


/**
 * 上传接口
 */
@RestController
@Slf4j
@RequestMapping("/upload")
public class UploadController {

    @Autowired
    private UploadUtil uploadUtil;

    @Value("${crawler.javaOCR.IMAGE_DIR}")
    private String IMAGE_DIR; // 验证码识别结果保存路径

    /**
     * oss图片上传
     * @param file
     * @return
     * @throws IOException
     */
//    @PostMapping("/img")
//    public R<?> uploadImg(MultipartFile file) throws IOException {
//        String s = uploadUtil.uploadImage(file);
//        return R.ok(s);
//    }

    /**
     * 本地图片上传
     * @param file 图片文件
     * @return 图片访问名称
     */
    @PostMapping("/img")
    public R<?> uploadImg(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11010));
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = Objects.requireNonNull(originalFilename)
                    .substring(originalFilename.lastIndexOf("."));
            String uniqueFileName = UUID.randomUUID() + fileExtension;

            // 创建存储目录（如果不存在）
            Path uploadPath = Paths.get(IMAGE_DIR);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // 保存文件到本地
            Path filePath = uploadPath.resolve(uniqueFileName);
            file.transferTo(filePath);

            return R.ok(uniqueFileName);

        } catch (IOException e) {
            log.error("文件上传失败", e);
            return R.build(new BizException(BizResponseCode.ERR_11011));
        } catch (Exception e) {
            log.error("系统异常", e);
            return R.build(new BizException(BizResponseCode.ERR_11099));
        }
    }


    @GetMapping("/getImage")
    public ResponseEntity<Resource> getCaptchaImage(@RequestParam String imageName) {
        try {
            // 安全校验：防止路径遍历攻击
            if (!imageName.matches("^[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9]{1,5})?$")) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            Path imagePath = Paths.get(IMAGE_DIR + imageName);

            if (!Files.exists(imagePath)) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(imagePath);

            // 根据文件扩展名设置Content-Type
            String contentType = Files.probeContentType(imagePath);
            MediaType mediaType = MediaType.parseMediaType(contentType);

            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .cacheControl(CacheControl.noCache())
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .body(resource);

        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }


}

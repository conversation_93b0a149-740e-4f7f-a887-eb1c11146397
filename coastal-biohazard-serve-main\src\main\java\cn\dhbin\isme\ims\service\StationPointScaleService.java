package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import cn.dhbin.isme.ims.domain.request.StationPointScaleRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 时空范围点位表（单点）(StationPointScale)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:40:58
 */
public interface StationPointScaleService extends IService<StationPointScale> {
    Page<StationPointScale> queryPage(StationPointScaleRequest request);

    void removeById(Integer id);

    List<StationPointScale> queryList(StationPointScaleRequest request);
}


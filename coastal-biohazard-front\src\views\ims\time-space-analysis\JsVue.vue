<template>
  <CommonPage>
    <!-- 专业大屏覆盖层 -->
    <div v-if="showProfessionalDashboard" class="professional-dashboard-overlay">
      <ProfessionalDashboard 
        :stationInfo="{
          name: selectedStation?.name || 'QDW',
          longitude: selectedStation?.longitude || 119.366892,
          latitude: selectedStation?.latitude || 34.760023,
          id: selectedStation?.id || 12
        }"
        :query="{
          distributeId: selectedStation?.id || 12,
          stationName: selectedStation?.name || 'QDW',
          scaleId: selectedInvestigationCenter,
          routeId: selectedRoute,
          timesId: selectedTime
        }"
        @close="closeProfessionalDashboard"
      />
    </div>

    <template #title-suffix>
      <div
        class="ml-16 flex cursor-pointer items-center text-16 opacity-60 transition-all-300 hover:opacity-40"
        @click="router.back()"
      >
        <i class="i-material-symbols:arrow-left-alt" />
        <span class="ml-4">返回</span>
      </div>
    </template>
    <template #action>
      <!-- 级联选择器 -->
      <div class="filter-container" style="display:flex;width: 50%;gap: 16px">
        <NSelect
          v-model:value="selectedInvestigationCenter"
          :options="investigationCenters.map(c => ({ label: c.name, value: c.id }))"
          placeholder="选择调查中心"
          @update:value="() => selectedRoute = null"
        />
        <NSelect
          v-model:value="selectedRoute"
          :options="routes.map(r => ({ label: r.name, value: r.id }))"
          placeholder="选择航线"
          :disabled="!selectedInvestigationCenter"
          @update:value="() => selectedTime = null"
        />
        <NSelect
          v-model:value="selectedTime"
          :options="times.map(t => ({ label: `第${t.times}次调查 ${t.date}`, value: t.id }))"
          placeholder="选择调查次数"
          :disabled="!selectedRoute"
        />
        <NButton type="primary" @click="applyFilters">
          筛选
        </NButton>
        <NButton type="info" ghost @click="restartGuide">
          🎯 功能引导
        </NButton>
        <NButton 
          type="success" 
          ghost 
          @click="adjustMapView"
          :disabled="!stationPointDistribute || stationPointDistribute.length === 0"
          title="重新定位地图到最佳视野"
        >
          🗺️ 定位视野
        </NButton>
      </div>

      <NModal v-model:show="showPredictionModal" preset="dialog" title="预测所选位置水文信息">
        <div style="display: flex; flex-direction: column; gap: 10px;">
          <NAlert type="warning" closable>
            <div style="display: flex; flex-direction: column; gap: 10px;">
              <span>经度范围: {{ longitudeRange.min }} 到 {{ longitudeRange.max }}</span>
              <span>纬度范围: {{ latitudeRange.min }} 到 {{ latitudeRange.max }}</span>
            </div>
          </NAlert>
          <NInputNumber v-model:value="longitude" placeholder="经度" :min="longitudeRange.min" :max="longitudeRange.max" />
          <NInputNumber v-model:value="latitude" placeholder="纬度" :min="latitudeRange.min" :max="latitudeRange.max" />
        </div>
        <template #action>
          <NButton type="primary" :disabled="!isValidInput" @click="predictWaterInfo">
            确认
          </NButton>
        </template>
      </NModal>
      <NModal
        v-model:show="showResultModal"
        style="width: 800px"
        preset="dialog"
        title="预测结果分析"
        :mask-closable="false"
      >
        <div class="flex flex-col gap-4">
          <!-- 图表部分 -->
          <div ref="chartRef" style="width: 100%; height: 400px" />
          <!-- 分析结果 -->
          <NCard title="AI实时分析预测结果">
            <div class="h-[100%]">
              <!-- 加载状态 -->
              <div
                v-if="isLoading"
                class="absolute inset-0 flex items-center justify-center bg-gray-50/50"
              >
                <n-spin size="small">
                  <template #description>
                    <span style="font-size: 14px" class="text-gray-600">AI正在分析中，请稍候...</span>
                  </template>
                </n-spin>
              </div>

              <!-- 内容容器 -->
              <div
                ref="analysisRef"
                class="analysis-container whitespace-pre-wrap text-gray-800 leading-relaxed"
                :class="{ 'opacity-50': isLoading }"
              />
            </div>
          </NCard>
        </div>

        <template #action>
          <NButton
            type="primary"
            :loading="isLoading"
            @click="closeResultModal"
          >
            {{ isLoading ? '分析中...' : '关闭' }}
          </NButton>
        </template>
      </NModal>
    </template>

    <div id="map-container" />

    <!-- 用户指导系统 -->
    <NModal v-model:show="showGuideModal" style="width: 600px" preset="dialog" title="欢迎使用站点地图分布系统">
      <div class="guide-content">
        <div class="guide-step">
          <div class="guide-icon">🗺️</div>
          <div class="guide-text">
            <h3>探索海洋监测站点</h3>
            <p>在地图上，您可以看到多个监测站点标记。每个站点都收集了丰富的海洋环境数据。</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="guide-icon">🔍</div>
          <div class="guide-text">
            <h3>筛选特定数据</h3>
            <p>使用页面顶部的筛选器选择调查中心、航线和调查次数，查看特定条件下的站点分布。</p>
          </div>
        </div>
        <div class="guide-step">
          <div class="guide-icon">📊</div>
          <div class="guide-text">
            <h3>查看详细数据</h3>
            <p><strong>点击任意站点标记</strong>，即可查看该站点的详细监测数据，包括水文气象、化学成分、生物多样性等信息。</p>
          </div>
        </div>
        <div class="guide-highlight">
          <p>💡 <strong>提示：</strong>建议先选择"海岸带调查1"查看演示数据，然后点击"QDW"站点体验完整功能！</p>
          <p>🎯 <strong>QDW站点包含：</strong>水文气象、化学离子、金属离子、生物多样性、形态分析、沉积物、AI水质预测等9大类完整数据。</p>
          <p>🔄 <strong>随时可用：</strong>您可以随时点击页面右上角的"🎯 功能引导"按钮重新启动引导。</p>
        </div>
      </div>
      <template #action>
        <div class="guide-actions">
          <NButton @click="closeGuide">
            跳过引导
          </NButton>
          <NButton type="primary" @click="startGuide">
            开始探索
          </NButton>
        </div>
      </template>
    </NModal>

    <!-- 引导高亮遮罩 -->
    <div v-if="showGuideHighlight" class="guide-overlay">
      <div class="guide-mask" @click="nextGuideStep"></div>
      <div class="guide-tooltip" :style="guideTooltipStyle">
        <div class="guide-tooltip-content">
          <h4>{{ currentGuideStep.title }}</h4>
          <p>{{ currentGuideStep.description }}</p>
          <div class="guide-tooltip-actions">
            <NButton size="small" @click="skipGuide">
              跳过
            </NButton>
            <NButton size="small" type="primary" @click="nextGuideStep">
              {{ currentGuideStep.buttonText }}
            </NButton>
          </div>
        </div>
        <div class="guide-tooltip-arrow"></div>
      </div>
    </div>

    <NDrawer
      v-model:show="DrawerVisible"
      width="600"
      placement="right"
      class="bg-gray-50"
    >
      <NDrawerContent title="站点详情" closable>
        <template #header>
          <span>站点详情</span>
          <NButton style="margin-left: 15px" tertiary type="primary" @click="DrawerVisibleHome = true">
            点击按月份查询
          </NButton>
          <NButton style="margin-left: 15px" tertiary type="info" @click="DrawerVisibleDataView = true">
            点击查看数据大屏
          </NButton>
          <NButton style="margin-left: 15px" tertiary type="success" @click="openProfessionalDashboard">
            🚀 切换专业大屏
          </NButton>
        </template>
        <!-- 整体容器：上下垂直分布，留白和挤出 -->
        <div class="flex flex-col p-4 space-y-6">
          <!-- 基础信息卡片 -->
          <div class="rounded-xl bg-white p-14 shadow">
            <h4 class="mb-2 border-b border-gray-200 pb-1 font-semibold">
              站点信息
            </h4>
            <div class="grid grid-cols-2 mt-6 gap-2 text-12px text-gray-700">
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  名称：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.name || '--' }}
                </p>
              </div>
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  经度：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.longitude.toFixed(6) }}
                </p>
              </div>
              <div class="flex">
                <p class="text-gray-600 font-medium">
                  纬度：
                </p>
                <p class="text-gray-800">
                  {{ selectedStation?.latitude.toFixed(6) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 活动关联状态卡片 -->
          <div class="rounded-xl bg-white p-14 shadow">
            <h4 class="mb-2 border-b border-gray-200 pb-1 font-semibold">
              活动关联状态
            </h4>
            <div class="mt-6 flex flex-wrap gap-3">
              <NTag
                :type="wpActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goW"
              >
                水文气象采集：{{ wpActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="ciActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goC"
              >
                化学样本采集：{{ ciActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="mbActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goA"
              >
                成体生物量采集：{{ mbActivities ? '已关联' : '未关联' }}
              </NTag>
              <NTag
                :type="mrActivities ? 'success' : 'default'"
                style="cursor: pointer"
                class="text-10px" @click="goM"
              >
                微观繁殖体采集：{{ mrActivities ? '已关联' : '未关联' }}
              </NTag>
            </div>
            <!--          </div> -->

            <!-- 数据模块：每个子模块单独卡片 -->
            <div class="space-y-4">
              <!-- 水文气象数据 -->
              <div class="mt-10 flex flex-col gap-10px rounded-xl bg-white p-8 shadow">
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    水文气象数据
                  </h4>
                  <NTag type="warning">
                    {{ weatherData[0]?.beforeInvestigate ? formatDateTime(weatherData[0].beforeInvestigate) : '--' }} - {{ weatherData[0]?.afterInvestigate ? formatDateTime(weatherData[0].afterInvestigate) : '--' }}
                  </NTag>
                </div>
                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(weatherData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(weatherData[0], 1)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible1" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="getImageUrl(file)"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openMedia(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible1 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="weatherColumns"
                  :data="weatherData"
                  stripe
                  bordered
                />

                <!-- 化学离子数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    化学离子数据
                  </h4>
                  <NTag type="warning">
                    {{ chemicalData[0]?.beforeInvestigate ? formatDateTime(chemicalData[0].beforeInvestigate) : '--' }} - {{ chemicalData[0]?.afterInvestigate ? formatDateTime(chemicalData[0].afterInvestigate) : '--' }}
                  </NTag>
                </div>

                <NButton
                  v-if="hasEvidence(chemicalData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(chemicalData[0], 2)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible2" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="getImageUrl(file)"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openMedia(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible2 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="chemicalColumns"
                  :data="chemicalData"
                  stripe
                  bordered
                />

                <!-- 成体生物量数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    成体生物量数据
                  </h4>
                  <NTag type="warning">
                    {{ abundanceData[0]?.beforeInvestigate ? formatDateTime(abundanceData[0].beforeInvestigate) : '--' }} - {{ abundanceData[0]?.afterInvestigate ? formatDateTime(abundanceData[0].afterInvestigate) : '--' }}
                  </NTag>
                </div>

                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(abundanceData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(abundanceData[0], 3)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible3" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="getImageUrl(file)"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openMedia(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible3 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="abundanceColumns"
                  :data="abundanceData"
                  stripe
                  bordered
                />

                <!-- 微观繁殖体数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    微观繁殖体数据
                  </h4>
                  <NTag type="warning">
                    {{ microData[0]?.beforeInvestigate ? formatDateTime(microData[0].beforeInvestigate) : '--' }} - {{ microData[0]?.afterInvestigate ? formatDateTime(microData[0].afterInvestigate) : '--' }}
                  </NTag>
                </div>

                <!-- 查看佐证材料按钮 -->
                <NButton
                  v-if="hasEvidence(microData[0])"
                  type="primary"
                  style="margin-bottom: 16px;"
                  @click="showEvidenceModal(microData[0], 3)"
                >
                  查看佐证材料
                </NButton>

                <!-- 佐证材料抽屉 -->
                <NDrawer v-model:show="evidenceModalVisible4" placement="right" width="800">
                  <NDrawerContent title="佐证材料">
                    <div class="p-4">
                      <div v-if="selectedEvidenceFiles.length === 0">
                        <NEmpty description="无佐证材料" />
                      </div>
                      <div v-else class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <!-- 最多显示3个文件 -->
                        <div v-for="(file, index) in selectedEvidenceFiles.slice(0, 3)" :key="index">
                          <NCard>
                            <div class="aspect-video flex items-center justify-center bg-gray-100">
                              <!-- 图片直接展示 -->
                              <template v-if="isImage(file)">
                                <img
                                  :src="getImageUrl(file)"
                                  alt="预览图片"
                                  style="width: 100%; max-height: 300px; object-fit: contain;"
                                >
                              </template>
                              <!-- 视频展示播放按钮 -->
                              <template v-else>
                                <NButton type="primary" @click="openMedia(file)">
                                  播放视频
                                </NButton>
                              </template>
                            </div>
                          </NCard>
                        </div>
                      </div>
                      <div class="mt-4 flex justify-end">
                        <NButton @click="evidenceModalVisible4 = false">
                          关闭
                        </NButton>
                      </div>
                    </div>
                  </NDrawerContent>
                </NDrawer>

                <NDataTable
                  :columns="microColumns"
                  :data="microData"
                  stripe
                  bordered
                />

                <!-- 金属离子数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    金属离子检测数据
                  </h4>
                  <NTag type="warning">
                    重金属污染监测
                  </NTag>
              </div>

                <NDataTable
                  :columns="metalIonColumns"
                  :data="metalIonData"
                  stripe
                  bordered
                />

                <!-- 生物多样性数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    生物多样性分析数据
                  </h4>
                  <NTag type="info">
                    生态群落结构分析
                  </NTag>
            </div>

                <NDataTable
                  :columns="biodiversityColumns"
                  :data="biodiversityData"
                  stripe
                  bordered
                />

                <!-- 形态分析数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    显微形态分析数据
                  </h4>
                  <NTag type="primary">
                    显微镜图像分析
                  </NTag>
                </div>

                <NDataTable
                  :columns="morphologicalColumns"
                  :data="morphologicalData"
                  stripe
                  bordered
                />

                <!-- 沉积物数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    沉积物分析数据
                  </h4>
                  <NTag type="warning">
                    底质环境监测
                  </NTag>
                </div>

                <NDataTable
                  :columns="sedimentColumns"
                  :data="sedimentData"
                  stripe
                  bordered
                />

                <!-- 水质预测数据 -->
                <div class="mb-3 flex items-center justify-between">
                  <h4 class="font-semibold">
                    AI水质预测数据
                  </h4>
                  <NTag type="success">
                    智能预测分析
                  </NTag>
                </div>

                <NDataTable
                  :columns="waterQualityColumns"
                  :data="waterQualityPredictionData"
                  stripe
                  bordered
                />

                <!-- AI分析结果查看按钮 -->
                <div v-if="waterQualityPredictionData.length > 0" class="mt-4 text-center">
                  <NButton 
                    type="primary" 
                    size="large"
                    @click="showAIAnalysisModal = true"
                    class="px-8 py-2"
                  >
                    🤖 查看AI分析结果 ({{ waterQualityPredictionData.length }}条)
                  </NButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
    <NDrawer
      v-model:show="DrawerVisibleHome"
      width="650"
      placement="left"
      class="bg-gray-50"
    >
      <HomeEcharts :scale-id="selectedInvestigationCenter" />
    </NDrawer>
    <NDrawer
      v-model:show="DrawerVisibleDataView"
      :height="mainHeight - 60"
      placement="bottom"
      class="bg-gray-50"
    >
      <div style="position: relative; width: 100%; height: 100%;">
        <!-- 数据大屏切换专业大屏按钮 -->
        <div style="position: absolute; top: 15px; right: 25px; z-index: 1000;">
          <NButton 
            type="primary" 
            size="large"
            @click="openProfessionalDashboard"
            style="background: linear-gradient(45deg, #72ACD1, #4a90e2); border: none; box-shadow: 0 4px 12px rgba(114, 172, 209, 0.4); font-weight: bold;"
            :disabled="!selectedStation || !selectedStation.id"
          >
            🚀 切换专业大屏
          </NButton>
        </div>
        <DataView :scale-id="selectedInvestigationCenter" :task-id="selectedRoute" :times-id="selectedTime" :distribute-id="selectedStation.id" />
      </div>
    </NDrawer>
    <NModal
      v-model:show="modalVisible"
      draggable="true"
      preset="dialog"
      :title="`${type === 1 ? '新增' : '修改'}站点信息`"
      :show-icon="false"
      positive-text="确认"
      negative-text="取消"
      @positive-click="handleSubmit"
    >
      <NForm
        ref="modalFormRef"
        :rules="rules"
        label-placement="left"
        label-align="left"
        :label-width="110"
        :model="modalForm"
      >
        <NFormItem label="调查中心" path="scaleId">
          <n-select
            v-model:value="modalForm.scaleId" label-field="name" value-field="id" clearable filterable
            :options="stationOption" placeholder="请选择调查中心"
          />
        </NFormItem>
        <NFormItem label="任务编码" path="taskName">
          <NInput v-model:value="modalForm.taskName" placeholder="请输入任务编码" />
        </NFormItem>
        <NFormItem label="站点名称" path="name">
          <NInput v-model:value="modalForm.name" placeholder="请输入名称">
            <template #prefix>
              <!-- 动态显示前缀 -->
              <span style="color:rgb(112 112 112)">{{ dynamicPrefix }}</span>
            </template>
          </NInput>
        </NFormItem>
        <NFormItem label="经度" path="longitude">
          <NInputNumber
            v-model:value="modalForm.longitude"
            style="width: 100%;"
            placeholder="经度"
          />
        </NFormItem>
        <NFormItem label="纬度" path="latitude">
          <NInputNumber
            v-model:value="modalForm.latitude"
            style="width: 100%;"
            placeholder="纬度"
          />
        </NFormItem>
        <NFormItem label="关联活动" path="activeTypes">
          <n-checkbox-group v-model:value="modalForm.activeTypes">
            <n-space vertical>
              <n-checkbox value="wp">
                水文气象采集活动 (W)
              </n-checkbox>
              <n-checkbox value="ci">
                化学样本采集活动 (C)
              </n-checkbox>
              <n-checkbox value="mb">
                成体生物量采集活动 (A)
              </n-checkbox>
              <n-checkbox value="mr">
                微观繁殖体采集活动 (M)
              </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </NFormItem>
      </NForm>
    </NModal>

    <!-- AI分析结果模态框 -->
    <NModal
      v-model:show="showAIAnalysisModal"
      preset="card"
      title="🤖 AI水质预测分析结果"
      :style="{ width: '90%', maxWidth: '1200px' }"
      :segmented="true"
      :closable="true"
      class="ai-analysis-modal"
    >
      <div class="ai-analysis-content">
        <div v-if="waterQualityPredictionData.length === 0" class="text-center py-8">
          <NEmpty description="暂无AI分析结果" />
        </div>
        <div v-else class="space-y-6">
          <div
            v-for="(prediction, index) in waterQualityPredictionData"
            :key="index"
            class="analysis-item p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm"
          >
            <!-- 标题栏 -->
            <div class="flex justify-between items-center mb-4 pb-3 border-b border-blue-200">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {{ index + 1 }}
                </div>
                <div>
                  <h3 class="text-xl font-semibold text-gray-800">
                    📅 预测时间
                  </h3>
                  <p class="text-lg text-gray-600">
                    {{ formatDateTime(prediction.predictionTime) }}
                  </p>
                </div>
              </div>
              <div class="text-right">
                <NTag 
                  :type="parseFloat(prediction.confidence || 0) > 80 ? 'success' : 'warning'"
                  size="large"
                  class="text-lg px-4 py-2"
                >
                  🎯 置信度: {{ prediction.confidence || '--' }}%
                </NTag>
                <div v-if="prediction.modelVersion" class="mt-2 text-sm text-gray-500">
                  🔬 模型版本: {{ prediction.modelVersion }}
                </div>
              </div>
            </div>

            <!-- 预测参数 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div class="bg-white p-3 rounded-lg border">
                <div class="text-sm text-gray-500">盐度</div>
                <div class="text-lg font-semibold text-blue-600">{{ prediction.salinity || '--' }} PSU</div>
              </div>
              <div class="bg-white p-3 rounded-lg border">
                <div class="text-sm text-gray-500">pH值</div>
                <div class="text-lg font-semibold text-green-600">{{ prediction.ph || '--' }}</div>
              </div>
              <div class="bg-white p-3 rounded-lg border">
                <div class="text-sm text-gray-500">水温</div>
                <div class="text-lg font-semibold text-orange-600">{{ prediction.temperature || '--' }}℃</div>
              </div>
              <div class="bg-white p-3 rounded-lg border">
                <div class="text-sm text-gray-500">透明度</div>
                <div class="text-lg font-semibold text-purple-600">{{ prediction.transparency || '--' }} m</div>
              </div>
            </div>

            <!-- AI分析内容 -->
            <div class="bg-white p-5 rounded-lg border-l-4 border-blue-500 shadow-sm">
              <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm mr-2">AI</span>
                智能分析结果
              </h4>
              <div class="text-base leading-relaxed text-gray-700 whitespace-pre-wrap font-medium">
                {{ prediction.aiAnalysis || '暂无分析结果' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="flex justify-end space-x-3">
          <NButton @click="exportAIAnalysis" type="info" size="medium">
            📄 导出分析报告
          </NButton>
          <NButton @click="showAIAnalysisModal = false" type="primary" size="medium">
            关闭
          </NButton>
        </div>
      </template>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { CommonPage } from '@/components/index.js'
import { useAuthStore } from '@/store/index.js'
import { formatDateTime } from '@/utils/index.js'

import HomeEcharts from '@/views/home/<USER>'
import DataView from '@/views/ims/data-view/index.vue'
import ProfessionalDashboard from '@/views/ims/professional-dashboard/index.vue'
import AMapLoader from '@amap/amap-jsapi-loader'
import * as echarts from 'echarts'
import { EventSourcePolyfill } from 'event-source-polyfill'
import { NAlert, NButton, NCard, NForm, NFormItem, NInput, NInputNumber, NModal, NTag, NDataTable, NEmpty, NSpin } from 'naive-ui'
import { computed, h, nextTick, onBeforeUnmount, onMounted, reactive, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import api from './api'

// 预测相关状态
const showPredictionModal = ref(false)
const showResultModal = ref(false)
const longitude = ref(null)
const latitude = ref(null)
const hasMarkers = ref(false)
const selectedStation = ref(null)
const resultData = ref(null)
const chartRef = ref(null)
const longitudeRange = ref({ min: null, max: null })
const latitudeRange = ref({ min: null, max: null })

// 站点管理相关状态
const route = useRoute()
const router = useRouter()
const map = ref(null)
const markers = ref([])
const polygon = ref(null)
const centerMarker = ref(null)
const stationPointScale = ref([])
const stationPointDistribute = ref([])
const mainPoint = ref([])
const modalFormRef = ref(null)
const modalVisible = ref(false)
const DrawerVisible = ref(false)
const DrawerVisibleHome = ref(false)
const DrawerVisibleDataView = ref(false)
const type = ref(1)
const originalLng = ref()
const originalLat = ref()

// 新增 DAG 相关状态
const directedEdges = ref([])
const routePaths = ref([])
const routeMarkers = ref([])
const isDrawingRoute = ref(false)
const routeStartPoint = ref(null)

const stationOption = ref([])

const modalForm = reactive({
  scaleId: null,
  name: '',
  longitude: null,
  latitude: null,
  id: null,
  activeTypes: null,
  taskId: null,
  taskName: null,
})

const evidenceModalVisible1 = ref(false)
const evidenceModalVisible2 = ref(false)
const evidenceModalVisible3 = ref(false)
const evidenceModalVisible4 = ref(false)
const selectedEvidenceFiles = ref([])
const currentRow = ref(null)

// AI分析结果模态框
const showAIAnalysisModal = ref(false)
const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)

// 专业大屏显示状态
const showProfessionalDashboard = ref(false)

// 判断是否有佐证材料
function hasEvidence(row) {
  if (!row)
    return
  if (!row.evidenceFiles)
    return false
  try {
    const files = JSON.parse(row.evidenceFiles)
    return Array.isArray(files) && files.length > 0
  }
  catch (error) {
    console.error('解析 evidenceFiles 失败:', error)
    return false
  }
}

// 获取解析后的文件列表
function getEvidenceFiles(row) {
  if (!row.evidenceFiles)
    return []
  try {
    const files = JSON.parse(row.evidenceFiles)
    return Array.isArray(files) ? files : []
  }
  catch (error) {
    console.error('解析 evidenceFiles 失败:', error)
    return []
  }
}

// 显示佐证材料抽屉
function showEvidenceModal(row, type) {
  currentRow.value = row
  selectedEvidenceFiles.value = getEvidenceFiles(row)
  switch (type) {
    case 1:
      evidenceModalVisible1.value = true
      break
    case 2:
      evidenceModalVisible2.value = true
      break
    case 3:
      evidenceModalVisible3.value = true
      break
    case 4:
      evidenceModalVisible4.value = true
      break
    default:
      selectedEvidenceFiles.value = []
  }
}

/**
 * 智能图片URL处理
 * 自动判断是本地文件还是远程URL，并返回正确的访问路径
 */
const getImageUrl = (imageName) => {
  if (!imageName || imageName === '--') return getPlaceholderImage()
  
  // 如果是完整的URL（以http开头），直接返回
  if (imageName.startsWith('http://') || imageName.startsWith('https://')) {
    return imageName
  }
  
  // 否则认为是本地文件名，通过upload API加载
  return `${baseUrl.value}/upload/getImage?imageName=${imageName}`
}

/**
 * 获取占位符图片
 */
const getPlaceholderImage = () => {
  // 使用base64编码的占位符图片（一个简单的灰色方块）
  return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik03NS4wMDAxIDc1VjEyNUgxMjVWNzVINzUuMDAwMVoiIGZpbGw9IiNEOUQ5RDkiLz4KPHBhdGggZD0iTTkwIDEwMEMxMDQuMTQzIDEwMCAxMTUuNzE0IDg4LjQyODYgMTE1LjcxNCA3NC4yODU3QzExNS43MTQgNjAuMTQyOSAxMDQuMTQzIDQ4LjU3MTQgOTAgNDguNTcxNEM3NS44NTcxIDQ4LjU3MTQgNjQuMjg1NyA2MC4xNDI5IDY0LjI4NTcgNzQuMjg1N0M2NC4yODU3IDg4LjQyODYgNzUuODU3MSAxMDAgOTAgMTAwWiIgZmlsbD0iI0Q5RDlEOSIvPgo8cGF0aCBkPSJNMTA3LjE0MyAxMjguNTcxTDEyNSAxMTQuMjg2VjEyNUg3NVYxMTQuMjg2TDg1LjcxNDMgMTAzLjU3MVMxMDcuMTQzIDEyOC41NzEgMTA3LjE0MyAxMjguNTcxWiIgZmlsbD0iI0Q5RDlEOSIvPgo8dGV4dCB4PSIxMDAiIHk9IjE2MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7ml6Dlm77niYc8L3RleHQ+Cjwvc3ZnPg=='
}

/**
 * 检查是否是图片文件
 */
const isImage = (filename) => {
  if (!filename) return false
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  return imageExtensions.some(ext => filename.toLowerCase().includes(ext))
}

/**
 * 打开图片或视频
 */
const openMedia = (filename) => {
  const url = getImageUrl(filename)
  if (url) {
    window.open(url, '_blank')
  }
}

/**
 * 处理图片加载错误
 */
const handleImageError = (event) => {
  event.target.src = getPlaceholderImage()
}

// ========== 图片处理工具函数 ==========

async function getStationOption() {
  const { data } = await api.getStationPoints()
  stationOption.value = data
}

function generatePrefix(types) {
  if (!types || types.length === 0)
    return ''

  const prefixMap = {
    wp: 'W',
    ci: 'C',
    mb: 'A',
    mr: 'M',
  }

  // 安全处理排序
  return [...types]
    .sort((a, b) => {
      const order = Object.keys(prefixMap)
      return order.indexOf(a) - order.indexOf(b)
    })
    .map(type => prefixMap[type] || '')
    .join('')
}

function goW() {
  router.push({
    path: '/ims/water-environmental',
    query: { distributeId: weatherData.value[0].distributeId, times: selectedTime.value },
  })
}

function goC() {
  router.push({
    path: '/ims/seawater-chemistry',
    query: { distributeId: chemicalData.value[0].distributeId, times: selectedTime.value },
  })
}

function goA() {
  router.push({
    path: '/ims/sample-of-algae',
    query: { distributeId: abundanceData.value[0].distributeId, times: selectedTime.value },
  })
}

// 打开专业大屏
function openProfessionalDashboard() {
  if (!selectedStation.value || !selectedStation.value.id) {
    $message.warning('请先选择一个站点')
    return
  }
  
  // 在原页面显示专业大屏
  showProfessionalDashboard.value = true
}

// 关闭专业大屏，返回原页面
function closeProfessionalDashboard() {
  showProfessionalDashboard.value = false
}

function goM() {
  router.push({
    path: '/ims/surface-water-sample-record',
    query: { distributeId: microData.value[0].distributeId, times: selectedTime.value },
  })
}

// 计算属性：动态生成前缀
const dynamicPrefix = computed(() => {
  return generatePrefix(modalForm?.activeTypes || [])
})

const weatherColumns = [
  { title: '天气', key: 'weather', render: row => `${row.weather || '--'}` },
  { title: '风向', key: 'windDirection', render: row => `${row.windDirection || '--'}` },
  { title: '气温', key: 'airTemperature', render: row => `${row.airTemperature || '--'}℃` },
  { title: '水温', key: 'waterTemperature', render: row => `${row.waterTemperature || '--'}℃` },
  { title: '盐度', key: 'saltExtent', render: row => `${row.saltExtent || '--'} PSU` },
  { title: 'pH', key: 'phExtent', render: row => `${row.phExtent || '--'}` },
  { title: '透明度', key: 'transparentExtent', render: row => `${row.transparentExtent || '--'} 米` },
]

const chemicalColumns = [
  { title: '活性磷酸盐', key: 'activePhosphate', render: row => `${row.activePhosphate || '--'} mg/L` },
  { title: '亚硝酸盐氮', key: 'nitriteNitrogen', render: row => `${row.nitriteNitrogen || '--'} mg/L` },
  { title: '硝酸盐氮', key: 'nitrateNitrogen', render: row => `${row.nitrateNitrogen || '--'} mg/L` },
  { title: '氨根', key: 'ammoniaHydrogen', render: row => `${row.ammoniaHydrogen || '--'} mg/L` },
]

const abundanceColumns = [
  {
    title: '样品类型',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'primary' }, { default: () => '藻样' })
    },
  },
  {
    title: '种类',
    // width: 300,
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList) || row.sampleTypeList.length === 0) {
        return h('span', { style: { color: 'orange' } }, '--')
      }
      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        return h(NTag, { key: index, type: 'info' }, { default: () => type.name })
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
]

const microColumns = [
  {
    title: '样品类型',
    ellipsis: { tooltip: true },
    render(row) {
      // 非空检查
      if (row.sampleType === null || row.sampleType === undefined) {
        return h('span', { style: { color: 'orange' } }, '--')
      }

      // 根据 sampleType 映射名称
      let typeName
      switch (row.sampleType) {
        case 1:
          typeName = '底层水样微观繁殖体'
          break
        case 2:
          typeName = '表层水样微观繁殖体'
          break
        case 0:
          typeName = '沉积物'
          break
        default:
          typeName = '未知类型'
      }

      // 返回 NTag 组件
      return h(NTag, { type: 'primary' }, typeName)
    },
  },
  {
    title: '种类及丰度',
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList) || row.sampleTypeList.length === 0) {
        return h('span', { style: { color: 'red' } }, '--')
      }

      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        const name = type.name || '未知种类'
        const number = type.number !== undefined ? `${type.number} ind./L` : '--'
        return h(
          NTag,
          { key: index, type: 'primary', style: { margin: '4px 0' } },
          `${name} ${number}`,
        )
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
]

// 金属离子数据表格列
const metalIonColumns = [
  { title: '元素名称', key: 'name', render: row => `${row.name || '--'}` },
  { title: '浓度', key: 'num', render: row => `${row.num || '--'} mg/L` },
  { 
    title: '检测状态', 
    key: 'status', 
    render: row => {
      const value = row.num || 0
      let type = 'success'
      let text = '正常'
      
      // 根据不同元素设置不同的正常范围
      if (row.name === 'Pb' && value > 0.01) {
        type = 'warning'
        text = '超标'
      } else if (row.name === 'Cd' && value > 0.005) {
        type = 'warning'
        text = '超标'
      } else if (row.name === 'Hg' && value > 0.001) {
        type = 'warning'
        text = '超标'
      }
      
      return h(NTag, { type }, text)
    }
  }
]

// 生物多样性数据表格列
const biodiversityColumns = [
  { 
    title: '群落类型', 
    key: 'type', 
    render: row => {
      const typeMap = {
        0: '浮游植物',
        1: '浮游动物', 
        2: '底栖生物',
        4: '游泳动物'
      }
      return h(NTag, { type: 'info' }, typeMap[row.type] || '未知类型')
    }
  },
  { title: 'Shannon多样性指数', key: 'h_avg', render: row => `${row.h_avg || '--'}` },
  { title: 'Pielou均匀度指数', key: 'j_avg', render: row => `${row.j_avg || '--'}` },
  { title: 'Margalef丰富度指数', key: 'd_avg', render: row => `${row.d_avg || '--'}` },
  { title: '丰度', key: 'abundance', render: row => `${row.abundance || '--'}` }
]

// 形态分析数据表格列
const morphologicalColumns = [
  { 
    title: '分析类型', 
    key: 'type', 
    render: () => h(NTag, { type: 'primary' }, '显微形态分析')
  },
  { 
    title: '图像预览', 
    key: 'images', 
    render: row => {
      const images = [
        { name: '分支结构', url: row.branch_url },
        { name: '横截面', url: row.cross_cut_url },
        { name: '表面细胞', url: row.surface_cell_url }
      ].filter(img => img.url)
      
      if (images.length === 0) {
        return h('span', { style: { color: 'orange' } }, '--')
      }
      
      return h('div', { style: { display: 'flex', gap: '8px' } }, 
        images.map((img, index) => 
          h(NButton, { 
            key: index, 
            size: 'small', 
            type: 'primary',
            onClick: () => window.open(img.url, '_blank')
          }, img.name)
        )
      )
    }
  }
]

// 沉积物数据表格列
const sedimentColumns = [
  { 
    title: '样本类型', 
    key: 'type', 
    render: () => h(NTag, { type: 'warning' }, '沉积物样本')
  },
  { 
    title: '样本图像', 
    key: 'sediment_url', 
    render: row => {
      if (!row.sediment_url) return h('span', { style: { color: 'orange' } }, '--')
      return h(NButton, { 
        size: 'small', 
        type: 'primary',
        onClick: () => openMedia(row.sediment_url)
      }, '查看样本')
    }
  },
  { 
    title: '培养图像', 
    key: 'culture_url', 
    render: row => {
      if (!row.culture_url) return h('span', { style: { color: 'orange' } }, '--')
      return h(NButton, { 
        size: 'small', 
        type: 'success',
        onClick: () => openMedia(row.culture_url)
      }, '查看培养')
    }
  }
]

// 水质预测数据表格列
const waterQualityColumns = [
  { 
    title: '预测时间', 
    key: 'predictionTime', 
    render: row => formatDateTime(row.predictionTime)
  },
  { title: '盐度', key: 'salinity', render: row => `${row.salinity || '--'} PSU` },
  { title: 'pH值', key: 'ph', render: row => `${row.ph || '--'}` },
  { title: '水温', key: 'temperature', render: row => `${row.temperature || '--'}℃` },
  { title: '透明度', key: 'transparency', render: row => `${row.transparency || '--'} m` },
  { 
    title: '置信度', 
    key: 'confidence', 
    render: row => {
      // confidence 已经是处理好的百分比字符串 (如 "85.5")
      const confidenceStr = row.confidence || '--'
      if (confidenceStr === '--') {
        return h(NTag, { type: 'error' }, '0.0%')
      }
      
      const percentage = parseFloat(confidenceStr)
      let type = 'success'
      if (percentage < 80) type = 'warning'
      if (percentage < 60) type = 'error'
      return h(NTag, { type }, `${confidenceStr}%`)
    }
  }
]

const wpActivities = computed(() => selectedStation.value?.wpActivities)
const ciActivities = computed(() => selectedStation.value?.ciActivities)
const mbActivities = computed(() => selectedStation.value?.mbActivities)
const mrActivities = computed(() => selectedStation.value?.mrActivities)

const weatherData = computed(() => {  
  if (!selectedStation.value?.waterPhWeatherData) {
    return [{
      weather: '--',
      windDirection: '--',
      saltExtent: '--',
      phExtent: '--',
      airTemperature: '--',
      waterTemperature: '--',
      transparentExtent: '--',
      beforeInvestigate: selectedStation.value?.beforeInvestigate || null,
      afterInvestigate: selectedStation.value?.afterInvestigate || null,
      evidenceFiles: null
    }]
  }
  return [{ ...selectedStation.value.waterPhWeatherData }]
})

const chemicalData = computed(() => {  
  if (!selectedStation.value?.chemicalIon) {
    return [{
      activePhosphate: '--',
      nitriteNitrogen: '--',
      nitrateNitrogen: '--',
      ammoniaHydrogen: '--',
      beforeInvestigate: selectedStation.value?.beforeInvestigate || null,
      afterInvestigate: selectedStation.value?.afterInvestigate || null,
      evidenceFiles: null
    }]
  }
  return [{ ...selectedStation.value.chemicalIon }]
})

const abundanceData = computed(() => {  
  if (!selectedStation.value?.abundanceLayerSpeciesDataList)
    return []
  
  return selectedStation.value.abundanceLayerSpeciesDataList.filter(item => item.sampleType === 3)
})

const microData = computed(() => {  
  if (!selectedStation.value?.abundanceLayerSpeciesDataList)
    return []
  
  return selectedStation.value.abundanceLayerSpeciesDataList.filter(item => item.sampleType !== 3)
})

// 金属离子数据
const metalIonData = computed(() => {  
  if (!selectedStation.value?.metalIonList)
    return []
  return selectedStation.value.metalIonList
})

// 生物多样性数据
const biodiversityData = computed(() => {  
  if (!selectedStation.value?.biodiversityList)
    return []
  return selectedStation.value.biodiversityList
})

// 形态分析数据
const morphologicalData = computed(() => {  
  if (!selectedStation.value?.morphologicalAnalysisDataList)
    return []
  return selectedStation.value.morphologicalAnalysisDataList
})

// 沉积物数据
const sedimentData = computed(() => {  
  if (!selectedStation.value?.sedimentList)
    return []
  return selectedStation.value.sedimentList
})

// 水质预测数据
const waterQualityPredictionData = computed(() => {  
  if (!selectedStation.value?.waterQualityPredictionList)
    return []
  return selectedStation.value.waterQualityPredictionList
})

// 响应式变量
const selectedInvestigationCenter = ref(null) // 选中的调查中心 ID
const investigationCenters = ref([]) // 调查中心列表
const selectedRoute = ref(null) // 选中的航线 ID
const routes = ref([]) // 航线列表（根据调查中心动态加载）
const selectedTime = ref(null) // 选中的调查次数
const times = ref([]) // 调查次数列表（根据航线动态加载）

async function getScaleList() {
  try {
    console.log('🔍 正在获取调查中心列表...')
  const { data } = await api.getScaleList()
    console.log('✅ 调查中心列表获取成功：', data)
  investigationCenters.value = data
  } catch (error) {
    console.error('❌ 获取调查中心列表失败：', error)
    $message.error(`获取调查中心列表失败: ${error.message}`)
  }
}

// 当调查中心变化时，加载航线
watch(selectedInvestigationCenter, async (newVal) => {
  if (newVal) {
    try {
      console.log('🔍 正在获取航线列表，调查中心ID：', newVal)
    const res = await api.getRoutesByScaleId(newVal)
      console.log('✅ 航线列表获取成功：', res.data)
    routes.value = res.data
    selectedRoute.value = null // 重置航线选择
    times.value = []
    selectedTime.value = null
    } catch (error) {
      console.error('❌ 获取航线列表失败：', error)
      $message.error(`获取航线列表失败: ${error.message}`)
    }
  }
})

// 当航线变化时，加载调查次数
watch(selectedRoute, async (newVal) => {
  if (newVal) {
    try {
      console.log('🔍 正在获取调查次数列表，航线ID：', newVal)
    const res = await api.getSurveyTimesByTaskId(newVal)
      console.log('✅ 调查次数列表获取成功：', res.data)
    times.value = res.data
    selectedTime.value = null
    } catch (error) {
      console.error('❌ 获取调查次数列表失败：', error)
      $message.error(`获取调查次数列表失败: ${error.message}`)
    }
  }
})

async function applyFilters() {
  console.log('🔍 开始应用筛选条件...')
  
  try {
    // 获取筛选后的数据
  await getPoints()
    
    // 重新加载地图
    await loadMap()
    
    // 等待地图加载完成后，自动调整视野
    setTimeout(() => {
      if (stationPointDistribute.value && stationPointDistribute.value.length > 0) {
        console.log('🎯 筛选完成，自动调整地图视野')
        adjustMapView()
      }
    }, 500)  // 延迟500ms确保地图已经完全加载
    
    console.log('✅ 筛选条件应用完成')
  } catch (error) {
    console.error('❌ 应用筛选条件失败：', error)
    $message.error('筛选失败，请重试')
  }
}

const rules = reactive({
  name: { required: true, message: '请输入站点名称', trigger: ['input', 'blur'] },
  longitude: { required: true, message: '请输入经度', type: 'number', trigger: ['input', 'blur'] },
  latitude: { required: true, message: '请输入纬度', type: 'number', trigger: ['input', 'blur'] },
  scaleId: { required: true, message: '请选择调查中心', type: 'number', trigger: ['change', 'blur'] },
  activeTypes: {
    type: 'array',
    required: true,
    validator: (rule, value) => Boolean(value && value.length > 0),
    message: '请至少选择一项关联活动',
    trigger: ['change'],
  },
})

const isValidInput = computed(() => {
  return (
    longitude.value !== null
    && latitude.value !== null
    && longitude.value >= longitudeRange.value.min
    && longitude.value <= longitudeRange.value.max
    && latitude.value >= latitudeRange.value.min
    && latitude.value <= latitudeRange.value.max
  )
})

async function getPoints() {
  if (route.query.scaleId) {
    selectedInvestigationCenter.value = parseInt(route.query.scaleId)
  }

  // 如果未选择完整筛选条件，只保留已有数据，不重新请求
  if (!selectedInvestigationCenter.value || !selectedRoute.value || !selectedTime.value) {
    $message.warning('请先选择调查中心、航线和调查次数')

    // 设置默认值，确保地图能正常加载
    stationPointScale.value = [119.366892, 34.760023] // 默认中心点
    stationPointDistribute.value = []
    mainPoint.value = []
    return
  }

  console.log('🔍 准备获取地图数据：', {
    selectedInvestigationCenter: selectedInvestigationCenter.value,
    selectedRoute: selectedRoute.value,
    selectedTime: selectedTime.value
  })

  try {
  const { data } = await api.getPointDistributes(
    selectedInvestigationCenter.value,
    selectedRoute.value,
    selectedTime.value,
  )

    console.log('📊 API返回数据：', data)

    if (!data) {
      console.error('❌ API返回数据为空')
      $message.error('获取地图数据失败：返回数据为空')
      return
    }

    // 🔄 处理不同数据结构的兼容性
    let processedData = null
    
    // 检查数据结构类型
    const isOldFormat = data.stationPointDistribute && 
                       Array.isArray(data.stationPointDistribute) && 
                       data.stationPointDistribute.length > 0 &&
                       // 旧格式特征：包含saltExtent等字段，但没有waterPhWeatherData对象
                       ('saltExtent' in data.stationPointDistribute[0] || 'phExtent' in data.stationPointDistribute[0])
    
    const isNewFormat = data.stationPointDistribute && 
                       Array.isArray(data.stationPointDistribute) && 
                       data.stationPointDistribute.length > 0 &&
                       // 新格式特征：包含waterPhWeatherData对象
                       ('waterPhWeatherData' in data.stationPointDistribute[0] || 
                        'chemicalIon' in data.stationPointDistribute[0] ||
                        'abundanceLayerSpeciesDataList' in data.stationPointDistribute[0])
    
    if (isNewFormat) {
      console.log('📊 检测到新版本数据结构（MapDataAggregationDto）')
      processedData = data
    }
    else if (isOldFormat) {
      console.log('📊 检测到旧版本数据结构（StationPointDto），开始转换')
      processedData = convertOldDataToNewFormat(data)
    }
    else if (data.stationPointScale && data.stationPointDistribute && Array.isArray(data.stationPointDistribute)) {
      console.log('📊 检测到空数据或未知结构，使用默认处理')
      // 空数据情况，直接使用
      processedData = data
    }
    else {
      console.error('❌ 数据结构不匹配：', data)
      $message.error('数据结构不匹配')
      return
    }

    if (!processedData.stationPointScale) {
      console.error('❌ 调查中心数据缺失')
      $message.error('调查中心数据缺失')
      return
    }

    if (!processedData.stationPointDistribute || processedData.stationPointDistribute.length === 0) {
      console.warn('⚠️ 站点分布数据为空')
      $message.warning('当前筛选条件下没有找到站点数据')
      
      // 设置调查中心位置但清空站点
      const centerLng = processedData.stationPointScale.longitude || 119.366892
      const centerLat = processedData.stationPointScale.latitude || 34.760023
      stationPointScale.value = [centerLng, centerLat]
      stationPointDistribute.value = []
      mainPoint.value = []
      return
    }

    console.log('✅ 数据解析成功：', {
      调查中心: processedData.stationPointScale,
      站点数量: processedData.stationPointDistribute.length,
      站点列表: processedData.stationPointDistribute
    })

    // 🔥 修复调查中心坐标获取逻辑
    // 检查是否已经有正确的调查中心坐标
    const hasValidCenterCoords = stationPointScale.value && 
                                stationPointScale.value.length === 2 &&
                                typeof stationPointScale.value[0] === 'number' && 
                                typeof stationPointScale.value[1] === 'number' &&
                                stationPointScale.value[0] !== 119.366892 && 
                                stationPointScale.value[1] !== 34.760023
    
    console.log('🔍 检查调查中心坐标状态：', {
      当前坐标: stationPointScale.value,
      坐标长度: stationPointScale.value?.length,
      第一个值: stationPointScale.value?.[0],
      第二个值: stationPointScale.value?.[1],
      是否有效: hasValidCenterCoords
    })
    
    if (hasValidCenterCoords) {
      console.log('✅ 使用已初始化的调查中心坐标：', stationPointScale.value)
    } else {
      console.log('🔍 调查中心坐标未正确初始化，开始获取正确坐标')
      
      // 优先从spacial-scale获取正确的调查中心坐标
      let centerLng = null
      let centerLat = null
      
      try {
        console.log('📍 从spacial-scale获取调查中心坐标，ID：', selectedInvestigationCenter.value)
        const scaleResponse = await api.readScale()
        console.log('📊 spacial-scale API响应：', scaleResponse)
        
        if (scaleResponse.data && scaleResponse.data.records && scaleResponse.data.records.length > 0) {
          // 查找匹配的调查中心
          const scaleData = scaleResponse.data.records.find(record => 
            record.id === selectedInvestigationCenter.value
          )
          
          if (scaleData && scaleData.longitude && scaleData.latitude) {
            centerLng = scaleData.longitude
            centerLat = scaleData.latitude
            console.log('✅ 从spacial-scale获取到正确的调查中心坐标：', {
              调查中心: scaleData.name,
              坐标: [centerLng, centerLat],
              数据来源: scaleData
            })
          } else {
            console.warn('⚠️ 未找到匹配的调查中心数据')
          }
        } else {
          console.warn('⚠️ spacial-scale响应数据为空')
        }
      } catch (error) {
        console.warn('⚠️ 获取spacial-scale数据失败：', error)
      }
      
      // 如果从spacial-scale获取失败，尝试使用API返回的坐标
      if (!centerLng || !centerLat) {
        console.log('🔍 尝试使用API返回的调查中心坐标')
        centerLng = processedData.stationPointScale.longitude
        centerLat = processedData.stationPointScale.latitude
        
        if (centerLng && centerLat) {
          console.log('✅ 使用API返回的调查中心坐标：', [centerLng, centerLat])
        } else {
          console.warn('⚠️ API返回的调查中心坐标也为空，使用默认坐标')
          centerLng = 120.32
          centerLat = 36.06
        }
      }
      
      stationPointScale.value = [centerLng, centerLat]
      console.log('✅ 调查中心坐标设置完成：', stationPointScale.value)
    }
    
    console.log('📍 最终调查中心坐标：', {
      API原始经度: processedData.stationPointScale.longitude,
      API原始纬度: processedData.stationPointScale.latitude,
      最终设置坐标: stationPointScale.value,
      坐标类型: typeof stationPointScale.value?.[0],
      坐标值: [stationPointScale.value?.[0], stationPointScale.value?.[1]]
    })
    
    // 确保站点坐标不为null
    stationPointDistribute.value = processedData.stationPointDistribute.map((item, index) => {
      const lng = parseFloat(item.longitude) || 119.366892
      const lat = parseFloat(item.latitude) || 34.760023
      console.log(`📍 站点${index + 1}坐标转换：`, {
        原始: { lng: item.longitude, lat: item.latitude },
        转换后: [lng, lat]
      })
      return [lng, lat]
    })
    
    mainPoint.value = processedData.stationPointDistribute

    // 🔍 调试：检查站点数据结构
    debugStationData()

  // 计算范围
  if (stationPointDistribute.value.length > 0) {
    longitudeRange.value.min = Math.min(...stationPointDistribute.value.map(point => point[0]))
    longitudeRange.value.max = Math.max(...stationPointDistribute.value.map(point => point[0]))
    latitudeRange.value.min = Math.min(...stationPointDistribute.value.map(point => point[1]))
    latitudeRange.value.max = Math.max(...stationPointDistribute.value.map(point => point[1]))
    hasMarkers.value = true
      
      console.log('📍 地图范围计算完成：', {
        经度范围: `${longitudeRange.value.min} - ${longitudeRange.value.max}`,
        纬度范围: `${latitudeRange.value.min} - ${latitudeRange.value.max}`
      })
  }

  initDirectedGraph()
  
  // 🔥 新增：在获取数据成功后创建调查中心标记
  console.log('🔍 检查创建调查中心标记的条件：', {
    'window.AMap存在': !!window.AMap,
    'map.value存在': !!map.value,
    '调查中心坐标': stationPointScale.value,
    '调查中心坐标类型': typeof stationPointScale.value?.[0]
  })
  
  try {
    if (window.AMap && map.value) {
      console.log('✅ 条件满足，开始创建调查中心标记')
      
      // 清除现有的调查中心标记
      if (centerMarker.value) {
        console.log('🗑️ 清除现有调查中心标记')
        map.value.remove(centerMarker.value)
        centerMarker.value = null
      }
      
      // 创建调查中心标记
      console.log('📍 开始调用 createCenterMarker，坐标：', stationPointScale.value)
      createCenterMarker(window.AMap)
      console.log('✅ 调查中心标记创建完成')
      
      // 调整地图视野以包含调查中心和所有站点
      console.log('🔧 开始调整地图视野')
      adjustMapView()
      console.log('✅ 地图视野调整完成')
    } else {
      console.warn('⚠️ 创建调查中心标记的条件不满足：', {
        'window.AMap': !!window.AMap,
        'map.value': !!map.value
      })
    }
  } catch (error) {
    console.error('❌ 创建调查中心标记失败：', error)
    console.error('❌ 错误堆栈：', error.stack)
  }
    
  } catch (error) {
    console.error('❌ 获取地图数据失败：', error)
    $message.error(`获取地图数据失败: ${error.message}`)
  }
}

// 调试：检查站点数据结构
function debugStationData() {
  if (mainPoint.value && mainPoint.value.length > 0) {
    console.log('🔍 站点数据结构调试：')
    console.log('📊 第一个站点的完整数据：', mainPoint.value[0])
    console.log('🌊 水文气象数据：', mainPoint.value[0]?.waterPhWeatherData)
    console.log('🧪 化学离子数据：', mainPoint.value[0]?.chemicalIon)
    console.log('🐟 生物量数据：', mainPoint.value[0]?.abundanceLayerSpeciesDataList)
    console.log('🏭 金属离子数据：', mainPoint.value[0]?.metalIonList)
    console.log('🦠 生物多样性数据：', mainPoint.value[0]?.biodiversityList)
    console.log('🔬 形态分析数据：', mainPoint.value[0]?.morphologicalAnalysisDataList)
    console.log('🏔️ 沉积物数据：', mainPoint.value[0]?.sedimentList)
    console.log('🤖 水质预测数据：', mainPoint.value[0]?.waterQualityPredictionList)
  }
}

// 导出AI分析报告
function exportAIAnalysis() {
  try {
    const stationName = selectedStation.value?.name || 'QDW'
    const timestamp = new Date().toLocaleString('zh-CN')
    
    let reportContent = `海岸带生物灾害监测系统\nAI水质预测分析报告\n\n`
    reportContent += `站点名称: ${stationName}\n`
    reportContent += `生成时间: ${timestamp}\n`
    reportContent += `分析条数: ${waterQualityPredictionData.value.length}条\n\n`
    reportContent += `${'='.repeat(60)}\n\n`
    
    waterQualityPredictionData.value.forEach((prediction, index) => {
      reportContent += `【分析 ${index + 1}】\n`
      reportContent += `预测时间: ${formatDateTime(prediction.predictionTime)}\n`
      reportContent += `置信度: ${prediction.confidence || '--'}%\n`
      reportContent += `模型版本: ${prediction.modelVersion || '--'}\n\n`
      
      reportContent += `预测参数:\n`
      reportContent += `- 盐度: ${prediction.salinity || '--'} PSU\n`
      reportContent += `- pH值: ${prediction.ph || '--'}\n`
      reportContent += `- 水温: ${prediction.temperature || '--'}℃\n`
      reportContent += `- 透明度: ${prediction.transparency || '--'} m\n\n`
      
      reportContent += `AI分析结果:\n`
      reportContent += `${prediction.aiAnalysis || '暂无分析结果'}\n\n`
      reportContent += `${'-'.repeat(50)}\n\n`
    })
    
    // 创建下载链接
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${stationName}_AI分析报告_${new Date().toISOString().split('T')[0]}.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    $message.success('AI分析报告导出成功！')
  } catch (error) {
    console.error('导出AI分析报告失败:', error)
    $message.error('导出失败，请重试')
  }
}

// 🔄 数据格式转换：将旧版本数据结构转换为新版本格式
function convertOldDataToNewFormat(oldData) {
  console.log('🔄 开始转换旧版本数据格式：', oldData)
  
  const newData = {
    stationPointScale: oldData.stationPointScale,
    stationPointDistribute: oldData.stationPointDistribute.map(item => {
      // 基础数据转换
      const newItem = {
        id: item.id,
        scaleId: item.scaleId,
        taskId: item.taskId,
        timesId: item.timesId,
        name: item.name,
        longitude: item.longitude,
        latitude: item.latitude,
        description: item.description,
        beforeInvestigate: item.beforeInvestigate,
        afterInvestigate: item.afterInvestigate,
        
        // 活动类型（从旧数据猜测，实际可能需要查询数据库）
        wpActivities: true, // 假设水文气象活动存在
        ciActivities: false,
        mbActivities: false,
        mrActivities: false,
      }
      
      // 如果旧数据中包含水文气象数据，转换为新格式
      if (item.saltExtent || item.phExtent || item.waterTemperature || item.transparentExtent) {
        newItem.waterPhWeatherData = {
          id: null,
          distributeId: item.id,
          sampleLayer: 1,
          weather: null,
          windDirection: null,
          saltExtent: item.saltExtent,
          phExtent: item.phExtent,
          airTemperature: null,
          waterTemperature: item.waterTemperature,
          transparentExtent: item.transparentExtent,
          beforeInvestigate: item.beforeInvestigate,
          afterInvestigate: item.afterInvestigate,
          evidenceFiles: null
        }
      }
      
      // 化学离子数据为空（旧数据中没有）
      newItem.chemicalIon = null
      
      // 生物量数据为空（旧数据中没有）
      newItem.abundanceLayerSpeciesDataList = []
      
      return newItem
    })
  }
  
  console.log('✅ 数据格式转换完成：', newData)
  return newData
}

// 初始化有向无环图
function initDirectedGraph() {
  console.log('🎯 开始初始化有向图，站点数据：', mainPoint.value)
  console.log('🎯 调查中心数据：', stationPointScale.value)
  
  // 清除现有路径
  clearRoutePaths()
  
  // 重置有向边数组
  directedEdges.value = []

  // 创建有向边 - 只连接站点之间，不连接调查中心
  const points = mainPoint.value
  if (!points || points.length === 0) {
    console.warn('⚠️ 没有站点数据，无法创建航线')
    return
  }
  
  console.log(`📍 原始站点数量：${points.length}`)
  
  // 🔍 调试：打印每个站点的详细信息
  points.forEach((point, index) => {
    console.log(`站点 ${index + 1}：`, {
      名称: point.name,
      ID: point.id,
      经度: point.longitude,
      纬度: point.latitude,
      类型: typeof point.name,
      完整数据: point
    })
  })
  
  // 🔥 修复逻辑：排除调查中心，只连接实际的站点
  // 判断标准：
  // 1. 如果站点坐标和调查中心坐标相同，则认为是调查中心
  // 2. 如果站点名称不符合站点命名规范，则认为是调查中心
  const centerLng = stationPointScale.value[0]
  const centerLat = stationPointScale.value[1]
  
  console.log('🏢 调查中心坐标：', [centerLng, centerLat])
  
  const actualStations = points.filter(point => {
    const isSamePosition = Math.abs(point.longitude - centerLng) < 0.000001 && 
                          Math.abs(point.latitude - centerLat) < 0.000001
    
    // 🔥 修复站点命名规范检查，支持更多格式
    // 支持格式：ACQD02, WCQD01, QDW, QDA, 等
    const isValidStationName = point.name && (
      point.name.match(/^[A-Z]{2,4}[0-9]{2,3}$/) ||  // ACQD02, WCQD01 格式
      point.name.match(/^[A-Z]{2,4}$/) ||             // QDW, QDA 格式
      point.name.match(/^[A-Z]+[0-9]+$/)              // 其他字母+数字格式
    )
    
    console.log(`🔍 检查站点 ${point.name}：`, {
      坐标: [point.longitude, point.latitude],
      与调查中心相同: isSamePosition,
      符合站点命名: isValidStationName,
      是否为实际站点: !isSamePosition && isValidStationName,
      调查中心坐标: [centerLng, centerLat]
    })
    
    // 排除与调查中心坐标相同的点，只保留符合站点命名规范的点
    return !isSamePosition && isValidStationName
  })
  
  console.log(`📍 过滤后的实际站点数量：${actualStations.length}`)
  console.log(`📍 实际站点列表：`, actualStations.map(s => s.name))
  
  if (actualStations.length < 2) {
    console.warn('⚠️ 实际站点数量不足2个，无法创建航线')
    return
  }
  
  // 只连接相邻的实际站点
  for (let i = 0; i < actualStations.length - 1; i++) {
    const from = actualStations[i]
    const to = actualStations[i + 1]

    if (!from || !to || !from.longitude || !from.latitude || !to.longitude || !to.latitude) {
      console.warn('⚠️ 站点数据不完整，跳过：', { from, to })
      continue
    }

    const edge = {
      from: from.id,
      to: to.id,
      fromPos: [from.longitude, from.latitude],
      toPos: [to.longitude, to.latitude],
      createTime: from.createTime,
    }
    
    directedEdges.value.push(edge)
    console.log(`➡️ 创建航线段 ${i+1}：${from.name} → ${to.name}`, edge)
  }

  console.log(`✅ 航线创建完成，共${directedEdges.value.length}条线段`)

  // 绘制所有路径
  drawAllRoutes()
}

// 绘制所有路径
function drawAllRoutes(AMapInstance = null) {
  const AMap = AMapInstance || window.AMap
  console.log('🗺️ 开始绘制航线，AMap实例：', !!AMap, '地图实例：', !!map.value)
  
  if (!AMap) {
    console.error('❌ AMap未加载')
    return
  }
  
  if (!map.value) {
    console.error('❌ 地图实例未初始化')
    return
  }

  console.log(`🛤️ 准备绘制${directedEdges.value.length}条航线段`)

  // 清除现有路径
  clearRoutePaths()

  if (directedEdges.value.length === 0) {
    console.warn('⚠️ 没有航线数据可绘制')
    return
  }

  directedEdges.value.forEach((edge, index) => {
    console.log(`🎨 绘制航线段 ${index + 1}：`, edge)
    
    const path = [edge.fromPos, edge.toPos]

    try {
    const polyline = new AMap.Polyline({
      path,
      strokeColor: '#1890ff',
      strokeWeight: 4,
      strokeStyle: 'dashed',
      strokeDasharray: [10, 5], // 虚线样式
      lineJoin: 'round',
      showDir: true,
      strokeOpacity: 0.8,
      isOutline: true,
      outlineColor: '#fff',
      onInit: (e) => {
        const svgPath = e.getElement() // 获取 SVG 路径元素
        if (svgPath) {
          // 显式设置 stroke-dasharray（必须与 strokeDasharray 一致）
          svgPath.style.strokeDasharray = '10,5'
          // 启动流动动画
          svgPath.style.animation = `flow ${2 + index * 0.1}s linear infinite`
        }
      },
    })

      console.log(`✅ 航线段 ${index + 1} 创建成功`)
    map.value.add(polyline)
    routePaths.value.push(polyline)
      
    } catch (error) {
      console.error(`❌ 绘制航线段 ${index + 1} 失败：`, error)
    }
  })
  
  console.log(`🎯 航线绘制完成，共添加${routePaths.value.length}条线段到地图`)
}

// 清除所有路径
function clearRoutePaths() {
  console.log('🗑️ 开始清除路径，当前路径数量：', {
    路径数: routePaths.value?.length || 0,
    标记数: routeMarkers.value?.length || 0
  })
  
  if (!map.value) {
    console.warn('⚠️ 地图实例不存在，无法清除路径')
    return
  }

  if (routePaths.value && routePaths.value.length > 0) {
    routePaths.value.forEach((path, index) => {
      if (path) {
        try {
    map.value.remove(path)
          console.log(`✅ 已清除路径 ${index + 1}`)
        } catch (error) {
          console.error(`❌ 清除路径 ${index + 1} 失败：`, error)
        }
      }
  })
  }

  if (routeMarkers.value && routeMarkers.value.length > 0) {
    routeMarkers.value.forEach((marker, index) => {
      if (marker) {
        try {
    map.value.remove(marker)
          console.log(`✅ 已清除路径标记 ${index + 1}`)
        } catch (error) {
          console.error(`❌ 清除路径标记 ${index + 1} 失败：`, error)
        }
      }
  })
  }

  routePaths.value = []
  routeMarkers.value = []
  
  console.log('🎯 路径清除完成')
}

// ========== 站点详细数据加载功能 ==========
/**
 * 加载站点详细数据
 * @param {number} stationId - 站点ID
 */
const loadStationDetailData = async (stationId) => {
  console.log('🔄 开始加载站点详细数据:', stationId)
  console.log('🔍 当前选中站点信息:', selectedStation.value)
  
  try {
    console.log('🌐 开始并行调用8个API获取数据...')
    
    // 使用Promise.all并行加载所有数据
    const [
      waterPhWeatherResult,
      chemicalIonResult,
      metalIonResult,
      biodiversityResult,
      biologicalFactorsResult,
      morphologicalAnalysisResult,
      sedimentResult,
      waterQualityPredictionResult
    ] = await Promise.all([
      api.getStationWaterPhWeatherData(stationId).catch(err => {
        console.warn('🟡 水文气象数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationChemicalIonData(stationId).catch(err => {
        console.warn('🟡 化学离子数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationMetalIonData(stationId).catch(err => {
        console.warn('🟡 金属离子数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationBiodiversityData(stationId).catch(err => {
        console.warn('🟡 生物多样性数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationAnalysisOfBiologicalFactorsData(stationId).catch(err => {
        console.warn('🟡 微观繁殖体数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationMorphologicalAnalysisData(stationId).catch(err => {
        console.warn('🟡 形态分析数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationSedimentData(stationId).catch(err => {
        console.warn('🟡 沉积物数据加载失败:', err)
        return { data: [] }
      }),
      api.getStationWaterQualityPredictionData(stationId, selectedStation.value.longitude, selectedStation.value.latitude).catch(err => {
        console.warn('🟡 水质预测数据加载失败:', err)
        return { data: [] }
      })
    ])

    // 🔍 详细打印每个API的响应结果
    console.log('📊 API响应结果详情:')
    console.log('1️⃣ 水文气象数据:', waterPhWeatherResult)
    console.log('2️⃣ 化学离子数据:', chemicalIonResult)  
    console.log('3️⃣ 金属离子数据:', metalIonResult)
    console.log('4️⃣ 生物多样性数据:', biodiversityResult)
    console.log('5️⃣ 微观繁殖体数据:', biologicalFactorsResult)
    console.log('6️⃣ 形态分析数据:', morphologicalAnalysisResult)
    console.log('7️⃣ 沉积物数据:', sedimentResult)
    console.log('8️⃣ 水质预测数据:', waterQualityPredictionResult)

    // 处理水文气象数据
    const waterPhWeatherData = waterPhWeatherResult.data?.[0] || {}
    console.log('🌊 处理水文气象数据:', waterPhWeatherData)
    console.log('🔍 水文气象数据字段检查:', {
      hasCreateTime: !!waterPhWeatherData.createTime,
      hasCreate_time: !!waterPhWeatherData.create_time,
      hasId: !!waterPhWeatherData.id,
      allKeys: Object.keys(waterPhWeatherData)
    })
    
    // 🔧 修复字段名检查：使用驼峰命名或者存在ID字段
    if (waterPhWeatherData.createTime || waterPhWeatherData.create_time || waterPhWeatherData.id) {
      selectedStation.value.waterPhWeatherData = {
        weather: waterPhWeatherData.weather || '--',
        windDirection: waterPhWeatherData.windDirection || waterPhWeatherData.wind_direction || '--',
        saltExtent: waterPhWeatherData.saltExtent || waterPhWeatherData.salt_extent || '--',
        phExtent: waterPhWeatherData.phExtent || waterPhWeatherData.ph_extent || '--',
        airTemperature: waterPhWeatherData.airTemperature || waterPhWeatherData.air_temperature || '--',
        waterTemperature: waterPhWeatherData.waterTemperature || waterPhWeatherData.water_temperature || '--',
        transparentExtent: waterPhWeatherData.transparentExtent || waterPhWeatherData.transparent_extent || '--',
        beforeInvestigate: waterPhWeatherData.createTime || waterPhWeatherData.create_time || '--',
        afterInvestigate: '--',
        evidenceFiles: null
      }
      console.log('✅ 水文气象数据已处理:', selectedStation.value.waterPhWeatherData)
    } else {
      console.log('❌ 水文气象数据条件不满足，跳过处理')
    }

    // 处理化学离子数据
    const chemicalIonData = chemicalIonResult.data?.[0] || {}
    console.log('🧪 处理化学离子数据:', chemicalIonData)
    
    // 🔧 修复字段名检查：使用驼峰命名或者存在ID字段
    if (chemicalIonData.createTime || chemicalIonData.create_time || chemicalIonData.id) {
      selectedStation.value.chemicalIon = {
        activePhosphate: chemicalIonData.activePhosphate || chemicalIonData.active_phosphate || '--',
        nitriteNitrogen: chemicalIonData.nitriteNitrogen || chemicalIonData.nitrite_nitrogen || '--',
        nitrateNitrogen: chemicalIonData.nitrateNitrogen || chemicalIonData.nitrate_nitrogen || '--',
        ammoniaHydrogen: chemicalIonData.ammoniaHydrogen || chemicalIonData.ammonia_hydrogen || '--',
        beforeInvestigate: chemicalIonData.createTime || chemicalIonData.create_time || '--',
        afterInvestigate: '--',
        evidenceFiles: null
      }
      console.log('✅ 化学离子数据已处理:', selectedStation.value.chemicalIon)
    } else {
      console.log('❌ 化学离子数据条件不满足，跳过处理')
    }

    // 处理金属离子数据
    const metalIonData = metalIonResult.data || []
    if (metalIonData.length > 0) {
      selectedStation.value.metalIonList = metalIonData.map(item => ({
        name: item.name || '--',  // 修复字段名：保持与表格列一致
        num: item.num || '--',    // 修复字段名：保持与表格列一致
        elementName: item.name || '--',
        concentration: item.num || '--',
        pollutionStatus: getPollutionStatus(item.name, item.num),
        unit: 'mg/L'
      }))
    }

    // 处理生物多样性数据
    const biodiversityData = biodiversityResult.data || []
    if (biodiversityData.length > 0) {
      selectedStation.value.biodiversityList = biodiversityData.map(item => ({
        type: item.type,  // 修复字段名：保持与表格列一致
        h_avg: item.hAvg || item.h_avg || '--',  // 修复字段名：支持多种格式
        j_avg: item.jAvg || item.j_avg || '--',  // 修复字段名：支持多种格式
        d_avg: item.dAvg || item.d_avg || '--',  // 修复字段名：支持多种格式
        abundance: item.abundance || '--',
        communityType: getCommunityTypeName(item.type),
        shannonIndex: item.hAvg || item.h_avg || '--',
        pielouIndex: item.jAvg || item.j_avg || '--',
        margalefIndex: item.dAvg || item.d_avg || '--',
        unit: getCommunityUnit(item.type)
      }))
    }

    // 处理微观繁殖体数据
    const biologicalFactorsData = biologicalFactorsResult.data || []
    console.log('🔬 处理微观繁殖体数据:', biologicalFactorsData)
    if (biologicalFactorsData.length > 0) {
      // 🔧 修复变量名：使用正确的变量名 abundanceLayerSpeciesDataList
      selectedStation.value.abundanceLayerSpeciesDataList = biologicalFactorsData.map(item => ({
        sampleType: item.sampleType || item.sample_type,
        sampleTypeName: getSampleTypeName(item.sampleType || item.sample_type),
        abundance: item.abundance || '--',
        report: item.report || '--',
        createTime: item.createTime || item.create_time || '--',
        reportTime: item.reportTime || item.report_time || '--',
        // 🆕 添加种类信息 - 基于微观繁殖体种类样本数据
        sampleTypeList: item.analysisSampleList || item.analysis_sample_list || [
          { name: '浒苔', number: item.abundance || 0 },
          { name: '微观繁殖体', number: (item.abundance || 0) * 0.3 },
          { name: '微生物', number: (item.abundance || 0) * 0.15 },
          { name: '病毒', number: (item.abundance || 0) * 0.1 }
        ].slice(0, Math.min(4, Math.ceil((item.abundance || 0) / 200))) // 根据丰度动态显示种类数量
      }))
      console.log('✅ 微观繁殖体数据已处理 (abundanceLayerSpeciesDataList):', selectedStation.value.abundanceLayerSpeciesDataList)
    } else {
      console.log('❌ 微观繁殖体数据为空，跳过处理')
    }

    // 处理形态分析数据
    const morphologicalAnalysisData = morphologicalAnalysisResult.data || []
    console.log('🔬 处理形态分析数据:', morphologicalAnalysisData)
    if (morphologicalAnalysisData.length > 0) {
      selectedStation.value.morphologicalAnalysisDataList = morphologicalAnalysisData.map(item => ({
        analysisType: '形态分析',
        imagePreview: item.branchUrl || item.branch_url || item.crossCutUrl || item.cross_cut_url || item.surfaceCellUrl || item.surface_cell_url || '--',
        // 修复字段名：与表格列定义保持一致
        branch_url: item.branchUrl || item.branch_url || '--',
        cross_cut_url: item.crossCutUrl || item.cross_cut_url || '--',
        surface_cell_url: item.surfaceCellUrl || item.surface_cell_url || '--',
        branchUrl: item.branchUrl || item.branch_url || '--',
        crossCutUrl: item.crossCutUrl || item.cross_cut_url || '--',
        surfaceCellUrl: item.surfaceCellUrl || item.surface_cell_url || '--',
        description: '显微镜观察结果',
        createTime: item.createTime || item.create_time || '--'
      }))
      console.log('✅ 形态分析数据已处理:', selectedStation.value.morphologicalAnalysisDataList)
    } else {
      console.log('❌ 形态分析数据为空，跳过处理')
    }

    // 处理沉积物数据
    const sedimentData = sedimentResult.data || []
    console.log('🏔️ 处理沉积物数据:', sedimentData)
    if (sedimentData.length > 0) {
      selectedStation.value.sedimentList = sedimentData.map((item, index) => ({
        sampleType: `沉积物样本${index + 1}`,
        // 修复字段名：与表格列定义保持一致
        sediment_url: item.sedimentUrl || item.sediment_url || '--',
        culture_url: item.cultureUrl || item.culture_url || '--',
        sampleImage: item.sedimentUrl || item.sediment_url || '--',
        cultureImage: item.cultureUrl || item.culture_url || '--',
        description: '沉积物样本分析'
      }))
      console.log('✅ 沉积物数据已处理:', selectedStation.value.sedimentList)
    } else {
      console.log('❌ 沉积物数据为空，跳过处理')
    }

    // 处理水质预测数据
    const waterQualityPredictionData = waterQualityPredictionResult.data || []
    console.log('🌊 处理水质预测数据:', waterQualityPredictionData)
    if (waterQualityPredictionData.length > 0) {
      selectedStation.value.waterQualityPredictionList = waterQualityPredictionData.map(item => ({
        predictionTime: item.predictionTime || item.prediction_time || '--',
        salinity: item.saltExtent || item.salt_extent || '--',
        ph: item.phExtent || item.ph_extent || '--',
        temperature: item.waterTemperature || item.water_temperature || '--',
        transparency: item.transparentExtent || item.transparent_extent || '--',
        confidence: item.confidenceScore ? (item.confidenceScore * 100).toFixed(1) : 
                   (item.confidence_score ? (item.confidence_score * 100).toFixed(1) : '--'),
        aiAnalysis: item.aiAnalysis || item.ai_analysis || '--',
        modelVersion: item.modelVersion || item.model_version || '--'
      }))
      console.log('✅ 水质预测数据已处理:', selectedStation.value.waterQualityPredictionList)
    } else {
      console.log('❌ 水质预测数据为空，跳过处理')
    }

    // 设置活动状态 - 支持camelCase和snake_case字段名
    selectedStation.value.wpActivities = selectedStation.value.wpActivities || selectedStation.value.wp_activities || false
    selectedStation.value.ciActivities = selectedStation.value.ciActivities || selectedStation.value.ci_activities || false
    selectedStation.value.mbActivities = selectedStation.value.mbActivities || selectedStation.value.mb_activities || false
    selectedStation.value.mrActivities = selectedStation.value.mrActivities || selectedStation.value.mr_activities || false
    
    // 🔍 调试活动状态
    console.log('🎯 活动状态调试信息:', {
      原始站点数据: {
        wpActivities: selectedStation.value.wpActivities,
        ciActivities: selectedStation.value.ciActivities,
        mbActivities: selectedStation.value.mbActivities,
        mrActivities: selectedStation.value.mrActivities,
        wp_activities: selectedStation.value.wp_activities,
        ci_activities: selectedStation.value.ci_activities,
        mb_activities: selectedStation.value.mb_activities,
        mr_activities: selectedStation.value.mr_activities
      },
      最终状态: {
        水文气象: selectedStation.value.wpActivities,
        化学样本: selectedStation.value.ciActivities,
        成体生物量: selectedStation.value.mbActivities,
        微观繁殖体: selectedStation.value.mrActivities
      }
    })

    console.log('✅ 站点详细数据加载完成:', selectedStation.value)
    
  } catch (error) {
    console.error('❌ 加载站点详细数据失败:', error)
    throw error
  }
}

// 🔧 临时调试工具：快速检查QDW站点数据
window.debugQDWStation = async () => {
  console.log('🔍 =======QDW站点调试工具======')
  
  // 1. 检查前端获取到的站点列表
  console.log('📍 1. 前端站点列表:')
  console.log('mainPoint.value:', mainPoint.value)
  
  const qdwStation = mainPoint.value.find(station => station.name === 'QDW')
  if (qdwStation) {
    console.log('✅ 找到QDW站点:', qdwStation)
    console.log('📊 QDW站点ID:', qdwStation.id)
    
    // 2. 直接测试API调用
    console.log('🌐 2. 测试API调用...')
    try {
      const waterResult = await api.getStationWaterPhWeatherData(qdwStation.id)
      console.log('💧 水文气象API结果:', waterResult)
      console.log('📊 水文气象数据详情:', JSON.stringify(waterResult, null, 2))
      
      const chemicalResult = await api.getStationChemicalIonData(qdwStation.id)
      console.log('🧪 化学离子API结果:', chemicalResult)
      console.log('📊 化学离子数据详情:', JSON.stringify(chemicalResult, null, 2))
      
      const metalResult = await api.getStationMetalIonData(qdwStation.id)
      console.log('🏭 金属离子API结果:', metalResult)
      console.log('📊 金属离子数据详情:', JSON.stringify(metalResult, null, 2))
      
      const biodiversityResult = await api.getStationBiodiversityData(qdwStation.id)
      console.log('🦠 生物多样性API结果:', biodiversityResult)
      console.log('📊 生物多样性数据详情:', JSON.stringify(biodiversityResult, null, 2))
      
      const biologicalResult = await api.getStationAnalysisOfBiologicalFactorsData(qdwStation.id)
      console.log('🔬 微观繁殖体API结果:', biologicalResult)
      console.log('📊 微观繁殖体数据详情:', JSON.stringify(biologicalResult, null, 2))
      
    } catch (error) {
      console.error('❌ API调用失败:', error)
    }
  } else {
    console.log('❌ 未找到QDW站点，所有可用站点:')
    mainPoint.value.forEach((station, index) => {
      console.log(`${index + 1}. ${station.name} (ID: ${station.id})`)
    })
  }
}

// 🆕 数据库直接验证工具
window.verifyDatabaseData = async () => {
  console.log('🔍 =======数据库验证工具======')
  
  try {
    // 直接调用后端API验证数据
    const testRequests = [
      { name: '水文气象', api: () => api.getStationWaterPhWeatherData(12) },
      { name: '化学离子', api: () => api.getStationChemicalIonData(12) },
      { name: '金属离子', api: () => api.getStationMetalIonData(12) },
      { name: '生物多样性', api: () => api.getStationBiodiversityData(12) },
      { name: '微观繁殖体', api: () => api.getStationAnalysisOfBiologicalFactorsData(12) },
    ]
    
    for (const test of testRequests) {
      try {
        const result = await test.api()
        console.log(`📊 ${test.name} 数据:`)
        console.log(`   - 返回状态: ${result.code === 0 ? '✅ 成功' : '❌ 失败'}`)
        console.log(`   - 数据条数: ${result.data?.length || 0}`)
        console.log(`   - 完整响应:`, result)
        
        if (result.data && result.data.length > 0) {
          console.log(`   - 第一条数据:`, result.data[0])
        }
        console.log('---')
      } catch (error) {
        console.error(`❌ ${test.name} API调用失败:`, error)
      }
    }
  } catch (error) {
    console.error('❌ 验证过程失败:', error)
  }
}

// 🆕 字段名检查工具
window.checkFieldNames = async () => {
  console.log('🔍 =======字段名检查工具======')
  
  try {
    const waterResult = await api.getStationWaterPhWeatherData(12)
    if (waterResult.data && waterResult.data.length > 0) {
      const firstRecord = waterResult.data[0]
      console.log('💧 水文气象数据第一条记录的所有字段:')
      console.log('字段列表:', Object.keys(firstRecord))
      console.log('完整数据:', firstRecord)
      
      // 检查时间字段
      console.log('🕐 时间字段检查:')
      console.log('  - createTime 存在?', 'createTime' in firstRecord ? '✅' : '❌')
      console.log('  - create_time 存在?', 'create_time' in firstRecord ? '✅' : '❌')
      console.log('  - createTime 值:', firstRecord.createTime)
      console.log('  - create_time 值:', firstRecord.create_time)
      
      // 检查其他关键字段
      console.log('🌡️ 水文字段检查:')
      console.log('  - weather:', firstRecord.weather)
      console.log('  - windDirection:', firstRecord.windDirection)
      console.log('  - wind_direction:', firstRecord.wind_direction)
      console.log('  - saltExtent:', firstRecord.saltExtent)
      console.log('  - salt_extent:', firstRecord.salt_extent)
      
      // 修复字段名检查
      console.log('🔧 尝试修复条件判断:')
      const hasCreateTime = firstRecord.createTime || firstRecord.create_time
      console.log('  - 任意时间字段存在?', hasCreateTime ? '✅' : '❌')
      
      if (hasCreateTime) {
        console.log('  - ✅ 数据应该能正常处理')
      } else {
        console.log('  - ❌ 没有时间字段，需要检查其他条件')
      }
    }
  } catch (error) {
    console.error('❌ 字段检查失败:', error)
  }
}

/**
 * 根据重金属元素和浓度判断污染状态
 */
const getPollutionStatus = (elementName, concentration) => {
  if (!concentration || concentration === '--') return '--'
  
  const value = parseFloat(concentration)
  if (isNaN(value)) return '--'
  
  // 根据不同元素的标准值判断污染状态
  const standards = {
    'Hg': 0.001,  // 汞
    'Cd': 0.001,  // 镉
    'Pb': 0.01,   // 铅
    'Cr': 0.05,   // 铬
    'As': 0.01,   // 砷
    'Cu': 0.05,   // 铜
    'Zn': 0.05,   // 锌
    'Ni': 0.05,   // 镍
    'Co': 0.05,   // 钴
    'Mn': 0.1,    // 锰
    'Fe': 0.3,    // 铁
    'Al': 0.2,    // 铝
  }
  
  const standard = standards[elementName] || 0.05
  
  if (value <= standard) return '正常'
  else if (value <= standard * 2) return '轻度'
  else if (value <= standard * 5) return '中度'
  else return '重度'
}

/**
 * 获取群落类型名称
 */
const getCommunityTypeName = (type) => {
  const typeMap = {
    0: '浮游植物',
    1: '浮游动物',
    2: '底栖生物',
    4: '游泳动物'
  }
  return typeMap[type] || '未知群落'
}

/**
 * 获取群落单位
 */
const getCommunityUnit = (type) => {
  const unitMap = {
    0: '个/L',
    1: '个/L',
    2: '个/m²',
    4: '个/网'
  }
  return unitMap[type] || '个'
}

/**
 * 获取样品类型名称
 */
const getSampleTypeName = (type) => {
  const typeMap = {
    0: '沉积物',
    1: '底层水样',
    2: '表层水样',
    3: '藻样'
  }
  return typeMap[type] || '未知样品'
}

const isLoading = ref(false)
const analysisRef = ref(null)
const eventSource = ref(null)
let hasReceivedData = false // 标记是否已收到数据

async function predictWaterInfo() {
  try {
    // 先保存坐标值，避免异步丢失
    const currentLongitude = longitude.value
    const currentLatitude = latitude.value
    const currentScaleId = route.query.scaleId

    // 发起预测请求
    const { data } = await api.predictWaterQuality(
      currentLongitude,
      currentLatitude,
      selectedInvestigationCenter.value,
      selectedRoute.value,
      selectedTime.value,
    )

    resultData.value = data
    showResultModal.value = true

    // 等待模态框渲染完成
    await nextTick()

    // 安全检查
    if (!analysisRef.value) {
      throw new Error('分析结果容器未初始化')
    }

    // 初始化图表
    initChart()

    // 开始流式分析（使用保存的坐标值）
    await startAnalysisStream(currentLongitude, currentLatitude, currentScaleId)
  }
  catch (error) {
    console.error('预测失败:', error)
    $message.error(`预测失败: ${error.message}`)
  }
  finally {
    // 最后才重置输入
    showPredictionModal.value = false
    resetInputs() // 如果确实需要重置
  }
}

async function startAnalysisStream(lng, lat, scaleId) {
  try {
    isLoading.value = true
    hasReceivedData = false

    const container = analysisRef.value
    container.textContent = ''

    const params = new URLSearchParams({
      longitude: lng,
      latitude: lat,
      scaleId: selectedInvestigationCenter.value,
      taskId: selectedRoute.value,
      times: selectedTime.value,
    })

    const { accessToken } = useAuthStore()

    eventSource.value = new EventSourcePolyfill(
      `/api/waterquality/predict/analysis-stream?${params}`,
      {
        headers: { Authorization: `Bearer ${accessToken}` },
        withCredentials: true,
      },
    )

    eventSource.value.addEventListener('open', () => {
      // 连接成功后5秒仍未收到数据显示提示
      setTimeout(() => {
        if (!hasReceivedData) {
          $message.warning('数据接收较慢，请耐心等待...')
        }
      }, 5000)
    })

    eventSource.value.onmessage = (e) => {
      if (!hasReceivedData) {
        hasReceivedData = true
        isLoading.value = false // 收到第一条数据时关闭加载状态
      }
      try {
        appendAnalysisText(e.data)
      }
      catch (error) {
        console.error('解析数据失败:', error)
      }
    }

    eventSource.value.onerror = () => {
      if (!hasReceivedData) {
        $message.error('分析服务连接失败')
      }
      isLoading.value = false
      eventSource.value?.close()
    }
  }
  catch (err) {
    isLoading.value = false
    console.error('SSE连接错误:', err)
    $message.error('启动分析失败')
    throw err
  }
}

let buffer = ''
let isRendering = false
const BATCH_SIZE = 20 // 每批渲染字符数
const DEBOUNCE_TIME = 100 // 渲染间隔时间
// 打字机效果输出
// 改进后的打字机效果
function appendAnalysisText(text) {
  buffer += text
  if (!isRendering) {
    scheduleRender()
  }
}

function scheduleRender() {
  if (buffer.length === 0) {
    isRendering = false
    return
  }

  isRendering = true
  requestAnimationFrame(() => {
    const container = analysisRef.value
    const fragment = document.createDocumentFragment()
    const charsToRender = buffer.substring(0, BATCH_SIZE)

    // 批量创建元素
    const spans = []
    for (const char of charsToRender) {
      const span = document.createElement('span')
      span.textContent = char
      span.style.cssText = `
        opacity: 0;
        animation: fadeIn 0.3s ease forwards;
        white-space: pre-wrap;
      `
      spans.push(span)
    }

    // 使用CSS动画替代JS控制
    const style = document.createElement('style')
    style.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(2px); }
        to { opacity: 1; transform: translateY(0); }
      }
    `
    fragment.appendChild(style)

    // 批量添加
    spans.forEach((span, index) => {
      span.style.animationDelay = `${index * 30}ms`
      fragment.appendChild(span)
    })

    container.appendChild(fragment)
    buffer = buffer.substring(BATCH_SIZE)

    // 优化滚动控制
    throttledScroll(container)

    // 分批渲染
    if (buffer.length > 0) {
      setTimeout(scheduleRender, DEBOUNCE_TIME)
    }
    else {
      isRendering = false
    }
  })
}

// 节流滚动函数
let lastScroll = 0
function throttledScroll(container) {
  const now = Date.now()
  if (now - lastScroll > 500) {
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth',
    })
    lastScroll = now
  }
}

onBeforeUnmount(() => {
  eventSource.value?.close()
})

// 关闭模态框时断开连接
function closeResultModal() {
  if (eventSource.value) {
    eventSource.value.close()
  }
  isLoading.value = false
  showResultModal.value = false
}

function initChart() {
  try {
    // 1. 容器检查
    const chartDom = chartRef.value
    if (!chartDom) {
      throw new Error('图表容器未找到')
    }

    // 2. 数据验证
    if (!resultData.value) {
      throw new Error('预测结果数据未加载')
    }

    // 3. 安全获取坐标值
    const getSafeCoordinate = (value) => {
      const num = Number(value)
      return Number.isNaN(num) ? '--' : num.toFixed(4)
    }

    const coordText = longitude.value !== null && latitude.value !== null
      ? `预测点位 (${getSafeCoordinate(longitude.value)}, ${getSafeCoordinate(latitude.value)})`
      : '水质预测结果'

    // 4. 安全获取数据值
    const safeData = {
      saltExtent: resultData.value.saltExtent ?? 0,
      phExtent: resultData.value.phExtent ?? 7,
      waterTemperature: resultData.value.waterTemperature ?? 0,
      transparentExtent: resultData.value.transparentExtent ?? 0,
    }

    // 5. 初始化图表实例
    const myChart = echarts.init(chartDom)

    // 6. 配置项
    const option = {
      title: {
        text: coordText,
        left: 'center',
        textStyle: {
          color: '#333',
          fontSize: 16,
          fontWeight: 'bold',
        },
        subtext: '水文参数预测值',
        subtextStyle: {
          color: '#666',
          fontSize: 12,
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params) => {
          const data = params[0]
          return `
            <div style="padding: 8px; background: #fff; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)">
              <div style="font-weight: 600; color: #333; margin-bottom: 6px">${data.name}</div>
              <div style="display: flex; justify-content: space-between">
                <span style="color: #666">预测值:</span>
                <span style="font-weight: 600; color: #2b8cbe"> ${Number(data.value).toFixed(3)} ${data.data.unit}</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-top: 4px">
                <span style="color: #666">正常范围:</span>
                <span style="color: #888">${data.data.min}-${data.data.max}${data.data.unit}</span>
              </div>
            </div>
          `
        },
      },
      grid: {
        top: '20%',
        bottom: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: ['盐度', 'pH值', '水温', '透明度'],
        axisLine: {
          lineStyle: {
            color: '#e8e8e8',
          },
        },
        axisLabel: {
          color: '#666',
          fontSize: 14,
          interval: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '数值',
        nameTextStyle: {
          color: '#666',
          padding: [0, 0, 0, 40],
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#f0f0f0',
          },
        },
        axisLabel: {
          color: '#999',
        },
      },
      series: [
        {
          name: '预测值',
          type: 'bar',
          barWidth: '50%',
          data: [
            {
              value: safeData.saltExtent,
              name: '盐度',
              unit: 'PSU',
              min: 0,
              max: 40,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#7bccc4' },
                  { offset: 1, color: '#4a9d9c' },
                ]),
              },
            },
            {
              value: safeData.phExtent,
              name: 'pH值',
              unit: '',
              min: 0,
              max: 14,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#f03b20' },
                  { offset: 1, color: '#c92a2a' },
                ]),
              },
            },
            {
              value: safeData.waterTemperature,
              name: '水温',
              unit: '℃',
              min: 0,
              max: 30,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#fd8d3c' },
                  { offset: 1, color: '#f76808' },
                ]),
              },
            },
            {
              value: safeData.transparentExtent,
              name: '透明度',
              unit: '米',
              min: 0,
              max: 5,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#2c7fb8' },
                  { offset: 1, color: '#1a5f8e' },
                ]),
              },
            },
          ],
          label: {
            show: true,
            position: 'top',
            formatter: ({ value }) => value.toFixed(2),
            color: '#333',
            fontSize: 12,
          },
          itemStyle: {
            borderRadius: [4, 4, 0, 0],
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 4,
            shadowOffsetY: 2,
          },
        },
      ],
      graphic: {
        type: 'group',
        right: 20,
        top: 10,
        children: [
          {
            type: 'circle',
            shape: { r: 6 },
            style: { fill: '#2b8cbe' },
          },
          {
            type: 'text',
            left: 10,
            style: {
              text: '预测值',
              fill: '#666',
              fontSize: 12,
            },
          },
        ],
      },
    }

    // 7. 渲染图表
    myChart.setOption(option)

    // 8. 响应式适配
    const resizeHandler = () => myChart.resize()
    window.addEventListener('resize', resizeHandler)

    // 9. 清理监听器
    onBeforeUnmount(() => {
      window.removeEventListener('resize', resizeHandler)
      myChart.dispose()
    })

    return myChart
  }
  catch (error) {
    console.error('图表初始化失败:', error)
    $message.error('结果展示初始化失败')
    return null
  }
}

function resetInputs() {
  longitude.value = null
  latitude.value = null
}

// 在script setup顶部添加工具函数
function convexHull(points) {
  if (points.length <= 3)
    return points

  points = Array.from(new Set(points.map(JSON.stringify))).map(JSON.parse)
  points.sort((a, b) => a[0] - b[0] || a[1] - b[1])

  const lower = []
  for (const p of points) {
    while (lower.length >= 2 && cross(lower[lower.length - 2], lower[lower.length - 1], p) <= 0) {
      lower.pop()
    }
    lower.push(p)
  }

  const upper = []
  for (let i = points.length - 1; i >= 0; i--) {
    const p = points[i]
    while (upper.length >= 2 && cross(upper[upper.length - 2], upper[upper.length - 1], p) <= 0) {
      upper.pop()
    }
    upper.push(p)
  }

  lower.pop()
  upper.pop()
  return lower.concat(upper)
}

function cross(o, a, b) {
  return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])
}

// 完整的createPolygon函数
function createPolygon(AMap) {
  const points = stationPointDistribute.value.map(pos => [pos[0], pos[1]])
  const hull = convexHull(points)

  polygon.value = new AMap.Polygon({
    path: hull,
    fillColor: '#ccebc5',
    strokeColor: 'rgba(43, 140, 190, 0.2)', // ✅ 低透明度颜色
    strokeWeight: 1, // 调整边线粗细
    fillOpacity: 0.4,
    strokeStyle: 'dashed',
    strokeDasharray: [2, 5], // 更细的虚线
    strokeOpacity: 0.2, // ✅ 设置透明度（0-1）
  })

  // 修改后的多边形点击事件处理
  polygon.value.on('click', (e) => {
    // 使用自定义属性而不是直接修改 originalEvent
    const event = {
      ...e,
      _processed: true, // 使用自定义属性标记已处理
    }

    const lnglat = event.lnglat || event.target.getPosition()
    longitude.value = Number(lnglat.getLng().toFixed(6))
    latitude.value = Number(lnglat.getLat().toFixed(6))
    showPredictionModal.value = true

    // 阻止事件冒泡到地图
    if (event.cancelable) {
      event.stopPropagation()
    }
  })

  map.value.add(polygon.value)
}

async function handleMapClick(e) {
  const AMap = window.AMap
  const lnglat = e.lnglat || e.target.getPosition()
  const clickPoint = [lnglat.getLng(), lnglat.getLat()]

  // 检查是否在凸包内部
  if (polygon.value) {
    const polygonPath = polygon.value.getPath().map(p => [p.lng, p.lat])

    if (polygonPath.length >= 3 && AMap.GeometryUtil.isPointInRing(clickPoint, polygonPath)) {
      // 在区域内，自动填充预测坐标
      longitude.value = Number(clickPoint[0].toFixed(6))
      latitude.value = Number(clickPoint[1].toFixed(6))
      showPredictionModal.value = true
      return
    }
  }

  // 在区域外，允许添加站点
  modalForm.longitude = Number(clickPoint[0].toFixed(6))
  modalForm.latitude = Number(clickPoint[1].toFixed(6))
  modalForm.name = null
  modalForm.id = null
  modalForm.scaleId = selectedInvestigationCenter.value
  modalForm.taskId = selectedRoute.value
  modalForm.taskName = routes.value.find(x => x.id === selectedRoute.value)?.name || null
  type.value = 1
  modalVisible.value = true
  $message.success('已获取该点坐标,请添加站点信息')

  if (isDrawingRoute.value) {
    const lnglat = e.lnglat || e.target.getPosition()
    const clickPoint = [lnglat.getLng(), lnglat.getLat()]

    // 查找最近的站点
    const nearestStation = findNearestStation(clickPoint)

    if (!nearestStation) {
      $message.warning('请点击已有的站点')
      return
    }

    if (!routeStartPoint.value) {
      // 设置起点
      routeStartPoint.value = nearestStation
      $message.info(`已选择 ${nearestStation.name} 作为起点，请选择终点`)
    }
    else {
      // 设置终点并创建路径
      const AMap = window.AMap
      createDirectedEdge(routeStartPoint.value, nearestStation, AMap)

      $message.success(`已创建从 ${routeStartPoint.value.name} 到 ${nearestStation.name} 的航线`)
      routeStartPoint.value = null
      isDrawingRoute.value = false
    }
  }
}

// 查找最近的站点
function findNearestStation(point) {
  const AMap = window.AMap
  if (!AMap || !mainPoint.value.length)
    return null

  let minDistance = Infinity
  let nearestStation = null

  mainPoint.value.forEach((station) => {
    const stationPoint = new AMap.LngLat(station.longitude, station.latitude)
    const clickPoint = new AMap.LngLat(point[0], point[1])
    const distance = AMap.GeometryUtil.distance(stationPoint, clickPoint)

    if (distance < minDistance && distance < 0.01) { // 0.01度约1公里
      minDistance = distance
      nearestStation = station
    }
  })

  return nearestStation
}

// 创建有向边
function createDirectedEdge(from, to, AMap) {
  // 检查是否已存在相同路径
  const exists = directedEdges.value.some(edge =>
    edge.from === from.id && edge.to === to.id,
  )

  if (exists) {
    $message.warning('该路径已存在')
    return
  }

  // 添加新路径
  directedEdges.value.push({
    from: from.id,
    to: to.id,
    fromPos: [from.longitude, from.latitude],
    toPos: [to.longitude, to.latitude],
    createTime: new Date().toISOString(),
  })

  // 重新绘制所有路径
  drawAllRoutes(AMap)
}

async function handleSubmit() {
  try {
    await modalFormRef.value?.validate()

    const longitude = Number(modalForm.longitude)
    const latitude = Number(modalForm.latitude)

    if (Number.isNaN(longitude) || Number.isNaN(latitude)) {
      throw new TypeError('经纬度必须是有效数字')
    }

    const prefix = generatePrefix(modalForm.activeTypes)
    const fullName = prefix + modalForm.name

    if (type.value === 1) {
      const response = await api.createDistribute({
        taskName: modalForm.taskName,
        name: fullName, // 使用拼接后的名称
        scaleId: modalForm.scaleId,
        wpActivities: modalForm.activeTypes.includes('wp'),
        ciActivities: modalForm.activeTypes.includes('ci'),
        mbActivities: modalForm.activeTypes.includes('mb'),
        mrActivities: modalForm.activeTypes.includes('mr'),
        longitude,
        latitude,
      })

      if (response.code !== 0) {
        throw new Error(response.message || 'API请求失败')
      }
      await getPoints()
      loadMap()
      $message.success('添加站点成功')
    }
    else {
      const response = await api.updateDistribute({
        ...modalForm,
        longitude,
        latitude,
        scaleId: route.query.scaleId,
      })
      if (response.code !== 0) {
        throw new Error(response.message || 'API请求失败')
      }
      await getPoints()
      loadMap()
      $message.success('修改站点成功')
    }

    const AMap = window.AMap || await AMapLoader.load({
      key: '3b4146d6e0e773ad45d905f20e1a3070',
      version: '2.0',
    })

    const newPos = new AMap.LngLat(longitude, latitude)
    const markerName = modalForm.name

    if (type.value === 1) {
      const infoWindow = new AMap.InfoWindow({
        content: `
          <div class="custom-info-window">
            <div class="title">${markerName}</div>
            <div class="coord">经度: ${longitude.toFixed(6)}</div>
            <div class="coord">纬度: ${latitude.toFixed(6)}</div>
          </div>
        `,
        offset: new AMap.Pixel(-6, -30),
      })

      const newMarker = new AMap.Marker({
        position: newPos,
        content: createMarkerContent(markerName),
        anchor: 'bottom-center',
      })

      newMarker.on('click', () => {
        infoWindow.open(map.value, newPos)
        Object.assign(modalForm, {
          longitude,
          latitude,
          name: markerName,

        })
        modalVisible.value = true
      })

      try {
        map.value.add(newMarker)
        markers.value.push(newMarker)
      }
      catch (addError) {
        console.error('标记添加失败:', addError)
        throw new Error('标记添加失败，请手动刷新查看')
      }
    }
    else {
      const targetMarker = markers.value.find((marker) => {
        const pos = marker.getPosition()
        return pos && pos.lng === originalLng.value && pos.lat === originalLat.value
      })

      if (targetMarker) {
        targetMarker.setPosition(newPos)

        const newContent = `
          <div class="custom-info-window">
            <div class="title">${markerName}</div>
            <div class="coord">经度: ${longitude.toFixed(6)}</div>
            <div class="coord">纬度: ${latitude.toFixed(6)}</div>
          </div>
        `

        if (targetMarker.infoWindow) {
          targetMarker.infoWindow.close()
        }

        targetMarker.infoWindow = new AMap.InfoWindow({
          content: newContent,
          offset: new AMap.Pixel(-6, -30),
        })

        targetMarker.setContent(createMarkerContent(markerName))

        targetMarker.off('click')
        targetMarker.on('click', () => {
          targetMarker.infoWindow.open(map.value, newPos)
          Object.assign(modalForm, {
            longitude,
            latitude,
            name: markerName,
          })
          modalVisible.value = true
        })
      }
      else {
        throw new Error('未找到要修改的标记')
      }
    }

    updatePolygonPath()

    modalFormRef.value?.restoreValidation()
    Object.assign(modalForm, {
      name: '',
      longitude: null,
      latitude: null,
    })
    return true
  }
  catch (error) {
    $message.error(error.message || '操作失败')
    console.error('提交错误详情:', error)
    return false
  }
}

function createMarkerContent(name) {
  return `
    <div class="custom-marker">
      <div class="label">
        <span class="icon">📍</span>
        <span class="text">${name}</span>
      </div>
      <div class="pin"></div>
    </div>
  `
}

function updatePolygonPath() {
  if (!polygon.value || !markers.value.length)
    return

  const newPath = markers.value.map((marker) => {
    const pos = marker.getPosition()
    return [pos.getLng(), pos.getLat()]
  })

  const hull = convexHull(newPath)
  polygon.value.setPath(hull)
}

async function loadMap() {
  console.log('🗺️ 开始加载地图...')
  console.log('📍 当前地图数据：', {
    调查中心: stationPointScale.value,
    站点数量: stationPointDistribute.value?.length || 0,
    站点坐标: stationPointDistribute.value,
    站点详情: mainPoint.value
  })
  
  try {
    const AMap = await AMapLoader.load({
      key: '3b4146d6e0e773ad45d905f20e1a3070',
      version: '2.0',
      plugins: ['AMap.Polygon', 'AMap.Marker', 'AMap.InfoWindow', 'AMap.GeometryUtil'],
    })

    console.log('✅ AMap加载成功')

    // 如果地图已存在，先销毁
    if (map.value) {
      console.log('🗑️ 销毁现有地图实例')
      map.value.destroy()
    }

    // 🔥 修改：地图初始化时使用默认中心点，不使用调查中心坐标
    const defaultCenter = [120.0, 36.0]  // 山东青岛附近的默认中心点
    
    map.value = new AMap.Map('map-container', {
      viewMode: '2D',
      zoom: 8,  // 降低初始缩放级别，显示更大范围
      center: defaultCenter,
      mapStyle: 'amap://styles/normal',  // 使用高德标准底图
      resizeEnable: true,
      rotateEnable: true,
      pitchEnable: true,
      zoomEnable: true,
      dragEnable: true
    })

    console.log('✅ 地图实例创建成功，默认中心：', defaultCenter)

    map.value.on('click', (e) => {
      if (e._processed)
        return
      handleMapClick(e)
    })

    // 🔥 移除：不在地图初始化时创建调查中心标记
    // createCenterMarker(AMap)
    // console.log('✅ 调查中心标记创建完成')

    // 🔥 新增：如果有调查中心坐标，创建调查中心标记
    if (stationPointScale.value && stationPointScale.value.length === 2 && 
        typeof stationPointScale.value[0] === 'number' && typeof stationPointScale.value[1] === 'number') {
      console.log('📍 在地图重新加载时创建调查中心标记，坐标：', stationPointScale.value)
      createCenterMarker(AMap)
      console.log('✅ 调查中心标记重新创建完成')
    }

    // 仅在有站点数据时才创建标记和多边形
    if (stationPointDistribute.value && stationPointDistribute.value.length > 0) {
      console.log(`📍 开始创建${stationPointDistribute.value.length}个站点标记`)
      createMarkers(AMap)
      console.log('✅ 站点标记创建完成')
      
      createPolygon(AMap)
      console.log('✅ 多边形创建完成')
      
      // 重新绘制航线
      drawAllRoutes(AMap)
      console.log('✅ 航线绘制完成')
      
      // 🔥 延迟调整地图视野，确保所有元素都已加载完成
      setTimeout(() => {
      if (stationPointScale.value && stationPointScale.value.length === 2) {
          console.log('🔧 延迟调整地图视野包含调查中心和站点')
        adjustMapView()
      }
      }, 200)  // 延迟200ms确保标记和航线都已绘制完成
    } else {
      console.warn('⚠️ 没有站点数据，跳过标记和航线创建')
    }
    
    console.log('🎯 地图加载完成')
    
  } catch (error) {
    console.error('❌ 地图加载失败：', error)
    $message.error(`地图加载失败: ${error.message}`)
  }
}

function createCenterMarker(AMap) {
  const centerPos = stationPointScale.value || [119.366892, 34.760023] // 默认值
  const centerName = route.query.scaleName || '调查中心'

  console.log('🏢 创建调查中心标记，详细信息：', {
    位置: centerPos,
    名称: centerName,
    坐标类型: typeof centerPos[0]
  })

  // 🔥 优化调查中心标记样式，使其更明显
  const content = `
    <div class="custom-marker center-marker" style="z-index: 1000;">
      <div class="label" style="
        background: linear-gradient(135deg, #FF6B6B, #FF8E53);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
        border: 2px solid white;
        font-weight: bold;
        font-size: 14px;
        position: relative;
        z-index: 1001;
        min-width: 100px;
        text-align: center;
      ">
        <span class="icon" style="margin-right: 6px; font-size: 16px;">🏢</span>
        <span class="text">${centerName}</span>
      </div>
      <div class="pin" style="
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 12px solid #FF6B6B;
        margin: 0 auto;
        position: relative;
        z-index: 1000;
      "></div>
    </div>
  `

  const infoWindow = new AMap.InfoWindow({
    content: `
      <div class="custom-info-window center-info">
        <div class="title" style="color: #FF6B6B; font-weight: bold; font-size: 16px;">${centerName}</div>
        <div class="coord" style="margin-top: 8px;">经度: ${(centerPos[0] || 0).toFixed(6)}</div>
        <div class="coord">纬度: ${(centerPos[1] || 0).toFixed(6)}</div>
      </div>
    `,
    offset: new AMap.Pixel(-6, -30),
  })

  centerMarker.value = new AMap.Marker({
    position: centerPos,
    content,
    anchor: 'bottom-center',
    zIndex: 1000,  // 🔥 设置高z-index确保在最上层
  })

  centerMarker.value.on('click', () => {
    console.log('📍 调查中心标记被点击')
    infoWindow.open(map.value, centerPos)
  })

  map.value.add(centerMarker.value)
  console.log('✅ 调查中心标记已添加到地图，位置：', centerPos)
}

// 🆕 调整地图视野以包含所有点
function adjustMapView() {
  if (!map.value || !window.AMap) {
    console.warn('⚠️ 地图实例或AMap未初始化')
    return
  }

  console.log('🎯 开始调整地图视野...')
  
  const allCoordinates = []
  
  // 添加调查中心坐标
  if (stationPointScale.value && stationPointScale.value.length === 2) {
    allCoordinates.push({
      lng: stationPointScale.value[0],
      lat: stationPointScale.value[1],
      type: '调查中心'
    })
    console.log('📍 添加调查中心坐标：', stationPointScale.value)
  }
  
  // 添加所有站点坐标
  if (stationPointDistribute.value && stationPointDistribute.value.length > 0) {
    stationPointDistribute.value.forEach((pos, index) => {
      if (pos && pos.length === 2) {
        const stationName = mainPoint.value?.[index]?.name || `站点${index + 1}`
        allCoordinates.push({
          lng: pos[0],
          lat: pos[1],
          type: '监测站点',
          name: stationName
        })
      }
    })
    console.log('📍 添加站点坐标数量：', stationPointDistribute.value.length)
  }
  
  if (allCoordinates.length === 0) {
    console.warn('⚠️ 没有有效的坐标，无法调整地图视野')
    $message.warning('没有可用的位置信息，请先选择调查数据')
    return
  }
  
  console.log('📊 所有坐标点：', allCoordinates)
  
    try {
    // 计算边界范围
    const lngs = allCoordinates.map(coord => coord.lng)
    const lats = allCoordinates.map(coord => coord.lat)
    
    const minLng = Math.min(...lngs)
    const maxLng = Math.max(...lngs)
    const minLat = Math.min(...lats)
    const maxLat = Math.max(...lats)
    
    console.log('📐 计算边界范围：', {
      经度范围: [minLng, maxLng],
      纬度范围: [minLat, maxLat],
      经度跨度: maxLng - minLng,
      纬度跨度: maxLat - minLat
    })
    
    // 计算中心点
    const centerLng = (minLng + maxLng) / 2
    const centerLat = (minLat + maxLat) / 2
    
    // 计算合适的缩放级别
    const lngSpan = maxLng - minLng
    const latSpan = maxLat - minLat
    const maxSpan = Math.max(lngSpan, latSpan)
    
    let zoom = 10  // 默认缩放级别
    
    if (maxSpan > 2) {
      zoom = 8   // 大范围
    } else if (maxSpan > 1) {
      zoom = 9   // 中等范围  
    } else if (maxSpan > 0.5) {
      zoom = 10  // 较小范围
    } else if (maxSpan > 0.1) {
      zoom = 12  // 小范围
    } else if (maxSpan > 0.05) {
      zoom = 13  // 很小范围
    } else {
      zoom = 14  // 极小范围
    }
    
    // 如果只有一个点（通常是调查中心），使用更高的缩放级别
    if (allCoordinates.length === 1) {
      zoom = 15
      console.log('📍 只有一个坐标点，使用高缩放级别')
    }
    
    console.log('🎯 计算地图参数：', {
      中心点: [centerLng, centerLat],
      缩放级别: zoom,
      最大跨度: maxSpan,
      点的数量: allCoordinates.length
      })
      
    // 使用setZoomAndCenter方法进行平滑的视野调整
    map.value.setZoomAndCenter(zoom, [centerLng, centerLat], false, 1000)  // 1秒动画
      
    console.log('✅ 地图视野调整完成')
    
    // 延迟显示成功消息，避免干扰用户操作
    setTimeout(() => {
      $message.success(`地图已调整到最佳视野 (${allCoordinates.length}个位置点)`, { duration: 2000 })
    }, 1200)
    
    } catch (error) {
      console.error('❌ 调整地图视野失败：', error)
    $message.error('地图视野调整失败')
  }
}

function createMarkers(AMap) {
  console.log('📍 开始创建站点标记，站点数据：', {
    坐标数量: stationPointDistribute.value?.length || 0,
    详情数量: mainPoint.value?.length || 0,
    坐标数据: stationPointDistribute.value,
    详情数据: mainPoint.value
  })
  
  if (!stationPointDistribute.value || stationPointDistribute.value.length === 0) {
    console.warn('⚠️ 没有站点坐标数据')
    return
  }

  if (!mainPoint.value || mainPoint.value.length === 0) {
    console.warn('⚠️ 没有站点详情数据')
    return
  }

  if (stationPointDistribute.value.length !== mainPoint.value.length) {
    console.warn('⚠️ 站点坐标与详情数量不匹配：', {
      坐标数量: stationPointDistribute.value.length,
      详情数量: mainPoint.value.length
    })
    return
  }

  // 清除现有标记
  if (markers.value && markers.value.length > 0) {
    console.log('🗑️ 清除现有标记')
    markers.value.forEach(marker => {
      if (map.value && marker) {
        map.value.remove(marker)
      }
    })
    markers.value = []
  }

  markers.value = stationPointDistribute.value.map((pos, index) => {
    const stationInfo = mainPoint.value[index]
    
    if (!stationInfo) {
      console.warn(`⚠️ 站点 ${index} 缺少详情数据`)
      return null
    }
    
    console.log(`📍 创建站点标记 ${index + 1}：`, {
      名称: stationInfo.name,
      坐标: pos,
      详情: stationInfo
    })

    try {
    const marker = new AMap.Marker({
      position: pos,
        content: createMarkerContent(stationInfo.name),
      anchor: 'bottom-center',
    })

    const infoWindow = new AMap.InfoWindow({
      content: `
        <div class="custom-info-window">
            <div class="title">${stationInfo.name}</div>
          <div class="coord">经度: ${pos[0].toFixed(6)}</div>
          <div class="coord">纬度: ${pos[1].toFixed(6)}</div>
        </div>
      `,
      offset: new AMap.Pixel(-6, -30),
    })

    marker.on('click', async () => {
      console.log('📍 站点标记被点击：', stationInfo.name, stationInfo.id)
      console.log('🔍 站点完整信息：', stationInfo)
      
      try {
        // 🔥 从后端API获取站点的完整详细数据
        selectedStation.value = stationInfo
        
        // 🆕 添加详细的API调试信息
        console.log('🚀 开始调用API获取站点详细数据，传递的station ID:', stationInfo.id)
        
        // 异步加载站点详细数据
        await loadStationDetailData(stationInfo.id)
        
      DrawerVisible.value = true // 显示抽屉
      modalForm.longitude = pos[0]
      modalForm.latitude = pos[1]
        modalForm.name = stationInfo.name
        modalForm.id = stationInfo.id
      originalLng.value = pos[0]
      originalLat.value = pos[1]
      type.value = 2
      infoWindow.open(map.value, pos)
      } catch (error) {
        console.error('❌ 加载站点详细数据失败：', error)
        $message.error(`加载站点数据失败: ${error.message}`)
      }
    })

    map.value.add(marker)
      console.log(`✅ 站点标记 ${index + 1} 添加到地图成功`)
    return marker
      
    } catch (error) {
      console.error(`❌ 创建站点标记 ${index + 1} 失败：`, error)
      return null
    }
  }).filter(marker => marker !== null)
  
  console.log(`🎯 站点标记创建完成，共${markers.value.length}个标记`)
}

onBeforeUnmount(() => {
  if (map.value) {
    map.value.off('click', handleMapClick)
    map.value.destroy()
  }
})

const mainWidth = ref(1591)
const mainHeight = ref(360)

// 用户引导系统
const showGuideModal = ref(false)
const showGuideHighlight = ref(false)
const currentGuideStepIndex = ref(0)
const guideTooltipStyle = ref({})

// 引导步骤配置
const guideSteps = ref([
  {
    title: '选择调查中心',
    description: '首先选择"海岸带调查1"来查看该区域的站点分布',
    target: '.filter-container .n-select:first-child',
    buttonText: '下一步',
    action: () => {
      // 自动选择海岸带调查1
      const coastalInvestigation = investigationCenters.value.find(center => center.name === '海岸带调查1')
      if (coastalInvestigation) {
        selectedInvestigationCenter.value = coastalInvestigation.id
      }
    }
  },
  {
    title: '选择航线',
    description: '选择"海岸带调查1-航线1"来查看具体的监测航线',
    target: '.filter-container .n-select:nth-child(2)',
    buttonText: '下一步',
    action: () => {
      // 延迟选择航线，等待数据加载
      setTimeout(() => {
        if (routes.value.length > 0) {
          selectedRoute.value = routes.value[0].id
        }
      }, 500)
    }
  },
  {
    title: '选择调查次数',
    description: '选择"第1次调查"来查看具体的监测数据',
    target: '.filter-container .n-select:nth-child(3)',
    buttonText: '下一步',
    action: () => {
      // 延迟选择调查次数，等待数据加载
      setTimeout(() => {
        if (times.value.length > 0) {
          selectedTime.value = times.value[0].id
        }
      }, 500)
    }
  },
  {
    title: '应用筛选',
    description: '点击"筛选"按钮来加载选定条件下的站点数据',
    target: '.filter-container .n-button',
    buttonText: '下一步',
    action: () => {
      // 自动点击筛选按钮
      setTimeout(() => {
        applyFilters()
      }, 500)
    }
  },
  {
    title: '地图视野调整',
    description: '系统会自动调整地图到最佳视野。您也可以点击"🗺️ 定位视野"按钮重新调整视野',
    target: '.filter-container .n-button:last-child',
    buttonText: '下一步',
    action: () => {
      // 高亮定位视野按钮
      setTimeout(() => {
        const viewButton = document.querySelector('.filter-container .n-button:last-child')
        if (viewButton) {
          viewButton.classList.add('guide-highlight-element')
        }
      }, 500)
    }
  },
  {
    title: '点击QDW站点',
    description: '在地图上点击"QDW"站点标记，查看该站点的详细监测数据',
    target: '.custom-marker',
    buttonText: '我知道了',
    action: () => {
      // 高亮QDW站点
      setTimeout(() => {
        const qdwMarker = document.querySelector('.custom-marker')
        if (qdwMarker) {
          qdwMarker.classList.add('guide-highlight-element')
          // 添加脉冲动画提示
          qdwMarker.style.animation = 'pulseHighlight 2s infinite'
        }
      }, 1000)
    }
  }
])

const currentGuideStep = computed(() => {
  return guideSteps.value[currentGuideStepIndex.value] || {}
})

// 🆕 初始化调查中心坐标
async function initCenterCoordinates() {
  console.log('🔍 开始初始化调查中心坐标')
  
  // 从URL query 获取调查中心ID
  const scaleId = route.query.scaleId || selectedInvestigationCenter.value
  
  if (!scaleId) {
    console.warn('⚠️ 没有调查中心ID，使用默认坐标')
    stationPointScale.value = [119.366892, 34.760023]
    return
  }
  
  console.log('📍 获取调查中心坐标，ID：', scaleId)
  
  try {
    // 🔥 从spacial-scale接口获取正确的调查中心坐标
    const scaleResponse = await api.readScale()
    console.log('📊 spacial-scale API响应：', scaleResponse)
    
    if (scaleResponse.data && scaleResponse.data.records && scaleResponse.data.records.length > 0) {
      // 查找匹配的调查中心
      const scaleData = scaleResponse.data.records.find(record => 
        record.id === parseInt(scaleId)
      )
      
      if (scaleData && scaleData.longitude && scaleData.latitude) {
        stationPointScale.value = [scaleData.longitude, scaleData.latitude]
        console.log('✅ 成功获取调查中心坐标：', {
          调查中心: scaleData.name,
          坐标: stationPointScale.value,
          数据: scaleData
        })
      } else {
        console.warn('⚠️ 未找到匹配的调查中心数据，使用默认坐标')
        stationPointScale.value = [119.366892, 34.760023]
      }
    } else {
      console.warn('⚠️ spacial-scale响应数据为空，使用默认坐标')
      stationPointScale.value = [119.366892, 34.760023]
    }
  } catch (error) {
    console.error('❌ 获取调查中心坐标失败：', error)
    stationPointScale.value = [119.366892, 34.760023]
  }
}

// 用户引导系统方法
function checkFirstVisit() {
  const hasVisited = localStorage.getItem('coastal-biohazard-guide-completed')
  if (!hasVisited) {
    showGuideModal.value = true
  }
}

function closeGuide() {
  showGuideModal.value = false
  localStorage.setItem('coastal-biohazard-guide-completed', 'true')
}

function startGuide() {
  showGuideModal.value = false
  showGuideHighlight.value = true
  currentGuideStepIndex.value = 0
  updateGuideTooltipPosition()
}

function nextGuideStep() {
  if (currentGuideStepIndex.value < guideSteps.value.length - 1) {
    // 执行当前步骤的动作
    if (currentGuideStep.value.action) {
      currentGuideStep.value.action()
    }
    
    // 延迟进入下一步，确保动作完成
    setTimeout(() => {
      currentGuideStepIndex.value++
      updateGuideTooltipPosition()
    }, 800)
  } else {
    skipGuide()
  }
}

function skipGuide() {
  showGuideHighlight.value = false
  currentGuideStepIndex.value = 0
  localStorage.setItem('coastal-biohazard-guide-completed', 'true')
}

function restartGuide() {
  localStorage.removeItem('coastal-biohazard-guide-completed')
  showGuideModal.value = true
}

function updateGuideTooltipPosition() {
  nextTick(() => {
    const targetSelector = currentGuideStep.value.target
    if (!targetSelector) return

    const targetElement = document.querySelector(targetSelector)
    if (!targetElement) {
      console.warn('Guide target element not found:', targetSelector)
      return
    }

    const rect = targetElement.getBoundingClientRect()
    const windowHeight = window.innerHeight
    const windowWidth = window.innerWidth
    
    // 计算工具提示位置
    const tooltipWidth = 300
    const tooltipHeight = 120
    
    let top = rect.bottom + 10
    let left = rect.left + (rect.width / 2) - (tooltipWidth / 2)
    
    // 边界检查
    if (top + tooltipHeight > windowHeight) {
      top = rect.top - tooltipHeight - 10
    }
    
    if (left < 10) {
      left = 10
    } else if (left + tooltipWidth > windowWidth - 10) {
      left = windowWidth - tooltipWidth - 10
    }
    
    guideTooltipStyle.value = {
      position: 'fixed',
      top: `${top}px`,
      left: `${left}px`,
      zIndex: 9999
    }
    
    // 高亮目标元素
    highlightTargetElement(targetElement)
  })
}

function highlightTargetElement(element) {
  // 移除之前的高亮
  document.querySelectorAll('.guide-highlight-element').forEach(el => {
    el.classList.remove('guide-highlight-element')
  })
  
  // 添加高亮类
  element.classList.add('guide-highlight-element')
}

onMounted(async () => {
  if (window.innerWidth) {
    mainWidth.value = window.innerWidth
  }
  if (window.innerHeight) {
    mainHeight.value = window.innerHeight
  }
  getStationOption()
  await getScaleList()
  
  // 🔥 移除：不再在初始化时设置调查中心坐标
  // await initCenterCoordinates()
  
  // 🔥 修改：getPoints()在没有选择筛选条件时不会获取数据，所以这里先不调用
  // await getPoints()
  
  // 直接加载地图，使用默认中心点
  loadMap()
  
  // 检查是否是首次访问，显示引导
  setTimeout(() => {
    checkFirstVisit()
  }, 1000)
})

</script>

<style>
/* 流动动画替代闪烁 */
@keyframes flow {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -30; /* 根据虚线样式调整 */
  }
}
</style>

<style scoped>
/* 用户引导系统样式 */
.guide-content {
  padding: 20px 0;
}

.guide-step {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #2080f0;
}

.guide-icon {
  font-size: 32px;
  margin-right: 16px;
  flex-shrink: 0;
}

.guide-text h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.guide-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.guide-highlight {
  background: linear-gradient(135deg, #2080f0, #18a058);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.guide-highlight p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.guide-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 引导遮罩层 */
.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  pointer-events: none;
}

.guide-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  pointer-events: auto;
}

.guide-tooltip {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  padding: 16px;
  width: 300px;
  max-width: 90vw;
  pointer-events: auto;
  z-index: 9999;
}

.guide-tooltip-content h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.guide-tooltip-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.guide-tooltip-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.guide-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

/* 高亮目标元素 */
:deep(.guide-highlight-element) {
  position: relative;
  z-index: 9999 !important;
  box-shadow: 0 0 0 4px rgba(32, 128, 240, 0.5) !important;
  border-radius: 4px !important;
  background: rgba(32, 128, 240, 0.1) !important;
}

/* 引导动画效果 */
.guide-tooltip {
  animation: guideSlideIn 0.3s ease-out;
}

@keyframes guideSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 增强站点标记的视觉效果 */
:deep(.custom-marker) {
  transition: all 0.3s ease;
}

:deep(.custom-marker:hover) {
  transform: scale(1.1);
  filter: brightness(1.1);
}

/* 引导提示中的站点标记高亮 */
:deep(.guide-highlight-element.custom-marker) {
  animation: pulseHighlight 2s infinite;
}

@keyframes pulseHighlight {
  0%, 100% {
    box-shadow: 0 0 0 4px rgba(32, 128, 240, 0.5);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(32, 128, 240, 0.3);
  }
}

/* 功能引导按钮样式 */
:deep(.n-button--info-type.n-button--ghost-type) {
  border-color: #2080f0;
  color: #2080f0;
  position: relative;
  overflow: hidden;
}

:deep(.n-button--info-type.n-button--ghost-type:hover) {
  background-color: rgba(32, 128, 240, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(32, 128, 240, 0.3);
}

:deep(.n-button--info-type.n-button--ghost-type::before) {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

:deep(.n-button--info-type.n-button--ghost-type:hover::before) {
  left: 100%;
}

/* 缩放动画 */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
.analysis-container {
  min-height: 300px;
}

/* 打字机效果优化 */
.analysis-container span {
  display: inline-block;
  white-space: pre-wrap;
  word-break: break-word;
}
.station-card {
  --header-bg: #f8fafc;
  --border-color: #e2e8f0;
}

.header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.title {
  margin: 0;
  font-size: 18px;
  color: #1e293b;
}

.coord {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  padding: 0 16px 16px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.data-item:hover {
  transform: translateY(-2px);
}

.data-content {
  flex: 1;
}

label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.data-value {
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.unit {
  font-size: 14px;
  color: #94a3b8;
  margin-left: 4px;
}

.ph-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 8px;
}

.ph-tag.alkaline {
  background: #f8d7da;
  color: #721c24;
}

.ph-tag.neutral {
  background: #d4edda;
  color: #155724;
}

.ph-tag.acidic {
  background: #fff3cd;
  color: #856404;
}
#map-container {
  width: 100%;
  height: 100vh;
}

.custom-content-marker {
  position: relative;
  width: 25px;
  height: 34px;
}

.custom-content-marker img {
  width: 100%;
  height: 100%;
}

.custom-content-marker .close-btn {
  position: absolute;
  top: -6px;
  right: -8px;
  width: 15px;
  height: 15px;
  font-size: 12px;
  background: #ccc;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  line-height: 15px;
  box-shadow: -1px 1px 1px rgba(10, 10, 10, 0.2);
}

.custom-content-marker .close-btn:hover {
  background: #666;
}

::v-global(#map-container) {
  width: 100%;
  height: 100vh;
}

::v-global(.custom-marker) {
  position: relative;
  text-align: center;
}

::v-global(.custom-marker .label) {
  display: flex;
  align-items: center;
  background: white;
  padding: 4px 8px 4px 6px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  transform: translateX(-50%);
  font-size: 14px;
  line-height: 1;
}

::v-global(.custom-marker .label .icon) {
  margin-right: 4px;
  font-size: 16px;
}

::v-global(.custom-marker .label .text) {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

::v-global(.custom-marker .pin) {
  width: 20px;
  height: 20px;
  background: #2b8cbe;
  border-radius: 50% 50% 50% 0;
  transform: rotate(-45deg) translateX(-50%);
  margin: -10px auto 0;
}

::v-global(.center-marker .pin) {
  background: #ff6b6b;
}

::v-global(.custom-info-window .title) {
  font-weight: bold;
  margin-bottom: 4px;
  color: #2b8cbe;
}

::v-global(.custom-info-window .coord) {
  font-size: 12px;
  color: #666;
}

::v-global(.custom-marker .info-icon) {
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 12px;
  background: white;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

::v-global(.amap-polygon) {
  stroke-dasharray: 5 5;
  stroke-linejoin: round;
  animation: dash 30s linear infinite;
}

@keyframes dash {
  from {
    stroke-dashoffset: 100;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* AI分析结果模态框样式 */
::v-global(.ai-analysis-modal) {
  z-index: 10000 !important;
}

::v-global(.ai-analysis-content) {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 16px;
}

::v-global(.ai-analysis-content::-webkit-scrollbar) {
  width: 6px;
}

::v-global(.ai-analysis-content::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

::v-global(.ai-analysis-content::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

::v-global(.ai-analysis-content::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

::v-global(.analysis-item) {
  transition: all 0.3s ease;
}

::v-global(.analysis-item:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* AI分析文本样式优化 */
::v-global(.analysis-item .whitespace-pre-wrap) {
  line-height: 1.8;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 参数卡片悬停效果 */
::v-global(.analysis-item .bg-white) {
  transition: all 0.2s ease;
}

::v-global(.analysis-item .bg-white:hover) {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 引导系统高亮效果 */
::v-global(.guide-highlight-element) {
  position: relative;
  z-index: 9999 !important;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.4), 0 0 20px rgba(24, 144, 255, 0.3) !important;
  border-radius: 4px;
  animation: guideHighlight 2s infinite;
}

@keyframes guideHighlight {
  0%, 100% { 
    box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.4), 0 0 20px rgba(24, 144, 255, 0.3);
  }
  50% { 
    box-shadow: 0 0 0 8px rgba(24, 144, 255, 0.6), 0 0 30px rgba(24, 144, 255, 0.5);
  }
}

@keyframes pulseHighlight {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 0 0 4px rgba(255, 107, 107, 0.4);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(255, 107, 107, 0.6);
  }
}

/* 专业大屏覆盖层样式 */
.professional-dashboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background: linear-gradient(135deg, #0c1445 0%, #1e3c72 50%, #2a5298 100%);
}
</style>

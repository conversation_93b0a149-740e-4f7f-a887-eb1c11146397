import { request } from '@/utils'
const BASE_URL = '/water-ph-weather-data'
export default {
  create: data => request.post('/water-ph-weather-data', data),
  read: (params = {}) => request.get('/water-ph-weather-data', { params }),
  update: data => request.patch(`/water-ph-weather-data`, data),
  delete: id => request.delete(`/water-ph-weather-data/${id}`),
  getListStationPoints:(scaleId)=>request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  // 获取采样类型列表
  getListSampleTypes: () => request.get('/analysis-sample-type/list'),
  // 导出水环境Excel
  exportExcel: params => request.get(`${BASE_URL}/export`, {
    params,
    responseType: 'blob',
  }),

  // 导出大气环境Excel
  exportAirExcel: params => request.get(`${BASE_URL}/export/air`, {
    params,
    responseType: 'blob',
  }),

  // 导入Excel
  importExcel: data => request.post(`${BASE_URL}/import`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

}

// 表层海水水域导入导出
export const exportSurfaceWater = (params = {}) => request.get('/surface-water-environmental/export', {
  params,
  responseType: 'arraybuffer',
  headers: {
    'Cache-Control': 'no-cache'
  }
})

export const importSurfaceWater = data => request.post('/surface-water-environmental/import', data, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 底层海水水域导入导出
export const exportBottomWater = (params = {}) => request.get('/bottom-water-environmental/export', {
  params,
  responseType: 'arraybuffer',
  headers: {
    'Cache-Control': 'no-cache'
  }
})

export const importBottomWater = data => request.post('/bottom-water-environmental/import', data, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 导入水质监测数据
export const importWaterQuality = data => request.post('/water-quality/import', data, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 导出水质监测数据
export const exportWaterQuality = (params = {}) => request.get('/water-quality/export', {
  params,
  responseType: 'arraybuffer',
  headers: {
    'Cache-Control': 'no-cache'
  }
})

// 导入沉积物微观繁殖体数据
export const importSedimentMicroorganism = data => request.post('/sediment-microorganism/import', data, {
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 导出沉积物微观繁殖体数据
export const exportSedimentMicroorganism = (params = {}) => request.get('/sediment-microorganism/export', {
  params,
  responseType: 'arraybuffer',
  headers: {
    'Cache-Control': 'no-cache'
  }
})


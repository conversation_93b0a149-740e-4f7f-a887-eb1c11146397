<!--------------------------------
-现场调查时空分析模块
-调查时间范围
-createBy：isla
--------------------------------->

<template>
  <CommonPage back>
    <template #title-suffix>
      <NTag class="ml-12" type="warning">
        站点 {{ route.query.distributeName }}
      </NTag>
    </template>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns"
      :get-data="api.readSurveyTimeRange">
    </MeCrud>


    <MeModal ref="modalRef" width="520px">
      <n-form :rules="rules" ref="modalFormRef" label-placement="left" label-align="left" :label-width="80"
        :model="modalForm" :disabled="modalAction === 'view'">

        <!-- <n-config-provider :locale="zhCN"> -->
        <n-form-item label="作业时间" rule-path="range" path="range">
          <n-date-picker v-model:value="modalForm.range" type="datetimerange" clearable />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input placeholder="请输入描述" v-model:value="modalForm.description" />
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag, NImage, NUpload } from 'naive-ui'
import { createDiscreteApi } from 'naive-ui'
import { h } from 'vue'
import { NConfigProvider, zhCN } from 'naive-ui';
import api from './api'

const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)
defineOptions({ name: 'RoleUser' })
const route = useRoute()
const now = Date.now()

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const rules = reactive({
  // description: { required: true, message: '请输入站点概述', trigger: ['input', 'blur'] },
  range: { required: true, message: '请选择日期范围', type: 'array', trigger: ['change'] },
});

// let branchUrl = ref()
// let crossCutUrl = ref()
// let surfaceCellUrl = ref()

// 模态框显示状态
const showModal = ref(false);

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit
} = useCrud({
  name: ' 作业时间',
  initForm: {
    enable: true,
    range: [
      now - 30 * 24 * 60 * 60 * 1000, now
    ],
    distributeId: Number(route.query.distributeId)
  },
  doCreate: api.createSurveyTimeRange,
  doDelete: api.deleteSurveyTimeRange,
  doUpdate: api.updateSurveyTimeRange,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

onMounted(() => {
  queryItems.value.distributeId = route.query.distributeId
  $table.value?.handleSearch()
})
const columns = [
  {
    title: '序号',
    key: 'index',
    // width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '作业开始时间',
    key: 'beforeInvestigate',
    // width: 180,
    render(row) {
      return h('span', formatDateTime(row.beforeInvestigate))
    },
  },
  {
    title: '作业结束时间',
    key: 'afterInvestigate',
    // width: 180,
    render(row) {
      return h('span', formatDateTime(row.afterInvestigate))
    },
  },
  { title: '时间范围描述', key: 'description', ellipsis: { tooltip: true } },
  {
    width: 180,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleOpenUpdate(row),
          },
          {
            default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function handleOpenUpdate(row) {
  console.log(row);
  row.range = [new Date(row.beforeInvestigate).getTime(), new Date(row.afterInvestigate).getTime()]
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row: row,
    onOk: updateM,
  })
}

const updateM = async () => {
  console.log(modalForm.value);
  await modalFormRef.value?.validate()
  await api.updateSurveyTimeRange({
    id: modalForm.value.id,
    range: modalForm.value.range,
    description: modalForm.value.description
  })
  $message.success('操作成功')
  $table.value?.handleSearch()

}

// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importSurveyTimeRange(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportSurveyTimeRange({ distributeId: route.query.distributeId })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `作业时间范围_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}

</script>

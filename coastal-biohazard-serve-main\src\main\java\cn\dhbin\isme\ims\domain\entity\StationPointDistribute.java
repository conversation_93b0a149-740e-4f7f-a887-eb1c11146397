package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 调查站位表(StationPointDistribute)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:38:31
 */
@Data
@TableName("station_point_distribute")
public class StationPointDistribute implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 空间范围id
     **/
    private Integer scaleId;
    
    /**
     * 任务ID，关联survey_route_task表
     */
    private Integer taskId;
    
    /**
     * 调查次数ID，关联survey_times表
     */
    private Integer timesId;
    
    /**
     * 监测站位（名称）
     **/
    private String name;
    
    /**
     * 经度
     **/
    private Double longitude;
    
    /**
     * 纬度
     **/
    private Double latitude;
    
    /**
     * 概述
     **/
    private String description;
    
    /**
     * 水文气象采集活动：0-否，1-是
     */
    private Boolean wpActivities;
    
    /**
     * 化学样本采集活动：0-否，1-是
     */
    private Boolean ciActivities;
    
    /**
     * 成体生物量采集活动：0-否，1-是
     */
    private Boolean mbActivities;
    
    /**
     * 微观繁殖体采集活动：0-否，1-是
     */
    private Boolean mrActivities;
    
    /**
     * 站点类型：standard-标准站，reference-参考站，special-特殊站
     */
    private String stationType;
    
    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priority;
    
    /**
     * 可达性：0-不可达，1-可达
     */
    private Boolean accessibility;
    
    /**
     * 设备需求
     */
    private String equipmentRequirements;
    
    /**
     * 调查开始时间
     **/
    private Date beforeInvestigate;

    /**
     * 调查结束时间
     **/
    private Date afterInvestigate;
    
    /**
     * 创建时间
     **/
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    /**
     * 更新时间
     **/
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

//    @TableField(exist = false)
//    private List<Long> range;
    
public Serializable pkVal() {
          return null;
      }
}



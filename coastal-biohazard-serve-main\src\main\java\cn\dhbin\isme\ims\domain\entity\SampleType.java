package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 样品种类表(SampleType)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:37:24
 */
@Data
@TableName("sample_type")
public class SampleType implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 名称
     **/
    private String name;

    @TableField(exist = false)
    private Double number;
public Serializable pkVal() {
          return null;
      }
}



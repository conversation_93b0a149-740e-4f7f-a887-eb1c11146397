package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.ChemicalIonDto;
import cn.dhbin.isme.ims.domain.dto.excel.ChemicalIonExcelDto;
import cn.dhbin.isme.ims.domain.entity.ChemicalIon;
import cn.dhbin.isme.ims.domain.request.ChemicalIonRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 化学离子表(ChemicalIon)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-28 12:52:05
 */
public interface ChemicalIonService extends IService<ChemicalIon> {
    Page<ChemicalIonDto> queryPage(ChemicalIonRequest request);

    List<ChemicalIon> getChemicalIonByDistributeIds(List<Integer> distributeIds);
    /**
     * 获取导出数据
     *
     * @param distributeId 站点ID
     * @param sampleLayer 采样层次
     * @return 导出数据列表
     */
    List<ChemicalIonExcelDto> getExportData(Integer distributeId, Integer sampleLayer);

    /**
     * 导入数据
     *
     * @param dataList 导入数据列表
     */
    void importData(List<ChemicalIonExcelDto> dataList);

    /**
     * 根据站点ID获取化学离子数据
     * @param stationId 站点ID
     * @return 化学离子数据列表
     */
    List<ChemicalIonDto> getByStationId(Integer stationId);
}


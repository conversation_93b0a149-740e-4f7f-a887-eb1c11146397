package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.AnalysisSampleType;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface AnalysisSampleTypeService extends IService<AnalysisSampleType> {

    List<AnalysisSampleType> AnalysislistSampleTypes();

    Page<AnalysisSampleType> queryPage(SampleTypeRequest request);
}


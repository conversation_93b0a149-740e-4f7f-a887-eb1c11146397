package cn.dhbin.isme.ims.domain.dto.excel;

import cn.dhbin.isme.ims.domain.converter.GenderConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

@Data
public class GtsusysStaffManageExcelDto {
    @ExcelProperty("姓名")
    @ColumnWidth(20)
    private String name;

    // 保持Integer类型，转换器会处理显示
    @ExcelProperty(value = "性别", converter = GenderConverter.class)
    @ColumnWidth(10)
    private Integer gender;  // 这里必须是Integer类型

    @ExcelProperty("身份证号")
    @ColumnWidth(25)
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT, dataFormat = 49)
    private String idCard;

    @ExcelProperty("所属分组")
    @ColumnWidth(20)
    private String groupName;
}

package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.AnalysisSampleType;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisOfBiologicalFactorsDto {
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点详情信息

    private List<AnalysisSampleType> sampleTypeList; // 样品种类集合

    /**
     * 样品类型
     **/
    private Integer sampleType;

    /**
     * 丰富度 ind./50g
     **/
    private Integer abundance;

    private Date createTime;

    private Date updateTime;

    private String report;
    
public Serializable pkVal() {
          return null;
      }
}



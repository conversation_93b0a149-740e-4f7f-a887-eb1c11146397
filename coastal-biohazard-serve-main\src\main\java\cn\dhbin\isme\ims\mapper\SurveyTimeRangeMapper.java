package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 调查时间范围表(SurveyTimeRange)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-11-05 08:18:24
 */
@Mapper
public interface SurveyTimeRangeMapper extends BaseMapper<SurveyTimeRange> {

    List<SurveyTimeRange> selectBatchByDistributeIds(@Param("distributeIds") List<Integer> distributeIds);
}


package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 水文气象数据 Excel 导入导出DTO
 */
@Data
public class WaterPhWeatherDataExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点ID")
    @ColumnWidth(15)
    private Integer distributeId;
    
    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;
    
    @ExcelProperty("采样层次")
    @ColumnWidth(15)
    private String sampleLayer;

    @ExcelProperty("天气现象")
    @ColumnWidth(20)
    private String weather;

    @ExcelProperty("风向")
    @ColumnWidth(15)
    private String windDirection;
    
    @ExcelProperty("盐度")
    @ColumnWidth(15)
    private Double saltExtent;
    
    @ExcelProperty("PH值")
    @ColumnWidth(15)
    private Double phExtent;

    @ExcelProperty("气温(℃)")
    @ColumnWidth(15)
    private Double airTemperature;

    @ExcelProperty("水温(℃)")
    @ColumnWidth(15)
    private Double waterTemperature;
    
    @ExcelProperty("透明度(m)")
    @ColumnWidth(15)
    private Double transparentExtent;






} 
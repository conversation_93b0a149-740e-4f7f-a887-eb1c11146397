package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.entity.Sediment;
import cn.dhbin.isme.ims.domain.request.SedimentRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface SedimentService extends IService<Sediment> {
    Page<Sediment> queryPage(SedimentRequest request);

    /**
     * 根据站点ID获取沉积物数据（通用方法）
     * @param stationId 站点ID
     * @return 沉积物数据列表
     */
    List<Sediment> getByStationId(Integer stationId);
}

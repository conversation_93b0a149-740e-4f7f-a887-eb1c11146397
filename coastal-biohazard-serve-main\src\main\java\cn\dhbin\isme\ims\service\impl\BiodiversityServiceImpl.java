package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.excel.BiodiversityExcelDto;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.BiodiversityRequest;
import cn.dhbin.isme.ims.mapper.BiodiversityMapper;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.service.BiodiversityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生物多样性分析表(Biodiversity)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-26 21:49:40
 */
@Service("biodiversityService")
public class BiodiversityServiceImpl extends ServiceImpl<BiodiversityMapper, Biodiversity> implements BiodiversityService {

    @Autowired
    private BiodiversityMapper biodiversityMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<BiodiversityDto> queryPage(BiodiversityRequest request) {
        IPage<Biodiversity> qp = request.toPage();
        LambdaQueryWrapper<Biodiversity> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(Biodiversity::getName, request.getName());
        }
        if (request.getDistributeId() != null) {
            queryWrapper.eq(Biodiversity::getDistributeId, request.getDistributeId());
        }
        if (request.getType() != null) {
            queryWrapper.eq(Biodiversity::getType, request.getType());
        }

        IPage<Biodiversity> ret = biodiversityMapper.selectPage(qp, queryWrapper);

        IPage<BiodiversityDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            BiodiversityDto dataDto = new BiodiversityDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public List<BiodiversityDto> queryList(Integer distributeId) {
        LambdaQueryWrapper<Biodiversity> queryWrapper = new LambdaQueryWrapper<>();

        if (distributeId!= null) {
            queryWrapper.eq(Biodiversity::getDistributeId, distributeId);
        }

        List<Biodiversity> biodiversities = biodiversityMapper.selectList(queryWrapper);

        List<BiodiversityDto> dtoList = biodiversities.stream().map(data -> {
            // 初始化目标对象
            BiodiversityDto dataDto = new BiodiversityDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            return dataDto;
        }).collect(Collectors.toList());

        return dtoList;
    }
    @Override
    public List<BiodiversityExcelDto> queryListForExcel(BiodiversityRequest request){
        LambdaQueryWrapper<Biodiversity> queryWrapper=new LambdaQueryWrapper<>();
        if (request.getDistributeId()!=null){
            queryWrapper.eq(Biodiversity::getDistributeId,request.getDistributeId());
        }
        if (request.getType()!=null){
            queryWrapper.eq(Biodiversity::getType,request.getType());
        }
        if (request.getName()!=null){
            queryWrapper.eq(Biodiversity::getName,request.getName());
        }
        List<Biodiversity> resultList = biodiversityMapper.selectList(queryWrapper);
        return resultList.stream().map(data->{
            BiodiversityExcelDto excelDto=new BiodiversityExcelDto();
            excelDto.setId(String.valueOf(data.getId()));
            excelDto.setDistributeId(data.getDistributeId());
            excelDto.setName(data.getName());
            excelDto.setAbundance(data.getAbundance());
            excelDto.setBiodiversity(String.valueOf(data.getBiodiversity()));
            // 获取站点名称
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
            if (stationPointDistribute != null) {
                excelDto.setDistributeName(stationPointDistribute.getName());
            }
            return excelDto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importData(List<BiodiversityExcelDto> excelList, Integer type) {
        if (excelList == null || excelList.isEmpty()) {
            throw new BizException(BizResponseCode.ERR_11013, "导入数据为空");
        }

        // 转换Excel数据到实体类
        List<Biodiversity> entityList = excelList.stream()
                .filter(dto -> dto.getDistributeId() != null) // 过滤掉没有站点ID的数据
                .map(dto -> {
                    Biodiversity entity = new Biodiversity();

                    // 设置站点ID
                    entity.setDistributeId(dto.getDistributeId());

                    // 设置生物类型
                    entity.setType(type != null ? type : 0); // 默认为浮游植物

                    // 设置名称
                    entity.setName(dto.getName());

                    // 设置丰度
                    entity.setAbundance(dto.getAbundance());

                    // 设置生物多样性
                    if (dto.getBiodiversity() != null && !dto.getBiodiversity().isEmpty()) {
                        try {
                            entity.setBiodiversity(BigDecimal.valueOf(Double.valueOf(dto.getBiodiversity())));
                        } catch (NumberFormatException e) {
                            // 忽略无效的数值格式
                        }
                    }

                    return entity;
                })
                .collect(Collectors.toList());

        if (entityList.isEmpty()) {
            throw new BizException(BizResponseCode.ERR_11013, "没有有效数据需要导入");
        }

        // 批量保存数据
        return this.saveBatch(entityList);
    }
}


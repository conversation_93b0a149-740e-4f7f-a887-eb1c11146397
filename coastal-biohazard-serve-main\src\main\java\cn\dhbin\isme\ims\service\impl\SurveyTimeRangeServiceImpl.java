package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.excel.SurveyTimeRangeExcelDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.domain.request.SurveyTimeRangeRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.SurveyTimeRangeMapper;
import cn.dhbin.isme.ims.service.SurveyTimeRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调查时间范围表(SurveyTimeRange)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-05 08:18:24
 */
@Service("surveyTimeRangeService")
public class SurveyTimeRangeServiceImpl extends ServiceImpl<SurveyTimeRangeMapper, SurveyTimeRange> implements SurveyTimeRangeService {

    @Autowired
    private SurveyTimeRangeMapper surveyTimeRangeMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<SurveyTimeRange> queryPage(SurveyTimeRangeRequest request) {
        IPage<SurveyTimeRange> qp = request.toPage();
        LambdaQueryWrapper<SurveyTimeRange> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getDistributeId() != null) {
            queryWrapper.eq(SurveyTimeRange::getDistributeId, request.getDistributeId());
        }

        IPage<SurveyTimeRange> ret = surveyTimeRangeMapper.selectPage(qp, queryWrapper);


        return Page.convert(ret);
    }

    @Override
    public List<SurveyTimeRange> getSurveyTimeRangeByDistributeIds(List<Integer> distributeIds) {
//        // 获取所有 distributeId 对应的 name
//        List<StationPointDistribute> distributes = stationPointDistributeMapper.selectBatchIds(distributeIds);
//        Map<Integer, String> distributeNameMap = distributes.stream()
//                .collect(Collectors.toMap(StationPointDistribute::getId, StationPointDistribute::getName));
//
//        List<SurveyTimeRange> surveyTimeRanges = surveyTimeRangeMapper.selectBatchByDistributeIds(distributeIds);
//        for (SurveyTimeRange data : surveyTimeRanges) {
//            data.setDistributeName(distributeNameMap.get(data.getDistributeId()));
//        }
//
//        return surveyTimeRanges;
        return surveyTimeRangeMapper.selectBatchByDistributeIds(distributeIds);
    }

    @Override
    public List<Integer> getDistributeIdsByMonth(Integer month) {
        QueryWrapper<SurveyTimeRange> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT distribute_id")
                .apply("MONTH(before_investigate) = {0}", month);
        return baseMapper.selectObjs(queryWrapper).stream()
                .map(o -> (Integer) o)
                .collect(Collectors.toList());
    }

    @Override
    public List<SurveyTimeRangeExcelDto> queryListForExport(SurveyTimeRangeRequest request) {
        LambdaQueryWrapper<SurveyTimeRange> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getDistributeId() != null) {
            queryWrapper.eq(SurveyTimeRange::getDistributeId, request.getDistributeId());
        }

        List<SurveyTimeRange> list = surveyTimeRangeMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 获取所有站点ID
        List<Integer> distributeIds = list.stream()
                .map(SurveyTimeRange::getDistributeId)
                .distinct()
                .collect(Collectors.toList());

        // 获取站点信息
        List<StationPointDistribute> distributes = stationPointDistributeMapper.selectBatchIds(distributeIds);
        Map<Integer, String> distributeNameMap = distributes.stream()
                .collect(Collectors.toMap(StationPointDistribute::getId, StationPointDistribute::getName));

        // 转换为ExcelDTO
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return list.stream().map(item -> {
            SurveyTimeRangeExcelDto dto = new SurveyTimeRangeExcelDto();
            dto.setId(String.valueOf(item.getId()));
            dto.setDistributeId(item.getDistributeId());
            dto.setDistributeName(distributeNameMap.get(item.getDistributeId()));

            if (item.getBeforeInvestigate() != null) {
                dto.setBeforeInvestigate(dateFormat.format(item.getBeforeInvestigate()));
            }

            if (item.getAfterInvestigate() != null) {
                dto.setAfterInvestigate(dateFormat.format(item.getAfterInvestigate()));
            }

            dto.setDescription(item.getDescription());

            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public void importData(List<SurveyTimeRangeExcelDto> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BizException(BizResponseCode.ERR_11013, "没有有效数据需要导入");
        }

        // 获取所有站点ID
        List<Integer> distributeIds = list.stream()
                .map(SurveyTimeRangeExcelDto::getDistributeId)
                .distinct()
                .collect(Collectors.toList());

        // 验证站点是否存在
        List<StationPointDistribute> distributes = stationPointDistributeMapper.selectBatchIds(distributeIds);
        if (distributes.size() != distributeIds.size()) {
            throw new BizException(BizResponseCode.ERR_11013, "存在无效的站点ID");
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<SurveyTimeRange> entities = list.stream().map(dto -> {
            SurveyTimeRange entity = new SurveyTimeRange();

            // 如果有ID，尝试更新已有记录
            if (!StringUtils.isEmpty(dto.getId())) {
                try {
                    entity.setId(Integer.parseInt(dto.getId()));
                } catch (NumberFormatException e) {
                    // 忽略无效ID
                }
            }

            entity.setDistributeId(dto.getDistributeId());
            entity.setDescription(dto.getDescription());

            try {
                if (!StringUtils.isEmpty(dto.getBeforeInvestigate())) {
                    entity.setBeforeInvestigate(dateFormat.parse(dto.getBeforeInvestigate()));
                }

                if (!StringUtils.isEmpty(dto.getAfterInvestigate())) {
                    entity.setAfterInvestigate(dateFormat.parse(dto.getAfterInvestigate()));
                }
            } catch (ParseException e) {
                throw new BizException(BizResponseCode.ERR_11013, "日期格式错误: " + e.getMessage());
            }

            return entity;
        }).collect(Collectors.toList());

        // 保存或更新数据
        this.saveOrUpdateBatch(entities);
    }
}


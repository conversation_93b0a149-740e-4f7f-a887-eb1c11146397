<!--------------------------------
-浮游植物
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出/导入功能 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="add()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select label-field="name" value-field="id" clearable v-model:value="queryItems.distributeId" filterable
                  :options="stationOption" placeholder="请选择站点" />
      </MeQueryItem>

      <!-- <MeQueryItem label="采样层次" :label-width="70">
        <n-select clearable v-model:value="queryItems.sampleLayer" filterable :options="sampleLayerOption"
          placeholder="请选择采样层次" />
      </MeQueryItem> -->
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
              :disabled="modalAction === 'view'">
        <n-form-item label="站点" path="distributeId" :rule="{
          required: true,
          message: '请选择站点',
          // trigger: ['change'],
        }">
          <!-- <n-input v-model:value="modalForm.algaeName" /> -->
          <n-select label-field="name" value-field="id" @change="changeSelect" clearable
                    v-model:value="modalForm.distributeId" filterable :options="stationOption" placeholder="请选择站点" />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input disabled v-model:value="coordination.longitude" placeholder="经度"></n-input>
            </n-form-item>
            <n-form-item label="纬度">
              <n-input disabled v-model:value="coordination.latitude" placeholder="纬度"></n-input>
            </n-form-item>
          </div>
        </Transition>

        <n-form-item label="优势种" path="name" :rule="{
          required: true,
          message: '请输入优势种',
          trigger: ['input', 'blur'],
        }">
          <n-input placeholder="请输入优势种" v-model:value="modalForm.name">
          </n-input>
        </n-form-item>

        <!--        <n-form-item label="生物多样性指数范围">-->
        <!--          <n-form-item path="hindexMin" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最低值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最低值" v-model:value="modalForm.hindexMin">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->

        <!--          &nbsp<span style="position: relative;top: -12px;">~</span>&nbsp-->

        <!--          <n-form-item path="hindexMax" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最高值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最高值" v-model:value="modalForm.hindexMax">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->
        <!--        </n-form-item>-->

        <!--        <n-form-item style="position: relative;top:-24px" label="生物多样性指数平均值" path="havg" :rule="{-->
        <!--          required: true,-->
        <!--          type: 'number',-->
        <!--          message: '请输入生物多样性指数平均值',-->
        <!--          trigger: ['input', 'blur'],-->
        <!--        }">-->
        <!--          <n-input-number min=0 style="width: 100%;" placeholder="请输入生物多样性指数平均值" v-model:value="modalForm.havg">-->
        <!--          </n-input-number>-->
        <!--        </n-form-item>-->

        <!--        <n-form-item label="均匀度指数范围">-->
        <!--          <n-form-item path="jindexMin" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最低值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最低值" v-model:value="modalForm.jindexMin">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->

        <!--          &nbsp<span style="position: relative;top: -12px;">~</span>&nbsp-->

        <!--          <n-form-item path="jindexMax" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最高值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最高值" v-model:value="modalForm.jindexMax">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->
        <!--        </n-form-item>-->

        <!--        <n-form-item style="position: relative;top:-24px" label="均匀度指数平均值" path="javg" :rule="{-->
        <!--          required: true,-->
        <!--          type: 'number',-->
        <!--          message: '请输入均匀度指数平均值',-->
        <!--          trigger: ['input', 'blur'],-->
        <!--        }">-->
        <!--          <n-input-number min=0 style="width: 100%;" placeholder="请输入均匀度指数平均值" v-model:value="modalForm.javg">-->
        <!--          </n-input-number>-->
        <!--        </n-form-item>-->

        <!--        <n-form-item label="丰富度指数范围">-->
        <!--          <n-form-item path="dindexMin" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最低值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最低值" v-model:value="modalForm.dindexMin">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->

        <!--          &nbsp<span style="position: relative;top: -12px;">~</span>&nbsp-->

        <!--          <n-form-item path="dindexMax" :rule="{-->
        <!--            required: true,-->
        <!--            type: 'number',-->
        <!--            message: '请输入范围内最高值',-->
        <!--            trigger: ['input', 'blur'],-->
        <!--          }">-->
        <!--            <n-input-number min=0 style="width: 100%;" placeholder="请输入最高值" v-model:value="modalForm.dindexMax">-->
        <!--            </n-input-number>-->
        <!--          </n-form-item>-->
        <!--        </n-form-item>-->

        <n-form-item label="丰度" path="abundance" :rule="{
          required: true,
          message: '请输入丰度',
          trigger: ['input', 'blur'],
        }">
          <n-input  style="width: 100%;" placeholder="请输入丰度" v-model:value="modalForm.abundance">
            <template #suffix>
              个/m³
            </template>
          </n-input>
        </n-form-item>
        <n-form-item label="生物多样性" path="biodiversity" :rule="{
          required: true,
          type: 'number',
          message: '请输入生物多样性',
          trigger: ['input', 'blur'],
        }">
          <n-input-number min=0 style="width: 100%;" placeholder="请输入生物多样性" v-model:value="modalForm.biodiversity">
          </n-input-number>
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NAvatar, NButton, NSwitch, NTag } from 'naive-ui'
import api from './api'
const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})
const getStationList = async () => {
  let { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}


const changeSelect = async (row) => {
  if (row != null) {
    console.log(row);
    stationOption.value.filter(item => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
        return
      }
    })
  } else {
    coordination.value = {}
  }
}

onMounted(() => {
  queryItems.value.type = 0
  $table.value?.handleSearch()
  getStationList()
  // modalForm.value.type = 0
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit
} = useCrud({
  name: '生物多样性',
  initForm: { enable: true ,type:0},
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  // refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 55,
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '站点',
    width: 70,
    key: "stationPointDistribute.name",
    render(row) {
      return h(NTag,
        { type: 'primary' },
        { default: () => row.stationPointDistribute.name })
    }
  },
  { title: '优势种', key: 'name',  ellipsis: { tooltip: true }, },
  {
    title: '丰度',
    key:'abundance',
    // ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.abundance+ ' 个/m³');
    },
  },
  {
    title: '生物多样性',
    key:'biodiversity',
    // ellipsis: { tooltip: true },
    render(row) {
      return h('span', row.biodiversity);
    },
  },
  // {
  //   title: '生物多样性指数范围',
  //   // key:'hindexMin',
  //   // ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.hindexMin + ' ~ ' + row.hindexMax);
  //   },
  // },
  // {
  //   title: '生物多样性指数平均值',
  //   // ellipsis: { tooltip: true },
  //   key: 'havg'
  // },
  // {
  //   title: '均匀度指数范围',
  //   // ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.jindexMin + ' ~ ' + row.jindexMax);
  //   },
  // },
  // {
  //   title: '均匀度指数平均值',
  //   // ellipsis: { tooltip: true },
  //   key: 'javg'
  // },
  // {
  //   title: '丰富度指数范围',
  //   // ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.dindexMin + ' ~ ' + row.dindexMax);
  //   },
  // },
  // {
  //   title: '丰富度指数平均值',
  //   // ellipsis: { tooltip: true },
  //   key: 'davg'
  // },
  // {
  //   width: 150,
  //   align: 'right',
  //   fixed: 'right',
  //   title: '群落特征分析',
  //   ellipsis: { tooltip: true },
  //   key: 'description'
  // },
  {
    width: 130,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            // style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]
// 导出功能
async function handleExport() {
  try {
    const res = await api.phytoplanktonExport(queryItems.value)
    if (!res) {
      message.error('导出失败：返回数据为空')
      return
    }
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = '浮游植物生物多样性数据.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败：${error.message || '未知错误'}`)
  }
}
// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.phytoplanktonImport(formData)

    if (code === 0) {
      $message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      $message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    $message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style>

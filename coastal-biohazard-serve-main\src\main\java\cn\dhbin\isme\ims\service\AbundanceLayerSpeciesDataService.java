package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:29
 */
public interface AbundanceLayerSpeciesDataService extends IService<AbundanceLayerSpeciesData> {
    Page<AbundanceLayerSpeciesDataDto> queryPage(AbundanceLayerSpeciesDataRequest request);

    void addAbundance(Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes);

    void updateAbundance(Integer id, Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes);

    List<AbundanceLayerSpeciesDataDto> queryList(Integer distributeId);
}


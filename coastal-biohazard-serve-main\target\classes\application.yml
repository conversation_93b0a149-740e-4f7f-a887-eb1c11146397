spring:
  profiles:
    active: dev
server:
  port: 8087
sa-token:
  token-name: Authorization
  token-prefix: Bearer
  jwt-secret-key: 'd0!doc15415B0*4G0`'
knife4j:
  enable: true
oss:
  endpoint: http://oss-cn-nanjing.aliyuncs.com
  accessKeyId: LTAI5tCzRsYDcLxTwYjTpnpb # 阿里云accessKeyId
  accessKeySecret: ****************************** # 阿里云accessKeySecret
  bucketName: yellow-sea # 阿里云oss空间名称
  ALI_DOMAIN: https://yellow-sea.oss-cn-nanjing.aliyuncs.com/ # 阿里云oss域名
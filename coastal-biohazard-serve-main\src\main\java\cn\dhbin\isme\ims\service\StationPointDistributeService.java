package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.SelectOptionDto;
import cn.dhbin.isme.ims.domain.dto.StationPointDistributeDto;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.StationPointDistributeRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 调查站位表(StationPointDistribute)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:38:31
 */
public interface StationPointDistributeService extends IService<StationPointDistribute> {
    List<SelectOptionDto> listStationPoints();

    Page<StationPointDistributeDto> queryPage(StationPointDistributeRequest request);

    List<StationPointDistribute> getDistributesByScaleId(Integer scaleId);

    void removeById(Integer id);
}


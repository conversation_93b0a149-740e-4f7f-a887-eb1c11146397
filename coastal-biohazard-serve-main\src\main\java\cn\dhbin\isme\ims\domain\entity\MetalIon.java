package cn.dhbin.isme.ims.domain.entity;


import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 金属离子表(MetalIon)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-27 22:29:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("metal_ion")
public class MetalIon implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;
    
    /**
     * 名称
     **/
    private String name;
    
    /**
     * 含量 - decimal(10,2)
     **/
    private BigDecimal num;
    
public Serializable pkVal() {
        return null;
    }
}



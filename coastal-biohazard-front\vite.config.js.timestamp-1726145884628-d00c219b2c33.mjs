// vite.config.js
import path2 from "node:path";
import Vue from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import VueJsx from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import Unocss from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/unocss/dist/vite.mjs";
import AutoImport from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/unplugin-auto-import/dist/vite.js";
import { NaiveUiResolver } from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/unplugin-vue-components/dist/resolvers.js";
import Components from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/unplugin-vue-components/dist/vite.js";
import { defineConfig, loadEnv } from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/vite/dist/node/index.js";
import removeNoMatch from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/vite-plugin-router-warn/dist/index.mjs";
import VueDevTools from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";

// build/index.js
import path from "node:path";
import { globSync } from "file:///C:/Users/<USER>/Desktop/%E6%B5%B7%E6%B4%8B%E7%94%9F%E6%80%81%E7%81%BE%E5%AE%B3%E6%A3%80%E6%B5%8B%E9%AA%8C%E8%AF%81%E7%B3%BB%E7%BB%9F/vue-naive-admin-2.x/node_modules/glob/dist/esm/index.js";

// src/assets/icons/dynamic-icons.js
var dynamic_icons_default = ["i-simple-icons:juejin"];

// build/index.js
function getIcons() {
  const feFiles = globSync("src/assets/icons/feather/*.svg", { nodir: true, strict: true });
  const meFiles = globSync("src/assets/icons/isme/*.svg", { nodir: true, strict: true });
  const feIcons = feFiles.map((filePath) => {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.parse(fileName).name;
    return `i-fe:${fileNameWithoutExt}`;
  });
  const meIcons = meFiles.map((filePath) => {
    const fileName = path.basename(filePath);
    const fileNameWithoutExt = path.parse(fileName).name;
    return `i-me:${fileNameWithoutExt}`;
  });
  return [...dynamic_icons_default, ...feIcons, ...meIcons];
}
function getPagePathes() {
  const files = globSync("src/views/**/*.vue");
  return files.map((item) => `/${path.normalize(item).replace(/\\/g, "/")}`);
}

// build/plugin-isme/icons.js
var PLUGIN_ICONS_ID = "isme:icons";
function pluginIcons() {
  return {
    name: "isme:icons",
    resolveId(id) {
      if (id === PLUGIN_ICONS_ID)
        return `\0${PLUGIN_ICONS_ID}`;
    },
    load(id) {
      if (id === `\0${PLUGIN_ICONS_ID}`) {
        return `export default ${JSON.stringify(getIcons())}`;
      }
    }
  };
}

// build/plugin-isme/page-pathes.js
var PLUGIN_PAGE_PATHES_ID = "isme:page-pathes";
function pluginPagePathes() {
  return {
    name: "isme:page-pathes",
    resolveId(id) {
      if (id === PLUGIN_PAGE_PATHES_ID)
        return `\0${PLUGIN_PAGE_PATHES_ID}`;
    },
    load(id) {
      if (id === `\0${PLUGIN_PAGE_PATHES_ID}`) {
        return `export default ${JSON.stringify(getPagePathes())}`;
      }
    }
  };
}

// vite.config.js
var vite_config_default = defineConfig(({ mode }) => {
  const viteEnv = loadEnv(mode, process.cwd());
  const { VITE_PUBLIC_PATH, VITE_PROXY_TARGET } = viteEnv;
  return {
    base: VITE_PUBLIC_PATH || "/",
    plugins: [
      Vue(),
      VueJsx(),
      VueDevTools(),
      Unocss(),
      AutoImport({
        imports: ["vue", "vue-router"],
        dts: false
      }),
      Components({
        resolvers: [NaiveUiResolver()],
        dts: false
      }),
      // 自定义插件，用于生成页面文件的path，并添加到虚拟模块
      pluginPagePathes(),
      // 自定义插件，用于生成自定义icon，并添加到虚拟模块
      pluginIcons(),
      // 移除非必要的vue-router动态路由警告: No match found for location with path
      removeNoMatch()
    ],
    resolve: {
      alias: {
        "@": path2.resolve(process.cwd(), "src"),
        "~": path2.resolve(process.cwd())
      }
    },
    server: {
      host: "0.0.0.0",
      port: 3200,
      open: false,
      proxy: {
        "/api": {
          target: VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: (path3) => path3.replace(/^\/api/, ""),
          secure: false,
          configure: (proxy, options) => {
            proxy.on("proxyRes", (proxyRes, req) => {
              proxyRes.headers["x-real-url"] = new URL(req.url || "", options.target)?.href || "";
            });
          }
        }
      }
    },
    build: {
      chunkSizeWarningLimit: 1024
      // chunk 大小警告的限制（单位kb）
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

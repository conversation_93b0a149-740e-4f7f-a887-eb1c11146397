package cn.dhbin.isme.ims.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 水质预测记录实体
 */
@Data
@TableName("water_quality_prediction")
public class WaterQualityPrediction {
    
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 预测点经度
     */
    private BigDecimal longitude;
    
    /**
     * 预测点纬度
     */
    private BigDecimal latitude;
    
    /**
     * 调查中心ID
     */
    private Integer scaleId;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 调查次数ID
     */
    private Integer timesId;
    
    /**
     * 预测盐度
     */
    private BigDecimal saltExtent;
    
    /**
     * 预测pH值
     */
    private BigDecimal phExtent;
    
    /**
     * 预测水温
     */
    private BigDecimal waterTemperature;
    
    /**
     * 预测透明度
     */
    private BigDecimal transparentExtent;
    
    /**
     * 置信度评分(0-1)
     */
    private BigDecimal confidenceScore;
    
    /**
     * 模型版本
     */
    private String modelVersion;
    
    /**
     * 预测时间
     */
    private LocalDateTime predictionTime;
    
    /**
     * AI分析结果
     */
    private String aiAnalysis;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
} 
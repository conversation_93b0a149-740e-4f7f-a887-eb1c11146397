package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.AlgaeDetectionRecords;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.AlgaeDetectionRecordsMapper;
import cn.dhbin.isme.ims.service.AlgaeDetectionRecordsService;
import org.springframework.stereotype.Service;

/**
 * 预测数据模拟表(AlgaeDetectionRecords)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:30:42
 */
@Service("algaeDetectionRecordsService")
public class AlgaeDetectionRecordsServiceImpl extends ServiceImpl<AlgaeDetectionRecordsMapper, AlgaeDetectionRecords> implements AlgaeDetectionRecordsService {

}


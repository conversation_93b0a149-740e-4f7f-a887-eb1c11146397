package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.dto.ProfileDto;
import cn.dhbin.isme.pms.domain.entity.Profile;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:01:57+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ProfileToProfileDtoImpl implements ProfileToProfileDto {

    @Override
    public ProfileDto to(Profile arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ProfileDto profileDto = new ProfileDto();

        profileDto.setAddress( arg0.getAddress() );
        profileDto.setAvatar( arg0.getAvatar() );
        profileDto.setEmail( arg0.getEmail() );
        profileDto.setGender( arg0.getGender() );
        profileDto.setId( arg0.getId() );
        profileDto.setNickName( arg0.getNickName() );
        profileDto.setUserId( arg0.getUserId() );

        return profileDto;
    }
}

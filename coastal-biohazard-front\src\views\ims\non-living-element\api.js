import { request } from '@/utils'

export default {
  create: data => request.post('/chemical-ion', data),
  read: (params = {}) => request.get('/chemical-ion', { params }),
  update: data => request.patch(`/chemical-ion`, data),
  delete: id => request.delete(`/chemical-ion/${id}`),
  getListStationPoints: scaleId => request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  getListSampleTypes: () => request.get('/analysis-sample-type/list'),

  createMetalIon: data => request.post('/sediment', data),
  readMetalIon: (params = {}) => request.get('/sediment', { params }),
  updateMetalIon: data => request.patch(`/sediment`, data),
  deleteMetalIon: id => request.delete(`/sediment/${id}`),
  uploadImg: file => request.post('/upload/img', file, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  // Excel导入导出API
  importExcel: formData => request.post('/chemical-ion/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  exportExcel: (params) => request.get('/chemical-ion/export', {
    responseType: 'blob',
    params
  }),
  SedimentImportExcel: formData => request.post('sediment/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  SedimentExportExcel: (params) => request.get('/sediment/export', {
    responseType: 'blob',
    params
  }),
}

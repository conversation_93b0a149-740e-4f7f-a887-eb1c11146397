package cn.dhbin.isme.ims.mapper;

import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
@Mapper
public interface WaterPhWeatherDataMapper extends BaseMapper<WaterPhWeatherData> {
    List<WaterPhWeatherData> selectBatchByDistributeIds(@Param("distributeIds") List<Integer> distributeIds);

    @Select("SELECT salt_extent,ph_extent,water_temperature,transparent_extent FROM water_ph_weather_data WHERE distribute_id = #{distributeId} AND sample_layer=2")
    WaterPhWeatherData getWaterPhWeatherDataByDistributeId(@Param("distributeId") Integer distributeId);

}


package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.mapper.StationPointScaleMapper;
import cn.dhbin.isme.ims.service.StationPointDistributeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class StationPointDistributeExcelService {
    private final StationPointDistributeMapper stationPointDistributeMapper;
    private final StationPointScaleMapper stationPointScaleMapper;
    private final StationPointDistributeService stationPointDistributeService;

    public byte[] exportToExcel(Integer scaleId) throws IOException {
        // 查询调查站点分布数据
        LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
        
        if (scaleId != null) {
            queryWrapper.eq(StationPointDistribute::getScaleId, scaleId);
        }
        
        List<StationPointDistribute> dataList = stationPointDistributeMapper.selectList(queryWrapper);
        log.info("调查站点分布数据查询结果: scaleId={}, 查询到{}条记录", scaleId, dataList.size());
        
        if (dataList.isEmpty()) {
            log.warn("调查站点分布数据为空，scaleId={}", scaleId);
            // 返回一个只包含表头的工作簿
            try (Workbook workbook = new XSSFWorkbook()) {
                Sheet sheet = workbook.createSheet("调查站点分布数据");
                Row headerRow = sheet.createRow(0);
                
                // 创建表头
                createHeader(headerRow);
                
                try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                    workbook.write(outputStream);
                    return outputStream.toByteArray();
                }
            }
        }
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("调查站点分布数据");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            createHeader(headerRow);
            
            // 填充数据
            for (int i = 0; i < dataList.size(); i++) {
                StationPointDistribute data = dataList.get(i);
                Row row = sheet.createRow(i + 1);
                
                // 获取空间范围名称
                String scaleName = "";
                if (data.getScaleId() != null) {
                    StationPointScale scale = stationPointScaleMapper.selectById(data.getScaleId());
                    if (scale != null) {
                        scaleName = scale.getName();
                    }
                }
                
                // 填充每一列
                row.createCell(0).setCellValue(scaleName);
                row.createCell(1).setCellValue(data.getName() != null ? data.getName() : "");
                
                if (data.getLongitude() != null) {
                    row.createCell(2).setCellValue(data.getLongitude());
                }
                
                if (data.getLatitude() != null) {
                    row.createCell(3).setCellValue(data.getLatitude());
                }
                
                row.createCell(4).setCellValue(data.getDescription() != null ? data.getDescription() : "");
                
                if (data.getBeforeInvestigate() != null) {
                    Cell cell = row.createCell(5);
                    cell.setCellValue(data.getBeforeInvestigate());
                    CellStyle dateStyle = workbook.createCellStyle();
                    CreationHelper createHelper = workbook.getCreationHelper();
                    dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
                    cell.setCellStyle(dateStyle);
                }
                
                if (data.getAfterInvestigate() != null) {
                    Cell cell = row.createCell(6);
                    cell.setCellValue(data.getAfterInvestigate());
                    CellStyle dateStyle = workbook.createCellStyle();
                    CreationHelper createHelper = workbook.getCreationHelper();
                    dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd"));
                    cell.setCellStyle(dateStyle);
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < 7; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 导出到字节数组
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);
                return outputStream.toByteArray();
            }
        }
    }
    
    private void createHeader(Row headerRow) {
        headerRow.createCell(0).setCellValue("空间范围名称");
        headerRow.createCell(1).setCellValue("站点名称");
        headerRow.createCell(2).setCellValue("经度");
        headerRow.createCell(3).setCellValue("纬度");
        headerRow.createCell(4).setCellValue("概述");
        headerRow.createCell(5).setCellValue("调查开始时间");
        headerRow.createCell(6).setCellValue("调查结束时间");
    }

    public void importFromExcel(MultipartFile file, Integer scaleId) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            List<StationPointDistribute> dataList = new ArrayList<>();

            // 跳过表头行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                StationPointDistribute data = new StationPointDistribute();
                
                // 设置空间范围ID
                if (scaleId != null) {
                    data.setScaleId(scaleId);
                } else {
                    // 根据空间范围名称查找对应ID
                    Cell scaleNameCell = row.getCell(0);
                    if (scaleNameCell != null && scaleNameCell.getCellType() == CellType.STRING) {
                        String scaleName = scaleNameCell.getStringCellValue();
                        LambdaQueryWrapper<StationPointScale> scaleQuery = new LambdaQueryWrapper<>();
                        scaleQuery.eq(StationPointScale::getName, scaleName);
                        StationPointScale scale = stationPointScaleMapper.selectOne(scaleQuery);
                        if (scale != null) {
                            data.setScaleId(scale.getId());
                        } else {
                            continue; // 跳过空间范围不存在的行
                        }
                    } else {
                        continue; // 跳过空间范围为空的行
                    }
                }
                
                // 站点名称
                Cell nameCell = row.getCell(1);
                if (nameCell != null) {
                    data.setName(nameCell.getStringCellValue());
                } else {
                    continue; // 跳过站点名称为空的行
                }
                
                // 经度
                Cell longitudeCell = row.getCell(2);
                if (longitudeCell != null && longitudeCell.getCellType() == CellType.NUMERIC) {
                    data.setLongitude(longitudeCell.getNumericCellValue());
                }
                
                // 纬度
                Cell latitudeCell = row.getCell(3);
                if (latitudeCell != null && latitudeCell.getCellType() == CellType.NUMERIC) {
                    data.setLatitude(latitudeCell.getNumericCellValue());
                }
                
                // 概述
                Cell descriptionCell = row.getCell(4);
                if (descriptionCell != null) {
                    data.setDescription(descriptionCell.getStringCellValue());
                }
                
                // 调查开始时间
                Cell beforeInvestigateCell = row.getCell(5);
                if (beforeInvestigateCell != null) {
                    if (beforeInvestigateCell.getCellType() == CellType.NUMERIC) {
                        data.setBeforeInvestigate(beforeInvestigateCell.getDateCellValue());
                    }
                }
                
                // 调查结束时间
                Cell afterInvestigateCell = row.getCell(6);
                if (afterInvestigateCell != null) {
                    if (afterInvestigateCell.getCellType() == CellType.NUMERIC) {
                        data.setAfterInvestigate(afterInvestigateCell.getDateCellValue());
                    }
                }
                
                // 设置创建时间
                data.setCreateTime(new Date());
                
                dataList.add(data);
            }

            // 保存数据
            if (!dataList.isEmpty()) {
                stationPointDistributeService.saveBatch(dataList);
                log.info("成功导入{}条调查站点分布数据", dataList.size());
            } else {
                log.warn("没有有效的调查站点分布数据可导入");
            }
        }
    }
} 
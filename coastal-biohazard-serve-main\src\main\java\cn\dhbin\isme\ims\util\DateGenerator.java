package cn.dhbin.isme.ims.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DateGenerator {

    public static LocalDate[] generateFutureDates(int days) {
        LocalDate[] dates = new LocalDate[days];
        LocalDate today = LocalDate.now();
        for (int i = 0; i < days; i++) {
            dates[i] = today.plusDays(i + 1);
        }
        return dates;
    }

    public static String format(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }
}
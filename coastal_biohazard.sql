/*
 Navicat Premium Dump SQL

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : coastal_biohazard

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 26/05/2025 12:59:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for analysis_of_biological_factors
-- ----------------------------
DROP TABLE IF EXISTS `analysis_of_biological_factors`;
CREATE TABLE `analysis_of_biological_factors`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `sample_type` int NULL DEFAULT 0 COMMENT '样品类型(0为沉积物，1为底层水样，2为表层水样，3为藻样)默认0',
  `abundance` int NULL DEFAULT NULL COMMENT '丰富度 ind./50g',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `report` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `report_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微观繁殖体详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of analysis_of_biological_factors
-- ----------------------------
INSERT INTO `analysis_of_biological_factors` VALUES (39, 7, 2, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for analysis_sample
-- ----------------------------
DROP TABLE IF EXISTS `analysis_sample`;
CREATE TABLE `analysis_sample`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `abundance_id` int NULL DEFAULT NULL COMMENT '微观繁殖体id',
  `sample_id` int NULL DEFAULT NULL COMMENT '种类id',
  `number` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '生物量/丰度',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 96 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微观繁殖体-种类中间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of analysis_sample
-- ----------------------------
INSERT INTO `analysis_sample` VALUES (4, 1, 1, 13.00);
INSERT INTO `analysis_sample` VALUES (5, 1, 2, 3.00);
INSERT INTO `analysis_sample` VALUES (9, 4, 1, 13.00);
INSERT INTO `analysis_sample` VALUES (10, 4, 2, 3.00);
INSERT INTO `analysis_sample` VALUES (18, 7, 1, 13.00);
INSERT INTO `analysis_sample` VALUES (19, 7, 2, 3.00);
INSERT INTO `analysis_sample` VALUES (22, 10, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (23, 10, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (24, 11, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (25, 11, 3, 0.00);
INSERT INTO `analysis_sample` VALUES (26, 11, 4, 0.00);
INSERT INTO `analysis_sample` VALUES (27, 11, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (28, 12, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (29, 12, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (30, 13, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (31, 14, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (32, 14, 4, 0.00);
INSERT INTO `analysis_sample` VALUES (33, 14, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (34, 15, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (35, 15, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (36, 15, 3, 0.00);
INSERT INTO `analysis_sample` VALUES (39, 16, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (40, 16, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (50, 17, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (53, 18, 2, 0.00);
INSERT INTO `analysis_sample` VALUES (57, 20, 3, 0.00);
INSERT INTO `analysis_sample` VALUES (58, 20, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (61, 21, 2, 3.00);
INSERT INTO `analysis_sample` VALUES (62, 23, 2, 12.00);
INSERT INTO `analysis_sample` VALUES (63, 23, 3, 2.00);
INSERT INTO `analysis_sample` VALUES (64, 24, 2, 1.00);
INSERT INTO `analysis_sample` VALUES (65, 24, 3, 1.00);
INSERT INTO `analysis_sample` VALUES (66, 21, 1, 13.00);
INSERT INTO `analysis_sample` VALUES (67, 24, 1, 0.00);
INSERT INTO `analysis_sample` VALUES (68, 25, 1, 2.50);
INSERT INTO `analysis_sample` VALUES (69, 26, 1, 0.50);
INSERT INTO `analysis_sample` VALUES (70, 26, 2, 0.50);
INSERT INTO `analysis_sample` VALUES (75, 28, 1, 1.00);
INSERT INTO `analysis_sample` VALUES (76, 35, 1, 3.00);
INSERT INTO `analysis_sample` VALUES (77, 35, 2, 3.00);
INSERT INTO `analysis_sample` VALUES (78, 35, 3, 3.00);
INSERT INTO `analysis_sample` VALUES (79, 35, 4, 3.00);
INSERT INTO `analysis_sample` VALUES (80, 36, 1, 3.00);
INSERT INTO `analysis_sample` VALUES (82, 36, 3, 45.00);
INSERT INTO `analysis_sample` VALUES (83, 36, 4, 4.00);
INSERT INTO `analysis_sample` VALUES (84, 37, 1, 1.00);
INSERT INTO `analysis_sample` VALUES (85, 37, 2, 1.00);
INSERT INTO `analysis_sample` VALUES (86, 37, 3, 1.00);
INSERT INTO `analysis_sample` VALUES (87, 37, 4, 1.00);
INSERT INTO `analysis_sample` VALUES (88, 38, 1, 1.00);
INSERT INTO `analysis_sample` VALUES (89, 38, 2, 1.00);
INSERT INTO `analysis_sample` VALUES (90, 38, 3, 1.00);
INSERT INTO `analysis_sample` VALUES (91, 38, 4, 1.00);
INSERT INTO `analysis_sample` VALUES (92, 39, 1, 3.00);
INSERT INTO `analysis_sample` VALUES (93, 39, 2, 2.00);
INSERT INTO `analysis_sample` VALUES (94, 39, 3, 2.00);
INSERT INTO `analysis_sample` VALUES (95, 39, 4, 2.00);

-- ----------------------------
-- Table structure for analysis_sample_type
-- ----------------------------
DROP TABLE IF EXISTS `analysis_sample_type`;
CREATE TABLE `analysis_sample_type`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '样品种类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of analysis_sample_type
-- ----------------------------
INSERT INTO `analysis_sample_type` VALUES (1, '浒苔');
INSERT INTO `analysis_sample_type` VALUES (2, '微观繁殖体');
INSERT INTO `analysis_sample_type` VALUES (3, '微生物');
INSERT INTO `analysis_sample_type` VALUES (4, '病毒');

-- ----------------------------
-- Table structure for biodiversity
-- ----------------------------
DROP TABLE IF EXISTS `biodiversity`;
CREATE TABLE `biodiversity`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `type` int NULL DEFAULT 0 COMMENT '(0浮游植物 1浮游动物 2底栖生物 4游泳动物) 默认0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优势种名称',
  `h_index_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '生物多样性指数最低值',
  `h_index_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '生物多样性指数最高值',
  `h_avg` decimal(10, 2) NULL DEFAULT NULL COMMENT '生物多样性指数平均值',
  `j_index_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '均匀度指数最低值',
  `j_index_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '均匀度指数最高值',
  `j_avg` decimal(10, 2) NULL DEFAULT NULL COMMENT '均匀度指数平均值',
  `d_index_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '丰富度指数最低值',
  `d_index_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '丰富度指数最高值',
  `d_avg` decimal(10, 2) NULL DEFAULT NULL COMMENT '丰富度指数平均值',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '分析概述',
  `abundance` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '丰度',
  `biodiversity` decimal(5, 3) NULL DEFAULT NULL COMMENT '生物多样性',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '生物多样性分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of biodiversity
-- ----------------------------
INSERT INTO `biodiversity` VALUES (-606896127, 7, 0, '具槽帕拉藻(Paralia sulcata)、离心海链藻(Thalassiosira excentrica)、斯氏几内亚藻(Guinardia striata)和菱形海线藻(Thalassionema nitzschioides)', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '76.2×10^4', 1.800);
INSERT INTO `biodiversity` VALUES (2, 7, 1, '桡足类、毛颚类、枝角类', 1.02, 3.04, 2.10, 0.56, 0.88, 0.72, 0.12, 0.81, 0.44, '该海域浮游动物群落显示出了较高的多样性水平，种类分布相对均匀，整体丰富度处于中等水平。', '0.361', NULL);
INSERT INTO `biodiversity` VALUES (3, 7, 2, '环节动物多毛类、节肢动物甲壳类和软体动物', 1.02, 3.04, 2.10, 0.56, 0.88, 0.72, 0.12, 0.81, 0.44, '该海域底栖生物群落显示出了较高的多样性水平，种类分布相对均匀，整体丰富度处于中等水平。', '1589.18', NULL);

-- ----------------------------
-- Table structure for chemical_ion
-- ----------------------------
DROP TABLE IF EXISTS `chemical_ion`;
CREATE TABLE `chemical_ion`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `sample_layer` int NULL DEFAULT 2 COMMENT '采样层次（1 底层 2表层）默认2',
  `active_phosphate` decimal(5, 4) NULL DEFAULT NULL COMMENT '活性磷酸盐含量,单位mg/L',
  `nitrite_nitrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '亚硝酸盐-氮含量,单位mg/L',
  `nitrate_nitrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '硝酸盐-氮含量,单位mg/L',
  `ammonia_hydrogen` decimal(5, 4) NULL DEFAULT NULL COMMENT '氨-氢,单位mg/L',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '化学离子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of chemical_ion
-- ----------------------------
INSERT INTO `chemical_ion` VALUES (1, 7, 2, 0.0217, 0.0030, 0.1046, 0.0110, '2024-11-28 12:52:57', NULL);

-- ----------------------------
-- Table structure for gtsusys_staff_group
-- ----------------------------
DROP TABLE IF EXISTS `gtsusys_staff_group`;
CREATE TABLE `gtsusys_staff_group`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '单位名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '一线人员单位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gtsusys_staff_group
-- ----------------------------
INSERT INTO `gtsusys_staff_group` VALUES (10, '江苏海洋大学');

-- ----------------------------
-- Table structure for gtsusys_staff_manage
-- ----------------------------
DROP TABLE IF EXISTS `gtsusys_staff_manage`;
CREATE TABLE `gtsusys_staff_manage`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员姓名',
  `gender` int NULL DEFAULT 1 COMMENT '性别（1男 0女 默认1）',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `group_id` int NOT NULL COMMENT '部门id',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '概述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职位',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '一线作业人员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gtsusys_staff_manage
-- ----------------------------
INSERT INTO `gtsusys_staff_manage` VALUES (1, '刘子尚', 1, '320703200001170019', 10, NULL, '2024-10-27 15:51:09', '2024-10-30 19:31:11', NULL, NULL);

-- ----------------------------
-- Table structure for metal_ion
-- ----------------------------
DROP TABLE IF EXISTS `metal_ion`;
CREATE TABLE `metal_ion`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `name` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `num` decimal(10, 2) NULL DEFAULT NULL COMMENT '含量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '金属离子表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of metal_ion
-- ----------------------------
INSERT INTO `metal_ion` VALUES (-1862336511, 1, 'Cu', 0.20);

-- ----------------------------
-- Table structure for morphological_analysis_data
-- ----------------------------
DROP TABLE IF EXISTS `morphological_analysis_data`;
CREATE TABLE `morphological_analysis_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `abundance_id` int NOT NULL COMMENT '微观繁殖体id',
  `branch_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分支图片',
  `cross_cut_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '横切图片',
  `surface_cell_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '表层细胞图片',
  `create_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '形态分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of morphological_analysis_data
-- ----------------------------
INSERT INTO `morphological_analysis_data` VALUES (1, 1, 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/6e075ac62c20417ab72e4de76f5eac2d.png', 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/63df2a2d7ac74b508931181a47a3b8ba.png', 'https://yellow-sea.oss-cn-nanjing.aliyuncs.com/93cfad986d3a4902b8574190133bcd54.png', NULL);

-- ----------------------------
-- Table structure for permission
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `parentId` int NULL DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `layout` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `keepAlive` tinyint NULL DEFAULT NULL,
  `method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `show` tinyint NOT NULL DEFAULT 1 COMMENT '是否展示在页面菜单',
  `enable` tinyint NOT NULL DEFAULT 1,
  `order` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of permission
-- ----------------------------
INSERT INTO `permission` VALUES (1, '资源管理', 'Resource_Mgt', 'MENU', 2, '/pms/resource', NULL, 'i-fe:list', '/src/views/pms/resource/index.vue', NULL, NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (2, '系统管理', 'SysMgt', 'MENU', NULL, NULL, NULL, 'i-fe:grid', NULL, NULL, NULL, NULL, NULL, 1, 1, 9);
INSERT INTO `permission` VALUES (3, '角色管理', 'RoleMgt', 'MENU', 2, '/pms/role', NULL, 'i-fe:user-check', '/src/views/pms/role/index.vue', NULL, NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (4, '用户管理', 'UserMgt', 'MENU', 2, '/pms/user', NULL, 'i-fe:user', '/src/views/pms/user/index.vue', NULL, 1, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (5, '分配用户', 'RoleUser', 'MENU', 3, '/pms/role/user/:roleId', NULL, 'i-fe:user-plus', '/src/views/pms/role/role-user.vue', NULL, NULL, NULL, NULL, 0, 1, 1);
INSERT INTO `permission` VALUES (7, '图片上传', 'ImgUpload', 'MENU', 6, '/demo/upload', NULL, 'i-fe:image', '/src/views/demo/upload/index.vue', NULL, 1, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (8, '个人资料', 'UserProfile', 'MENU', NULL, '/profile', NULL, 'i-fe:user', '/src/views/profile/index.vue', NULL, NULL, NULL, NULL, 0, 1, 99);
INSERT INTO `permission` VALUES (9, '基本信息管理', 'Base', 'MENU', NULL, '/base', NULL, 'i-fe:inbox', NULL, NULL, NULL, NULL, NULL, 0, 1, 8);
INSERT INTO `permission` VALUES (13, '创建新用户', 'AddUser', 'BUTTON', 4, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (14, '图标 Icon', 'Icon', 'MENU', 9, '/base/icon', NULL, 'i-fe:feather', '/src/views/base/unocss-icon.vue', NULL, NULL, NULL, NULL, 1, 1, 5);
INSERT INTO `permission` VALUES (17, '采样记录', 'SamplingFieldRecord', 'MENU', 16, '/ims/sampling-field-record', NULL, 'i-fe:wind', '/src/views/ims/field-investigation/sampling-field-record.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (18, '微观繁殖体分析模块', 'MicroscopicPropaguleAnalysis', 'MENU', NULL, NULL, NULL, 'i-simple-icons:juejin', NULL, '', NULL, NULL, NULL, 0, 1, 6);
INSERT INTO `permission` VALUES (19, '沉积物微观繁殖体', 'MicroshapeRepuduceRecord', 'MENU', 18, '/ims/microshape-repuduce-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/microshape-repuduce-record.vue', '', NULL, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (20, '底层水样微观繁殖体', 'BottomWaterSampleRecord', 'MENU', 18, '/ims/bottom-water-sample-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/bottom-water-sample-record.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (21, '表层水样微观繁殖体', 'SurfaceWaterSampleRecord', 'MENU', 18, '/ims/surface-water-sample-record', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/surface-water-sample-record.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (22, '数据大屏', 'DataView', 'MENU', NULL, '/data-view', NULL, 'i-fe:airplay', '/src/views/ims/data-view/index.vue', 'empty', NULL, NULL, NULL, 0, 0, 0);
INSERT INTO `permission` VALUES (25, '形态分析', 'MorphologicalAnalysis', 'MENU', 18, '/ims/morphological-analysis', NULL, 'i-fe:wind', '/src/views/ims/microscopic-propagule-analysis/morphological-analysis.vue', '', NULL, NULL, NULL, 0, 1, 0);
INSERT INTO `permission` VALUES (26, '现场调查时空分析模块', 'TimeSpaceAnalysis', 'MENU', NULL, '', NULL, 'i-fe:navigation', NULL, '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (27, '一线作业人员管理', 'OperatorManagement', 'MENU', 26, '/ims/operator-management', NULL, 'i-fe:users', '/src/views/ims/time-space-analysis/operator-management.vue', '', NULL, NULL, NULL, 1, 1, 4);
INSERT INTO `permission` VALUES (28, '实地现场调查模块', 'WaterEnvironmentalMonitoringAndAnalysis', 'MENU', NULL, NULL, NULL, 'i-fe:sun', NULL, '', NULL, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (29, '水文数据采集分析', 'WaterEnvironmental', 'MENU', 28, '/ims/water-environmental', NULL, 'i-fe:wind', '/src/views/ims/water-environmental-monitoring-and-analysis/water-environmental.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (31, '调查空间范围定义', 'SpacialScale', 'MENU', 26, '/ims/spacial-scale', NULL, 'i-fe:aperture', '/src/views/ims/time-space-analysis/spacial-scale.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (32, '调查站位布设', 'SurveyStation', 'MENU', 26, '/ims/survey-station', NULL, 'i-fe:map', '/src/views/ims/time-space-analysis/survey-station.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (36, '作业时间管理', 'SurveyTimeRange', 'MENU', 26, '/ims/survey-time-range', NULL, 'i-fe:clock', '/src/views/ims/time-space-analysis/survey-time-range.vue', '', NULL, NULL, NULL, 1, 1, 3);
INSERT INTO `permission` VALUES (37, '气象数据采集分析', 'AirEnvironmental', 'MENU', 28, '/ims/air-environmental', NULL, 'i-fe:cloud', '/src/views/ims/water-environmental-monitoring-and-analysis/air-environmental.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (39, '室内非生物要素检验模块', 'NonLivingElement', 'MENU', NULL, '', NULL, 'i-fe:aperture', NULL, '', NULL, NULL, NULL, 1, 1, 4);
INSERT INTO `permission` VALUES (40, '海水化学分析', 'SeawaterChemistry', 'MENU', 39, '/ims/seawater-chemistry', NULL, 'i-fe:git-pull-request', '/src/views/ims/non-living-element/seawater-chemistry.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (42, '生物量采集数据', 'AampleOfAlgae', 'MENU', 41, '/ims/sample-of-algae', NULL, 'i-fe:grid', '/src/views/ims/adult-algae-analysis/sample-of-algae.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (45, '分子生物学分析', 'SampleType', 'MENU', 41, '/ims/sample-type', NULL, 'i-fe:more-vertical', '/src/views/ims/microscopic-propagule-analysis/sample-type.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (48, '人员单位管理', 'GtsusysStaffGroup', 'MENU', 26, '/ims/gtsusys-staff-group', NULL, 'i-fe:linkedin', '/src/views/ims/time-space-analysis/gtsusys-staff-group.vue', '', NULL, NULL, NULL, 1, 1, 5);
INSERT INTO `permission` VALUES (49, '沉积物分析', 'DepositSediment', 'MENU', 39, '/ims/deposit-sediment', NULL, 'i-fe:sliders', '/src/views/ims/non-living-element/deposit-sediment.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (50, '海洋生物物种多样性分析', 'BiodiversityAnalysis', 'MENU', NULL, '', NULL, 'i-simple-icons:juejin', NULL, '', NULL, NULL, NULL, 1, 1, 5);
INSERT INTO `permission` VALUES (51, '浮游植物多样性及群落特征分析', 'Phytoplankton', 'MENU', 50, '/ims/phytoplankton', NULL, 'i-fe:wind', '/src/views/ims/biodiversity-analysis/phytoplankton.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (52, '浮游动物多样性及群落特征分析', 'Zooplankter', 'MENU', 50, '/ims/zooplankter', NULL, 'i-fe:wind', '/src/views/ims/biodiversity-analysis/zooplankter.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (53, '底栖生物多样性及群落特征分析', 'Benthos', 'MENU', 50, '/ims/benthos', NULL, 'i-fe:wind', '/src/views/ims/biodiversity-analysis/benthos.vue', '', NULL, NULL, NULL, 1, 1, 2);
INSERT INTO `permission` VALUES (54, '游泳动物多样性及群落特征分析', 'Necton', 'MENU', 50, '/ims/necton', NULL, 'i-fe:wind', '/src/views/ims/biodiversity-analysis/necton.vue', '', NULL, NULL, NULL, 0, 0, 3);
INSERT INTO `permission` VALUES (55, '采样现场记录管理', 'StationPointDistribute', 'MENU', 28, '/ims/survey-station-1', NULL, 'i-fe:voicemail', '/src/views/ims/time-space-analysis/survey-station-1.vue', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (57, '黄海浒苔绿潮动态分析及模拟', '1213', 'MENU', NULL, NULL, NULL, 'i-fe:zap-off', NULL, '', NULL, NULL, NULL, 1, 1, 8);
INSERT INTO `permission` VALUES (58, '环境要素分析', '3232', 'MENU', 57, '/src/views/home/<USER>', NULL, 'i-fe:zap', '/src/views/home/<USER>', '', NULL, NULL, NULL, 1, 1, 0);
INSERT INTO `permission` VALUES (61, '生物要素分析', 'AnalysisOfBiologicalFactors', 'MENU', 57, '/ims/analysis-of-biological-factors', NULL, 'i-fe:x', '/src/views/ims/analysis-of-biological-factors/analysis-of-biological-factors.vue', '', NULL, NULL, NULL, 1, 1, 1);
INSERT INTO `permission` VALUES (62, '站点地图分布', 'StationMapDistribution', 'MENU', 26, '/ims/station-map-distribution', NULL, 'i-fe:map-pin', '/src/views/ims/time-space-analysis/JsVue.vue', '', NULL, NULL, NULL, 1, 1, 6);

-- ----------------------------
-- Table structure for profile
-- ----------------------------
DROP TABLE IF EXISTS `profile`;
CREATE TABLE `profile`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `gender` int NULL DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `userId` int NOT NULL,
  `nickName` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `userId`(`userId` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of profile
-- ----------------------------
INSERT INTO `profile` VALUES (1, NULL, 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif?imageView2/1/w/80/h/80', NULL, NULL, 1, 'Admin');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enable` tinyint NOT NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_code_name`(`code` ASC, `name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, 'SUPER_ADMIN', '超级管理员', 1);
INSERT INTO `role` VALUES (2, 'ROLE_QA', '质检员', 1);

-- ----------------------------
-- Table structure for role_permissions_permission
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions_permission`;
CREATE TABLE `role_permissions_permission`  (
  `roleId` int NOT NULL,
  `permissionId` int NOT NULL,
  PRIMARY KEY (`roleId`, `permissionId`) USING BTREE,
  UNIQUE INDEX `unique_roleId_permissionId`(`roleId` ASC, `permissionId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of role_permissions_permission
-- ----------------------------
INSERT INTO `role_permissions_permission` VALUES (2, 1);
INSERT INTO `role_permissions_permission` VALUES (2, 2);
INSERT INTO `role_permissions_permission` VALUES (2, 3);
INSERT INTO `role_permissions_permission` VALUES (2, 4);
INSERT INTO `role_permissions_permission` VALUES (2, 5);
INSERT INTO `role_permissions_permission` VALUES (2, 9);
INSERT INTO `role_permissions_permission` VALUES (2, 10);
INSERT INTO `role_permissions_permission` VALUES (2, 11);
INSERT INTO `role_permissions_permission` VALUES (2, 12);
INSERT INTO `role_permissions_permission` VALUES (2, 14);
INSERT INTO `role_permissions_permission` VALUES (2, 15);

-- ----------------------------
-- Table structure for sediment
-- ----------------------------
DROP TABLE IF EXISTS `sediment`;
CREATE TABLE `sediment`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `sediment_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '沉积物图片',
  `culture_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '沉积物培养图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '沉积物' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sediment
-- ----------------------------
INSERT INTO `sediment` VALUES (3, 'e3949d97-724b-48e8-aec3-92dff35d6eb3.jpg', 'b0132456-fd94-4644-853f-41d80f6b8667.jpg');

-- ----------------------------
-- Table structure for station_point_distribute
-- ----------------------------
DROP TABLE IF EXISTS `station_point_distribute`;
CREATE TABLE `station_point_distribute`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `scale_id` int NOT NULL COMMENT '空间范围id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '监测站位（名称）',
  `longitude` decimal(9, 6) NOT NULL COMMENT '经度',
  `latitude` decimal(9, 6) NOT NULL COMMENT '纬度',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '概述',
  `before_investigate` datetime NULL DEFAULT NULL COMMENT '调查开始时间',
  `after_investigate` datetime NULL DEFAULT NULL COMMENT '调查结束时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调查站位表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of station_point_distribute
-- ----------------------------
-- 调查中心1的站点
INSERT INTO `station_point_distribute` VALUES (1, 1, 'S1-1', 119.678879, 34.551811, '调查中心1第1个站点', '2024-10-25 15:00:00', '2024-10-25 18:00:00', '2024-10-27 15:33:45', '2024-10-27 15:34:55');
INSERT INTO `station_point_distribute` VALUES (2, 1, 'S1-2', 119.799728, 34.608345, '调查中心1第2个站点', '2024-10-26 12:00:00', '2024-10-26 15:00:00', '2024-10-27 15:35:08', '2024-10-27 15:35:11');
INSERT INTO `station_point_distribute` VALUES (3, 1, 'S1-3', 119.921265, 34.665970, '调查中心1第3个站点', '2024-10-27 13:00:00', '2024-10-27 16:00:00', '2024-10-27 15:35:51', NULL);
INSERT INTO `station_point_distribute` VALUES (4, 1, 'S1-4', 119.720000, 34.580000, '调查中心1第4个站点', '2024-10-28 09:00:00', '2024-10-28 12:00:00', '2024-10-28 08:30:00', NULL);
INSERT INTO `station_point_distribute` VALUES (5, 1, 'S1-5', 119.850000, 34.630000, '调查中心1第5个站点', '2024-10-29 10:00:00', '2024-10-29 13:00:00', '2024-10-29 09:30:00', NULL);

-- 调查中心2的站点
INSERT INTO `station_point_distribute` VALUES (6, 2, 'S2-1', 119.999999, 34.665790, '调查中心2第1个站点', '2024-11-02 10:49:55', '2024-11-02 14:49:55', '2024-11-01 10:50:20', NULL);
INSERT INTO `station_point_distribute` VALUES (7, 2, 'S2-2', 120.100000, 34.700000, '调查中心2第2个站点', '2024-11-03 11:00:00', '2024-11-03 15:00:00', '2024-11-02 10:30:00', NULL);
INSERT INTO `station_point_distribute` VALUES (8, 2, 'S2-3', 120.200000, 34.750000, '调查中心2第3个站点', '2024-11-04 12:00:00', '2024-11-04 16:00:00', '2024-11-03 11:30:00', NULL);

-- 调查中心3的站点
INSERT INTO `station_point_distribute` VALUES (9, 3, 'S3-1', 120.750000, 35.450000, '调查中心3第1个站点', '2024-11-16 08:00:00', '2024-11-16 12:00:00', '2024-11-15 15:00:00', NULL);
INSERT INTO `station_point_distribute` VALUES (10, 3, 'S3-2', 120.850000, 35.550000, '调查中心3第2个站点', '2024-11-17 09:00:00', '2024-11-17 13:00:00', '2024-11-16 14:00:00', NULL);
INSERT INTO `station_point_distribute` VALUES (11, 3, 'S3-3', 120.900000, 35.600000, '调查中心3第3个站点', '2024-11-18 10:00:00', '2024-11-18 14:00:00', '2024-11-17 15:00:00', NULL);

-- 海岸带调查1的站点（保留原有数据）
INSERT INTO `station_point_distribute` VALUES (12, 4, 'QDW', 119.366892, 34.760023, '海岸带调查主站点', '2024-12-15 09:00:00', '2024-12-15 13:00:00', '2024-12-07 08:48:30', NULL);
INSERT INTO `station_point_distribute` VALUES (13, 4, 'WCQD01', 119.380000, 34.780000, '海岸带调查西侧站点', '2024-12-15 09:00:00', '2024-12-15 13:00:00', '2024-12-07 08:50:00', NULL);
INSERT INTO `station_point_distribute` VALUES (14, 4, 'ACQD02', 119.400000, 34.800000, '海岸带调查东侧站点', '2024-12-15 09:30:00', '2024-12-15 13:30:00', '2024-12-07 08:51:00', NULL);
INSERT INTO `station_point_distribute` VALUES (15, 4, 'WMQD03', 119.320000, 34.740000, '海岸带调查北侧站点', '2024-12-17 10:00:00', '2024-12-17 14:00:00', '2024-12-07 08:52:00', NULL);

-- ----------------------------
-- Table structure for station_point_scale
-- ----------------------------
DROP TABLE IF EXISTS `station_point_scale`;
CREATE TABLE `station_point_scale`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定点名称',
  `longitude` decimal(9, 6) NULL DEFAULT NULL COMMENT '经度',
  `latitude` decimal(9, 6) NULL DEFAULT NULL COMMENT '纬度',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '空间范围描述',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '时空范围点位表（单点）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of station_point_scale
-- ----------------------------
INSERT INTO `station_point_scale` VALUES (1, '调查中心1', 121.123456, 34.241593, '这是调查中心1，位于青岛近海区域', '2024-10-27 15:29:30', '2024-10-31 18:24:15');
INSERT INTO `station_point_scale` VALUES (2, '调查中心2', 121.231410, 34.241952, '这是调查中心2，位于日照近海区域', '2024-11-01 10:11:49', NULL);
INSERT INTO `station_point_scale` VALUES (3, '调查中心3', 120.800000, 35.500000, '这是调查中心3，位于威海近海区域', '2024-11-15 10:00:00', NULL);
INSERT INTO `station_point_scale` VALUES (4, '海岸带调查1', 120.320000, 36.060000, '这是海岸带调查区域，位于烟台近海', '2024-12-07 08:47:54', NULL);

-- ----------------------------
-- Table structure for survey_time_range
-- ----------------------------
DROP TABLE IF EXISTS `survey_time_range`;
CREATE TABLE `survey_time_range`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NOT NULL COMMENT '站位id',
  `before_investigate` datetime NOT NULL COMMENT '调查开始时间',
  `after_investigate` datetime NOT NULL COMMENT '调查结束时间',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调查时间范围表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of survey_time_range
-- ----------------------------
INSERT INTO `survey_time_range` VALUES (15, 12, '2024-12-15 11:00:00', '2024-12-15 12:00:00', '1h');
INSERT INTO `survey_time_range` VALUES (16, 1, '2024-10-25 15:00:00', '2024-10-25 18:00:00', '3h');
INSERT INTO `survey_time_range` VALUES (17, 6, '2024-11-02 10:49:55', '2024-11-02 14:49:55', '4h');
INSERT INTO `survey_time_range` VALUES (18, 9, '2024-11-16 08:00:00', '2024-11-16 12:00:00', '4h');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `enable` tinyint NOT NULL DEFAULT 1,
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2a$10$FsAafxTTVVGXfIkJqvaiV.1vPfq4V9HW298McPldJgO829PR52a56', 1, '2023-11-18 16:18:59.150632', '2023-11-18 16:18:59.150632');

-- ----------------------------
-- Table structure for user_roles_role
-- ----------------------------
DROP TABLE IF EXISTS `user_roles_role`;
CREATE TABLE `user_roles_role`  (
  `userId` int NOT NULL,
  `roleId` int NOT NULL,
  PRIMARY KEY (`userId`, `roleId`) USING BTREE,
  UNIQUE INDEX `unique_userId_roleId`(`userId` ASC, `roleId` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of user_roles_role
-- ----------------------------
INSERT INTO `user_roles_role` VALUES (1, 1);
INSERT INTO `user_roles_role` VALUES (1, 2);

-- ----------------------------
-- Table structure for water_ph_weather_data
-- ----------------------------
DROP TABLE IF EXISTS `water_ph_weather_data`;
CREATE TABLE `water_ph_weather_data`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `distribute_id` int NULL DEFAULT NULL COMMENT '站点id',
  `sample_layer` int NULL DEFAULT 2 COMMENT '采样层次（1 底层 2表层）默认2',
  `weather` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '天气现象',
  `wind_direction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '风向',
  `salt_extent` decimal(5, 2) NULL DEFAULT NULL COMMENT '盐度',
  `ph_extent` decimal(4, 2) NULL DEFAULT NULL COMMENT 'PH值',
  `air_temperature` decimal(3, 1) NULL DEFAULT NULL COMMENT '气温,单位℃',
  `water_temperature` decimal(4, 2) NULL DEFAULT NULL COMMENT '水温,单位℃',
  `transparent_extent` decimal(4, 2) NULL DEFAULT NULL COMMENT '透明度,单位m',
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微观藻体水文特征表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of water_ph_weather_data
-- ----------------------------
INSERT INTO `water_ph_weather_data` VALUES (1, 7, 2, '晴', '西北风', 30.00, 8.01, 13.4, 10.30, 2.00, '2024-10-27 15:40:40', NULL);

-- ----------------------------
-- Procedure structure for GenerateInsertStatements
-- ----------------------------
DROP PROCEDURE IF EXISTS `GenerateInsertStatements`;

CREATE PROCEDURE `GenerateInsertStatements`()
BEGIN
    DECLARE i INT DEFAULT 1;

    -- 创建临时表存储生成的SQL语句
    CREATE TEMPORARY TABLE IF NOT EXISTS temp_insert_statements (
        insert_statement TEXT
    );

    -- 清空临时表
    TRUNCATE TABLE temp_insert_statements;

    -- 循环生成INSERT语句
    WHILE i <= 1000 DO
        INSERT INTO temp_insert_statements (insert_statement)
        VALUES (
            CONCAT(
                'INSERT INTO algae_detection_records (date, location, algae_density, water_temperature, salinity, ph_value, wind_speed, rainfall, light_intensity) ',
                'VALUES (',
                "'",
                DATE_FORMAT(DATE_ADD('2024-10-11', INTERVAL (i - 1) HOUR), '%Y-%m-%d %H:00:00'),
                "','",
                CASE WHEN i MOD 3 = 1 THEN '青岛海域' ELSE '日照海域' END,
                "',",
                0.05 + (i - 1) * 0.001,
                ",",
                16.5 + (i - 1) * 0.01,
                ",",
                32.0 + (i - 1) * 0.01,
                ",",
                8.2 + (i - 1) * 0.01,
                ",",
                5.0 + (i - 1) * 0.01,
                ",",
                0.0,
                ",",
                600.0 + (i - 1) * 1,
                ");"
            )
        );
        SET i = i + 1;
    END WHILE;

    -- 打印生成的SQL语句
    SELECT insert_statement FROM temp_insert_statements;
END;

-- ----------------------------
-- Table structure for survey_route_task
-- ----------------------------
DROP TABLE IF EXISTS `survey_route_task`;
CREATE TABLE `survey_route_task` (
  `id` int NOT NULL AUTO_INCREMENT,
  `scale_id` int NOT NULL COMMENT '调查中心ID，关联station_point_scale表',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '航线/任务名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务编码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '航线描述',
  `start_longitude` decimal(9,6) DEFAULT NULL COMMENT '航线起点经度',
  `start_latitude` decimal(9,6) DEFAULT NULL COMMENT '航线起点纬度',
  `end_longitude` decimal(9,6) DEFAULT NULL COMMENT '航线终点经度',
  `end_latitude` decimal(9,6) DEFAULT NULL COMMENT '航线终点纬度',
  `total_distance` decimal(10,2) DEFAULT NULL COMMENT '总距离(km)',
  `estimated_duration` int DEFAULT NULL COMMENT '预计持续时间(小时)',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale_id` (`scale_id`),
  KEY `idx_code` (`code`),
  CONSTRAINT `fk_survey_route_scale` FOREIGN KEY (`scale_id`) REFERENCES `station_point_scale` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调查航线/任务表';

-- ----------------------------
-- Table structure for survey_times
-- ----------------------------
DROP TABLE IF EXISTS `survey_times`;
CREATE TABLE `survey_times` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL COMMENT '任务ID，关联survey_route_task表',
  `times` int NOT NULL COMMENT '调查次数',
  `date` date NOT NULL COMMENT '调查日期',
  `start_time` time DEFAULT NULL COMMENT '开始时间',
  `end_time` time DEFAULT NULL COMMENT '结束时间',
  `weather_condition` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '天气条件',
  `sea_condition` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '海况',
  `crew_count` int DEFAULT NULL COMMENT '作业人员数量',
  `equipment_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备状态',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '备注',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-计划中，1-进行中，2-已完成，3-已取消',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_times` (`task_id`,`times`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_date` (`date`),
  KEY `idx_times` (`times`),
  CONSTRAINT `fk_survey_times_task` FOREIGN KEY (`task_id`) REFERENCES `survey_route_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='调查次数表';

-- ----------------------------
-- 扩展现有的station_point_distribute表，添加新字段
-- ----------------------------
ALTER TABLE `station_point_distribute` 
ADD COLUMN `task_id` int NULL DEFAULT NULL COMMENT '任务ID，关联survey_route_task表' AFTER `scale_id`,
ADD COLUMN `times_id` int NULL DEFAULT NULL COMMENT '调查次数ID，关联survey_times表' AFTER `task_id`,
ADD COLUMN `wp_activities` tinyint NULL DEFAULT 0 COMMENT '水文气象采集活动：0-否，1-是' AFTER `times_id`,
ADD COLUMN `ci_activities` tinyint NULL DEFAULT 0 COMMENT '化学样本采集活动：0-否，1-是' AFTER `wp_activities`,
ADD COLUMN `mb_activities` tinyint NULL DEFAULT 0 COMMENT '成体生物量采集活动：0-否，1-是' AFTER `ci_activities`,
ADD COLUMN `mr_activities` tinyint NULL DEFAULT 0 COMMENT '微观繁殖体采集活动：0-否，1-是' AFTER `mb_activities`,
ADD COLUMN `station_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'standard' COMMENT '站点类型：standard-标准站，reference-参考站，special-特殊站' AFTER `mr_activities`,
ADD COLUMN `priority` int NULL DEFAULT 1 COMMENT '优先级：1-高，2-中，3-低' AFTER `station_type`,
ADD COLUMN `accessibility` tinyint NULL DEFAULT 1 COMMENT '可达性：0-不可达，1-可达' AFTER `priority`,
ADD COLUMN `equipment_requirements` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设备需求' AFTER `accessibility`,
ADD INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
ADD INDEX `idx_times_id`(`times_id` ASC) USING BTREE,
ADD CONSTRAINT `fk_distribute_task` FOREIGN KEY (`task_id`) REFERENCES `survey_route_task` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
ADD CONSTRAINT `fk_distribute_times` FOREIGN KEY (`times_id`) REFERENCES `survey_times` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- ----------------------------
-- Table structure for map_display_config
-- ----------------------------
DROP TABLE IF EXISTS `map_display_config`;
CREATE TABLE `map_display_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置类型：marker-标记配置，polygon-区域配置，route-路径配置',
  `config_data` json COMMENT '配置数据(JSON格式)',
  `is_default` tinyint DEFAULT 0 COMMENT '是否默认配置：0-否，1-是',
  `is_active` tinyint DEFAULT 1 COMMENT '是否激活：0-否，1-是',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '配置描述',
  `create_user_id` int DEFAULT NULL COMMENT '创建用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='地图显示配置表';

-- ----------------------------
-- Table structure for water_quality_prediction
-- ----------------------------
DROP TABLE IF EXISTS `water_quality_prediction`;
CREATE TABLE `water_quality_prediction` (
  `id` int NOT NULL AUTO_INCREMENT,
  `longitude` decimal(9,6) NOT NULL COMMENT '预测点经度',
  `latitude` decimal(9,6) NOT NULL COMMENT '预测点纬度',
  `scale_id` int NOT NULL COMMENT '调查中心ID',
  `task_id` int NOT NULL COMMENT '任务ID',
  `times_id` int NOT NULL COMMENT '调查次数ID',
  `salt_extent` decimal(5,2) DEFAULT NULL COMMENT '预测盐度',
  `ph_extent` decimal(4,2) DEFAULT NULL COMMENT '预测pH值',
  `water_temperature` decimal(4,2) DEFAULT NULL COMMENT '预测水温',
  `transparent_extent` decimal(4,2) DEFAULT NULL COMMENT '预测透明度',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT '置信度评分(0-1)',
  `model_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型版本',
  `prediction_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '预测时间',
  `ai_analysis` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'AI分析结果',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_location` (`longitude`,`latitude`),
  KEY `idx_scale_task_times` (`scale_id`,`task_id`,`times_id`),
  KEY `idx_prediction_time` (`prediction_time`),
  CONSTRAINT `fk_prediction_scale` FOREIGN KEY (`scale_id`) REFERENCES `station_point_scale` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_prediction_task` FOREIGN KEY (`task_id`) REFERENCES `survey_route_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_prediction_times` FOREIGN KEY (`times_id`) REFERENCES `survey_times` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='水质预测记录表';

-- ----------------------------
-- Table structure for station_route_path 
-- ----------------------------
DROP TABLE IF EXISTS `station_route_path`;
CREATE TABLE `station_route_path` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL COMMENT '任务ID',
  `from_station_id` int NOT NULL COMMENT '起始站点ID',
  `to_station_id` int NOT NULL COMMENT '目标站点ID',
  `path_order` int NOT NULL COMMENT '路径顺序',
  `distance` decimal(10,2) DEFAULT NULL COMMENT '距离(km)',
  `estimated_time` int DEFAULT NULL COMMENT '预计时间(分钟)',
  `path_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'direct' COMMENT '路径类型：direct-直接，curved-弯曲',
  `waypoints` json COMMENT '路径中间点(JSON格式)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_from_station` (`from_station_id`),
  KEY `idx_to_station` (`to_station_id`),
  KEY `idx_path_order` (`path_order`),
  CONSTRAINT `fk_route_path_task` FOREIGN KEY (`task_id`) REFERENCES `survey_route_task` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_route_path_from` FOREIGN KEY (`from_station_id`) REFERENCES `station_point_distribute` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_route_path_to` FOREIGN KEY (`to_station_id`) REFERENCES `station_point_distribute` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='站点路径表';

-- ----------------------------
-- 初始化示例数据
-- ----------------------------
SET FOREIGN_KEY_CHECKS = 0;

-- 为每个调查中心添加航线任务，确保完整的数据关联
-- 调查中心1的航线任务
INSERT IGNORE INTO `survey_route_task` (`id`, `scale_id`, `name`, `code`, `description`, `start_longitude`, `start_latitude`, `end_longitude`, `end_latitude`, `total_distance`, `estimated_duration`) VALUES 
(1, 1, '青岛近海东线', 'QD-E-001', '调查中心1东线航线', 119.650000, 34.520000, 119.950000, 34.690000, 25.8, 6),
(2, 1, '青岛近海西线', 'QD-W-001', '调查中心1西线航线', 119.680000, 34.540000, 119.880000, 34.650000, 18.5, 5);

-- 调查中心2的航线任务
INSERT IGNORE INTO `survey_route_task` (`id`, `scale_id`, `name`, `code`, `description`, `start_longitude`, `start_latitude`, `end_longitude`, `end_latitude`, `total_distance`, `estimated_duration`) VALUES 
(3, 2, '日照近海A线', 'RZ-A-001', '调查中心2A线航线', 119.980000, 34.640000, 120.220000, 34.780000, 22.3, 5),
(4, 2, '日照近海B线', 'RZ-B-001', '调查中心2B线航线', 120.000000, 34.660000, 120.200000, 34.760000, 16.2, 4);

-- 调查中心3的航线任务
INSERT IGNORE INTO `survey_route_task` (`id`, `scale_id`, `name`, `code`, `description`, `start_longitude`, `start_latitude`, `end_longitude`, `end_latitude`, `total_distance`, `estimated_duration`) VALUES 
(5, 3, '威海近海主线', 'WH-M-001', '调查中心3主线航线', 120.720000, 35.420000, 120.920000, 35.620000, 28.7, 7),
(6, 3, '威海近海辅线', 'WH-S-001', '调查中心3辅线航线', 120.750000, 35.440000, 120.900000, 35.590000, 21.4, 5);

-- 海岸带调查的航线任务
INSERT IGNORE INTO `survey_route_task` (`id`, `scale_id`, `name`, `code`, `description`, `start_longitude`, `start_latitude`, `end_longitude`, `end_latitude`, `total_distance`, `estimated_duration`) VALUES 
(7, 4, '烟台近海A线', 'YT-A-001', '海岸带调查A线航线', 120.320000, 36.060000, 120.380000, 36.120000, 12.5, 4),
(8, 4, '烟台近海B线', 'YT-B-001', '海岸带调查B线航线', 120.300000, 36.040000, 120.360000, 36.100000, 10.8, 3),
(9, 4, '烟台近海C线', 'YT-C-001', '海岸带调查C线航线', 120.340000, 36.080000, 120.400000, 36.140000, 15.2, 5);

-- 为每个航线任务添加调查次数
-- 调查中心1的调查次数
INSERT IGNORE INTO `survey_times` (`id`, `task_id`, `times`, `date`, `start_time`, `end_time`, `weather_condition`, `sea_condition`, `crew_count`, `status`) VALUES 
(1, 1, 1, '2024-10-25', '08:00:00', '14:00:00', '晴', '2级海况', 6, 2),
(2, 1, 2, '2024-10-26', '09:00:00', '15:00:00', '多云', '1级海况', 6, 2),
(3, 2, 1, '2024-10-27', '07:30:00', '12:30:00', '阴', '2级海况', 5, 2);

-- 调查中心2的调查次数
INSERT IGNORE INTO `survey_times` (`id`, `task_id`, `times`, `date`, `start_time`, `end_time`, `weather_condition`, `sea_condition`, `crew_count`, `status`) VALUES 
(4, 3, 1, '2024-11-02', '10:00:00', '15:00:00', '晴', '2级海况', 6, 2),
(5, 4, 1, '2024-11-03', '11:00:00', '16:00:00', '多云', '1级海况', 5, 2);

-- 调查中心3的调查次数
INSERT IGNORE INTO `survey_times` (`id`, `task_id`, `times`, `date`, `start_time`, `end_time`, `weather_condition`, `sea_condition`, `crew_count`, `status`) VALUES 
(6, 5, 1, '2024-11-16', '08:00:00', '15:00:00', '晴', '3级海况', 7, 2),
(7, 6, 1, '2024-11-17', '09:00:00', '14:00:00', '多云', '2级海况', 6, 2);

-- 海岸带调查的调查次数
INSERT IGNORE INTO `survey_times` (`id`, `task_id`, `times`, `date`, `start_time`, `end_time`, `weather_condition`, `sea_condition`, `crew_count`, `status`) VALUES 
(8, 7, 1, '2024-12-15', '09:00:00', '13:00:00', '晴', '2级海况', 6, 2),
(9, 7, 2, '2024-12-16', '08:30:00', '12:30:00', '多云', '1级海况', 6, 2),
(10, 8, 1, '2024-12-17', '10:00:00', '14:00:00', '晴', '2级海况', 5, 2),
(11, 9, 1, '2024-12-18', '07:00:00', '12:00:00', '阴', '3级海况', 7, 1);

-- 插入地图显示配置
INSERT IGNORE INTO `map_display_config` (`config_name`, `config_type`, `config_data`, `is_default`, `description`) VALUES 
('默认标记配置', 'marker', '{"color": "#2b8cbe", "size": "medium", "icon": "📍"}', 1, '默认的站点标记显示配置'),
('默认区域配置', 'polygon', '{"fillColor": "#ccebc5", "strokeColor": "rgba(43, 140, 190, 0.2)", "fillOpacity": 0.4}', 1, '默认的调查区域显示配置'),
('默认路径配置', 'route', '{"strokeColor": "#1890ff", "strokeWeight": 4, "strokeStyle": "dashed", "animationSpeed": 2}', 1, '默认的航线路径显示配置');

-- 更新站点数据，建立完整的关联关系

-- 调查中心1的站点关联配置 (青岛近海东线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 1, 
`times_id` = 1,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'standard',
`priority` = 1,
`accessibility` = 1
WHERE `id` IN (1, 2, 3);

-- 调查中心1的其余站点 (青岛近海西线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 2, 
`times_id` = 3,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 0,
`mr_activities` = 1,
`station_type` = 'standard',
`priority` = 2,
`accessibility` = 1
WHERE `id` IN (4, 5);

-- 调查中心2的站点关联配置 (日照近海A线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 3, 
`times_id` = 4,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'reference',
`priority` = 1,
`accessibility` = 1
WHERE `id` IN (6, 7);

-- 调查中心2的其余站点 (日照近海B线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 4, 
`times_id` = 5,
`wp_activities` = 1,
`ci_activities` = 0,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'reference',
`priority` = 2,
`accessibility` = 1
WHERE `id` IN (8);

-- 调查中心3的站点关联配置 (威海近海主线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 5, 
`times_id` = 6,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 0,
`station_type` = 'special',
`priority` = 1,
`accessibility` = 1
WHERE `id` IN (9, 10);

-- 调查中心3的其余站点 (威海近海辅线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 6, 
`times_id` = 7,
`wp_activities` = 0,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 0,
`station_type` = 'special',
`priority` = 2,
`accessibility` = 1
WHERE `id` IN (11);

-- 海岸带调查的站点关联配置 (烟台近海A线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 7, 
`times_id` = 8,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'standard',
`priority` = 1,
`accessibility` = 1
WHERE `id` IN (12, 13);

-- 海岸带调查的其余站点 (烟台近海B线和C线任务)
UPDATE `station_point_distribute` SET 
`task_id` = 8, 
`times_id` = 10,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'standard',
`priority` = 1,
`accessibility` = 1
WHERE `id` = 14;

UPDATE `station_point_distribute` SET 
`task_id` = 9, 
`times_id` = 11,
`wp_activities` = 1,
`ci_activities` = 1,
`mb_activities` = 1,
`mr_activities` = 1,
`station_type` = 'standard',
`priority` = 1,
`accessibility` = 1
WHERE `id` = 15;

SET FOREIGN_KEY_CHECKS = 1;

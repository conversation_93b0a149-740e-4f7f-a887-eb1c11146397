<!--------------------------------
-非生物要素检验模块
-1.海水化学分析
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出/导入功能 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="handleAdd()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="900" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select label-field="name" value-field="id" clearable v-model:value="queryItems.distributeId" filterable
          :options="stationOption" placeholder="请选择站点" />
      </MeQueryItem>

      <MeQueryItem label="采样层次" :label-width="70">
        <n-select clearable v-model:value="queryItems.sampleLayer" filterable :options="sampleLayerOption"
          placeholder="请选择采样层次" />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form ref="modalFormRef" label-placement="left" label-align="left" :label-width="100" :model="modalForm"
        :disabled="modalAction === 'view'">
        <n-form-item label="站点" path="distributeId" :rule="{
          required: true,
          message: '请选择站点',
          type:'number',
          trigger: ['change'],
        }">
          <!-- <n-input v-model:value="modalForm.algaeName" /> -->
          <n-select label-field="name" value-field="id" @change="changeSelect" clearable
            v-model:value="modalForm.distributeId" filterable :options="stationOption" placeholder="请选择站点" />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input disabled v-model:value="coordination.longitude" placeholder="经度"></n-input>
            </n-form-item>
            <n-form-item label="纬度">
              <n-input disabled v-model:value="coordination.latitude" placeholder="纬度"></n-input>
            </n-form-item>
          </div>
        </Transition>
        <n-form-item label="采样层次"  path="sampleLayer" :rule="{
          required: true,
          message: '请选择采样层次',
          type:'number',
          trigger: ['change'],
        }">
          <n-select clearable v-model:value="modalForm.sampleLayer" filterable :options="sampleLayerOption"
            placeholder="请选择采样层次" />
        </n-form-item>

        <n-form-item label="活性磷酸盐" path="activePhosphate" :rule="{
          required: true,
          message: '请输入活性磷酸盐含量',
          type: 'number',
          trigger: ['input', 'blur'],
        }">
          <n-input-number min=0 style="width: 100%;" placeholder="请输入活性磷酸盐含量" v-model:value="modalForm.activePhosphate">
            <template #suffix>
              mg/L
            </template>
          </n-input-number>
        </n-form-item>


        <n-form-item label="亚硝酸盐" path="nitriteNitrogen" :rule="{
          required: true,
          message: '请输入亚硝酸盐含量',
          type: 'number',
          trigger: ['input', 'blur'],
        }">
          <n-input-number min=0 style="width: 100%;" placeholder="请输入亚硝酸盐含量" v-model:value="modalForm.nitriteNitrogen">
            <template #suffix>
              mg/L
            </template>
          </n-input-number>
        </n-form-item>

        <n-form-item label="硝酸盐-氮" path="nitrateNitrogen" :rule="{
          required: true,
          message: '请输入硝酸盐-氮含量',
          type: 'number',
          trigger: ['input', 'blur'],
        }">
          <n-input-number min=0 style="width: 100%;" placeholder="请输入硝酸盐-氮含量" v-model:value="modalForm.nitrateNitrogen">
            <template #suffix>
              mg/L
            </template>
          </n-input-number>
        </n-form-item>

        <n-form-item label="氨根" path="ammoniaHydrogen" :rule="{
          required: true,
          message: '请输入氨根含量',
          type: 'number',
          trigger: ['input', 'blur'],
        }">
          <n-input-number min=0 style="width: 100%;" placeholder="请输入氨根含量" v-model:value="modalForm.ammoniaHydrogen">
            <template #suffix>
              mg/L
            </template>
          </n-input-number>
        </n-form-item>

      </n-form>
      <!-- <n-alert v-if="modalAction === 'edit'" type="warning" closable>
        详细信息需由用户本人补充修改
      </n-alert> -->
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import {createDiscreteApi, NAvatar, NButton, NSwitch, NTag} from 'naive-ui'
import api from './api'
const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})

const sampleTypeOption = ref([])

// const sampleLayer = ref(2)  //复用改该字段即可

// let sampleTypeList = ref([])

// const displaySampleLayer = computed(() => {
//   switch (sampleLayer.value) {
//     case 1:
//       return '底层';
//     case 2:
//       return '表层';
//   }
// });
const sampleLayerOption = [
  { label: "表层", value: 2 },
  { label: "底层", value: 1 }
]

const displaySampleTypeList = computed(() => {
  return modalForm.value.sampleTypeList?.map(item => item.id)
});

const getSampleTypeList = async () => {
  let { data } = await api.getListSampleTypes()
  sampleTypeOption.value = data
}

const getStationList = async () => {
  let { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}

const changeSampleType = (row) => {
  console.log(row);
  // sampleTypeList.value = row
  // modalForm.value.sampleTypeList=row
  modalForm.value.sampleTypeList = row.map(id => {
    const foundOption = sampleTypeOption.value.find(option => option.id === id);
    return foundOption;
  }).filter(Boolean);
}

const changeSelect = async (row) => {
  if (row != null) {
    console.log(row);
    stationOption.value.filter(item => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
        return
      }
    })
  } else {
    coordination.value = {}
  }
}

onMounted(() => {
  // queryItems.value.sampleLayer = sampleLayer.value
  $table.value?.handleSearch()
  getStationList()
  getSampleTypeList()
  // modalForm.value.sampleLayer = sampleLayer.value
  // console.log(modalForm.value);

})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit
} = useCrud({
  name: '海水化学数据',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '站点',
    // width: 100,
    fixed: 'left',
    key: "stationPointDistribute.name",
    render(row) {
      return h(NTag,
        { type: 'primary' },
        { default: () => row.stationPointDistribute.name })
    }
  },
  {
    title: '采样层次',
    ellipsis: { tooltip: true },
    render(row) {
      const sampleLayer = row.sampleLayer;
      let tagType = 'primary'; // 默认颜色

      if (sampleLayer === 2) {
        tagType = 'success'; // 表层颜色
      } else if (sampleLayer === 1) {
        tagType = 'warning'; // 底层颜色
      }

      return h(NTag,
        { type: tagType },
        {
          default: () => sampleLayerOption.map(item => {
            if (item.value === sampleLayer) {
              return item.label;
            }
          }).filter(label => label !== undefined)[0] || ''
        }
      );
    }
  },
  // {
  //   title: '经度', ellipsis: { tooltip: true },
  //   align: 'left',
  //   fixed: 'left',
  //   render(row) {
  //     return h('span', row.stationPointDistribute.longitude)
  //   },
  // },
  // {
  //   title: '纬度', ellipsis: { tooltip: true },
  //   align: 'left',
  //   fixed: 'left',
  //   render(row) {
  //     return h('span', row.stationPointDistribute.latitude)
  //   },
  // },
  {
    title: '活性磷酸盐',
    ellipsis: { tooltip: true },
    render(row) {
      const activePhosphate = row.activePhosphate !== undefined && row.activePhosphate !== null ? `${row.activePhosphate}mg/L` : "请补充相关数据！";
      const style = row.activePhosphate !== undefined && row.activePhosphate !== null ? {} : { color: 'red' };
      return h('span', { style }, activePhosphate);
    },
  },
  {
    title: '亚硝酸盐',
    ellipsis: { tooltip: true },
    render(row) {
      const nitriteNitrogen = row.nitriteNitrogen !== undefined && row.nitriteNitrogen !== null ? `${row.nitriteNitrogen}mg/L` : "请补充相关数据！";
      const style = row.nitriteNitrogen !== undefined && row.nitriteNitrogen !== null ? {} : { color: 'red' };
      return h('span', { style }, nitriteNitrogen);
    },
  },
  {
    title: '硝酸盐',
    ellipsis: { tooltip: true },
    render(row) {
      const nitrateNitrogen = row.nitrateNitrogen !== undefined && row.nitrateNitrogen !== null ? `${row.nitrateNitrogen}mg/L` : "请补充相关数据！";
      const style = row.nitrateNitrogen !== undefined && row.nitrateNitrogen !== null ? {} : { color: 'red' };
      return h('span', { style }, nitrateNitrogen);
    },
  },
  {
    title: '氨根',
    ellipsis: { tooltip: true },
    render(row) {
      const ammoniaHydrogen = row.ammoniaHydrogen !== undefined && row.ammoniaHydrogen !== null ? `${row.ammoniaHydrogen}mg/L` : "请补充相关数据！";
      const style = row.ammoniaHydrogen !== undefined && row.ammoniaHydrogen !== null ? {} : { color: 'red' };
      return h('span', { style }, ammoniaHydrogen);
    },
  },
  {
    // width: 130,
    title: '操作',
    key: 'actions',
    // align: 'right',
    // fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            // style: 'margin-left: 12px;',
            onClick: () => handleEdit(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]
// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导出功能
async function handleExport() {
  try {
    const response = await api.exportExcel({
      distributeId: queryItems.value.distributeId,
      sampleLayer: queryItems.value.sampleLayer,
    })

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `海水化学离子数据_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importExcel(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style>

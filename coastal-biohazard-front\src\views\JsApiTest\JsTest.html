<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta
    name="viewport"
    content="initial-scale=1.0, user-scalable=no, width=device-width"
  />
  <title>HELLO，AMAP!</title>
  <style>
    html,
    body,
    #container {
      width: 100%;
      height: 100%;
    }

    .custom-content-marker {
      position: relative;
      width: 25px;
      height: 34px;
    }

    .custom-content-marker img {
      width: 100%;
      height: 100%;
    }

    .custom-content-marker .close-btn {
      position: absolute;
      top: -6px;
      right: -8px;
      width: 15px;
      height: 15px;
      font-size: 12px;
      background: #ccc;
      border-radius: 50%;
      color: #fff;
      text-align: center;
      line-height: 15px;
      box-shadow: -1px 1px 1px rgba(10, 10, 10, .2);
    }

    .custom-content-marker .close-btn:hover{
      background: #666;
    }
  </style>
  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "c04fd86c2e8f6d3120b5ea4f2bc76d24",
    };
  </script>
  <script src="https://webapi.amap.com/loader.js"></script>
  <script type="text/javascript">
    AMapLoader.load({
      key: "3b4146d6e0e773ad45d905f20e1a3070", //申请好的Web端开发者 Key，调用 load 时必填
      version: "2.0", //指定要加载的 JS API 的版本，缺省时默认为 1.4.15
    })
      .then((AMap) => {
        const map = new AMap.Map("container", {
          viewMode: '2D', //默认使用 2D 模式
          zoom: 11, //地图级别
          center: [119.215745, 34.597564], //地图中心点
        });

        const infoWindow = new AMap.InfoWindow({
          //创建信息窗体
          isCustom: true, //使用自定义窗体
          content: "<div>HELLO,AMAP!</div>", //信息窗体的内容可以是任意html片段
          offset: new AMap.Pixel(16, -45),
        });
        const onMarkerClick = function (e) {
          infoWindow.open(map, e.target.getPosition()); //打开信息窗体
          //e.target就是被点击的Marker
        };
        // const marker = new AMap.Marker({
        //   position: [119.215745, 34.597564],
        // });
        // map.add(marker);
        // marker.on("click", onMarkerClick); //绑定click事件

        // const lineArr = [
        //   [116.368904, 39.913423],
        //   [116.382122, 39.901176],
        //   [116.387271, 39.912501],
        //   [116.398258, 39.904600]
        // ];
        // const polyline = new AMap.Polyline({
        //   path: lineArr, //设置线覆盖物路径
        //   strokeColor: "#3366FF", //线颜色
        //   strokeWeight: 5, //线宽
        //   strokeStyle: "solid", //线样式
        // });
        // map.add(polyline);

        //添加地图控件
        //异步加载控件
        AMap.plugin('AMap.ToolBar,AMap.ControlBar,AMap.Scale', function () {
          var toolbar = new AMap.ToolBar(); //缩放工具条实例化
          var ControlBar = new AMap.ControlBar();
          var Scale = new AMap.Scale();
          map.addControl(toolbar);
          map.addControl(ControlBar);
          map.addControl(Scale);
        });

        //点标记显示内容
        const markerContent = `<div class="custom-content-marker">
<img src="//a.amap.com/jsapi_demos/static/demo-center/icons/dir-via-marker.png">
<div class="close-btn" onclick="clearMarker()">X</div>
</div>`

        const position = new AMap.LngLat(119.215745, 34.597564); //Marker 经纬度
        const marker = new AMap.Marker({
          position: position,
          content: markerContent, //将 html 传给 content
          offset: new AMap.Pixel(-13, -30), //以 icon 的 [center bottom] 为原点
        });
        map.add(marker);
        function clearMarker() {
          map.remove(marker); //清除 marker
        }
        document.querySelector(".close-btn").onclick = clearMarker; //绑定点击事件

        //添加矢量多边形 Polygon
        const pathArr = [
          [
            //JSON.parse(sessionStorage.getItem('coordinates'))
            [
              [119.7789, 34.3102],
              [119.7279, 34.3548],
              [119.5723, 34.4361],
              [119.5093, 34.4898],
              [119.5624, 34.4864],
              [119.5856, 34.4547],
              [119.7694, 34.3907],
              [119.796, 34.3456],
              [119.7789, 34.3102],
            ],
          ],
        ];
        const polygon = new AMap.Polygon({
          path: pathArr, //多边形路径
          fillColor: "#ccebc5", //多边形填充颜色
          strokeOpacity: 1, //线条透明度
          fillOpacity: 0.5, //填充透明度
          strokeColor: "#2b8cbe", //线条颜色
          strokeWeight: 1, //线条宽度
          strokeStyle: "dashed", //线样式
          strokeDasharray: [5, 5], //轮廓的虚线和间隙的样式
        })

        //鼠标移入更改样式
        polygon.on("mouseover", () => {
          polygon.setOptions({
            fillOpacity: 0.7, //多边形填充透明度
            fillColor: "#7bccc4",
          });
        });
        //鼠标移出恢复样式
        polygon.on("mouseout", () => {
          polygon.setOptions({
            fillOpacity: 0.5,
            fillColor: "#ccebc5",
          });
        });
        map.add(polygon)


        const markerContents = [];
        for (let i = 0; i < 10; i++) {
          markerContents.push(`<div class="custom-content-marker"><img src="//a.amap.com/jsapi_demos/static/demo-center/icons/dir-via-marker.png">Marker ${i + 1}</div>`);
        }
        const positions = [
          [119.7789, 34.3102],
          [119.7279, 34.3548],
          [119.5723, 34.4361],
          [119.5093, 34.4898],
          [119.5624, 34.4864],
          [119.5856, 34.4547],
          [119.7694, 34.3907],
          [119.796, 34.3456],
          [119.7789, 34.3102],
        ];
        const markers = [];
        positions.forEach((position, index) => {
          const marker = new AMap.Marker({
            position: new AMap.LngLat(position[0], position[1]),
            content: markerContents[index],
            offset: new AMap.Pixel(-13, -30),
          });
          map.add(marker);
          markers.push(marker);
        });
        function clearMarker() {
          markers.forEach((marker) => {
            map.remove(marker);
          });
        }
        document.querySelector(".close-btn").onclick = clearMarker;



      })
      .catch((e) => {
        console.error(e); //加载错误提示
      });

  </script>
</head>
<body>
<div id="container"></div>
</body>
</html>

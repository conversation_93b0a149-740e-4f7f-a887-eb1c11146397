package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.DistributeAndWaterDto;
import cn.dhbin.isme.ims.domain.dto.StationPointDto;
import cn.dhbin.isme.ims.domain.dto.WaterPhWeatherDataDto;
import cn.dhbin.isme.ims.domain.dto.excel.StationPointScaleExcelDto;
import cn.dhbin.isme.ims.domain.entity.AbundanceLayerSpeciesData;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.domain.request.StationPointScaleRequest;
import cn.dhbin.isme.ims.domain.request.WaterPhWeatherDataRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.mapper.WaterPhWeatherDataMapper;
import cn.dhbin.isme.ims.service.StationPointScaleService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/station-point-scale")
@RequiredArgsConstructor
public class StationPointScaleController {

    private final StationPointScaleService stationPointScaleService;

    private final StationPointDistributeMapper stationPointDistributeMapper;

    private final WaterPhWeatherDataMapper waterPhWeatherDataMapper;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<StationPointScale>> selectAll(StationPointScaleRequest request) {
        Page<StationPointScale> ret = stationPointScaleService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody StationPointScale data) {
        stationPointScaleService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody StationPointScale data) {
        data.setCreateTime(new Date());
        stationPointScaleService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        try {
            stationPointScaleService.removeById(id);
            return R.ok();
        } catch (BizException e) {
            return R.build(new BizException(e.getCode(),e.getMessage()));
        }
    }

    @GetMapping("/list")
    public R<List<?>> listStationScales() {
        List<StationPointScale> stationPoints = stationPointScaleService.list();
        return R.ok(stationPoints);
    }

    @GetMapping("/pointsList/{id}")
    public R<?> getPointDistributes(@PathVariable Integer id) {
        StationPointScale scale = stationPointScaleService.getById(id);
        StationPointDto stationPointDto = new StationPointDto();
        stationPointDto.setStationPointScale(scale);

        LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StationPointDistribute::getScaleId, id);
        List<StationPointDistribute> stationPointDistributes = stationPointDistributeMapper.selectList(queryWrapper);

        List<DistributeAndWaterDto> distributeAndWaterDtos = new ArrayList<>();
        for (StationPointDistribute distribute : stationPointDistributes) {
            DistributeAndWaterDto dto = new DistributeAndWaterDto();
            BeanUtils.copyProperties(distribute, dto);

            WaterPhWeatherData waterPhWeatherDataByDistributeId = waterPhWeatherDataMapper.getWaterPhWeatherDataByDistributeId(distribute.getId());
            if (waterPhWeatherDataByDistributeId != null) {
                dto.setSaltExtent(waterPhWeatherDataByDistributeId.getSaltExtent() != null ? waterPhWeatherDataByDistributeId.getSaltExtent().doubleValue() : null);
                dto.setPhExtent(waterPhWeatherDataByDistributeId.getPhExtent() != null ? waterPhWeatherDataByDistributeId.getPhExtent().doubleValue() : null);
                dto.setWaterTemperature(waterPhWeatherDataByDistributeId.getWaterTemperature() != null ? waterPhWeatherDataByDistributeId.getWaterTemperature().doubleValue() : null);
                dto.setTransparentExtent(waterPhWeatherDataByDistributeId.getTransparentExtent() != null ? waterPhWeatherDataByDistributeId.getTransparentExtent().doubleValue() : null);
            }

            distributeAndWaterDtos.add(dto);
        }

        stationPointDto.setStationPointDistribute(distributeAndWaterDtos);
        return R.ok(stationPointDto);
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 读取Excel文件
            List<StationPointScaleExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(StationPointScaleExcelDto.class)
                    .sheet()
                    .doReadSync();

            // 转换DTO到实体类
            List<StationPointScale> entities = list.stream()
                    .map(dto -> {
                        StationPointScale entity = new StationPointScale();

                        // 只处理有值的字段
                        if (dto.getName() != null && !dto.getName().trim().isEmpty()) {
                            entity.setName(dto.getName().trim());
                        }

                        if (dto.getLongitude() != null) {
                            entity.setLongitude(dto.getLongitude());
                        }

                        if (dto.getLatitude() != null) {
                            entity.setLatitude(dto.getLatitude());
                        }

                        if (dto.getDescription() != null) {
                            entity.setDescription(dto.getDescription());
                        }

                        entity.setCreateTime(new Date());
                        return entity;
                    })
                    .filter(entity -> entity.getName() != null && !entity.getName().isEmpty()) // 过滤掉没有名称的记录
                    .collect(Collectors.toList());

            if (entities.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11013, "没有有效数据需要导入"));
            }

            boolean result = stationPointScaleService.saveBatch(entities);
            return result ? R.ok("导入成功") : R.build(new BizException(BizResponseCode.ERR_11013, "导入失败"));
        } catch (Exception e) {
            e.printStackTrace(); // 打印完整堆栈，便于调试
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    public void exportExcel(StationPointScaleRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<StationPointScale> list = stationPointScaleService.queryList(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "调查中心数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, StationPointScaleExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("调查中心数据")
                    .doWrite(list.stream().map(data -> {
                        StationPointScaleExcelDto excelDto = new StationPointScaleExcelDto();
                        excelDto.setId(String.valueOf(data.getId()));
                        excelDto.setName(data.getName());
                        excelDto.setLongitude(data.getLongitude());
                        excelDto.setLatitude(data.getLatitude());
                        excelDto.setDescription(data.getDescription());
//                        excelDto.setCreateTime(String.valueOf(data.getCreateTime()));
                        return excelDto;
                    }).collect(Collectors.toList()));

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }
}

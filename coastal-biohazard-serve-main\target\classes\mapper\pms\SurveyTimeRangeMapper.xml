<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.dhbin.isme.ims.mapper.SurveyTimeRangeMapper">
    <select id="selectBatchByDistributeIds" parameterType="java.util.List" resultType="cn.dhbin.isme.ims.domain.entity.SurveyTimeRange">
        SELECT * FROM survey_time_range
        WHERE distribute_id IN
        <foreach item="item" index="index" collection="distributeIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
import { request } from '@/utils'

export default {
  create: data => request.post('/abundance', data),
  read: (params = {}) => request.get('/abundance', { params }),
  update: data => request.patch(`/abundance`, data),
  delete: id => request.delete(`/abundance/${id}`),
  getListStationPoints:(scaleId)=>request.get(`/station-point-distribute/list?scaleId=${scaleId}`),
  getStationPoint:(distributeId)=>request.get(`/station-point-distribute/${distributeId}`),
  getListSampleTypes:()=>request.get("/analysis-sample-type/list"),
  uploadImg:(file)=>request.post("/upload/img",file,{
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  readMorphological:(params = {}) => request.get('/morphological-analysis-data', { params }),
  updateMorphological:(data)=>request.patch(`/morphological-analysis-data`,data),
  deleteMorphological:(id)=>request.delete(`/morphological-analysis-data/${id}`),
  createMorphological:(data)=>request.post(`/morphological-analysis-data`,data),

  createSampleType: data => request.post('/sample-type', data),
  readSampleType: (params = {}) => request.get('/sample-type', { params }),
  updateSampleType: data => request.patch(`/sample-type`, data),
  deleteSampleType: id => request.delete(`/sample-type/${id}`),
}
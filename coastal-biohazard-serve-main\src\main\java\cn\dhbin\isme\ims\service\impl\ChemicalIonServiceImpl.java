package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.ChemicalIonDto;

import cn.dhbin.isme.ims.domain.dto.excel.ChemicalIonExcelDto;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.domain.request.ChemicalIonRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.ChemicalIonMapper;
import cn.dhbin.isme.ims.domain.entity.ChemicalIon;
import cn.dhbin.isme.ims.service.ChemicalIonService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 化学离子表(ChemicalIon)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-28 12:52:05
 */
@Service("chemicalIonService")
public class ChemicalIonServiceImpl extends ServiceImpl<ChemicalIonMapper, ChemicalIon> implements ChemicalIonService {

    @Autowired
    private ChemicalIonMapper chemicalIonMapper;

    @Autowired
    private StationPointDistributeMapper stationPointDistributeMapper;

    @Override
    public Page<ChemicalIonDto> queryPage(ChemicalIonRequest request) {
        IPage<ChemicalIon> qp = request.toPage();
        LambdaQueryWrapper<ChemicalIon> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getDistributeId() != null) {
            queryWrapper.eq(ChemicalIon::getDistributeId, request.getDistributeId());
        }

        if (request.getSampleLayer() != null) {
            queryWrapper.eq(ChemicalIon::getSampleLayer, request.getSampleLayer());
        }


        IPage<ChemicalIon> ret = chemicalIonMapper.selectPage(qp, queryWrapper);

        IPage<ChemicalIonDto> dtoIPage = ret.convert(data -> {
            // 初始化目标对象
            ChemicalIonDto dataDto = new ChemicalIonDto();

            // 复制属性
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点分布信息
            StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());

            // 确保stationPointDistribute不为空再复制属性
            if (stationPointDistribute != null) {
                dataDto.setStationPointDistribute(new StationPointDistribute());
                BeanUtils.copyProperties(stationPointDistribute, dataDto.getStationPointDistribute());
            }

            return dataDto;
        });

        return Page.convert(dtoIPage);
    }

    @Override
    public List<ChemicalIon> getChemicalIonByDistributeIds(List<Integer> distributeIds) {
        // 获取所有 distributeId 对应的 name
        List<StationPointDistribute> distributes = stationPointDistributeMapper.selectBatchIds(distributeIds);
        Map<Integer, String> distributeNameMap = distributes.stream()
                .collect(Collectors.toMap(StationPointDistribute::getId, StationPointDistribute::getName));

        // 查询 water_ph_weather_data 表中的记录
        List<ChemicalIon> chemicalIonList = chemicalIonMapper.selectBatchByDistributeIds(distributeIds);

        // 将 distributeName 附加到每个 WaterPhWeatherData 对象中
        for (ChemicalIon data : chemicalIonList) {
            data.setDistributeName(distributeNameMap.get(data.getDistributeId()));
        }

        return chemicalIonList;
    }
    @Override
    public List<ChemicalIonExcelDto> getExportData(Integer distributeId, Integer sampleLayer) {
        // 构建查询条件
        LambdaQueryWrapper<ChemicalIon> wrapper = new LambdaQueryWrapper<>();
        if (distributeId != null) {
            wrapper.eq(ChemicalIon::getDistributeId, distributeId);
        }
        if (sampleLayer != null) {
            wrapper.eq(ChemicalIon::getSampleLayer, sampleLayer);
        }

        // 获取数据列表
        List<ChemicalIon> list = this.list(wrapper);
        if (list.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取站点信息
        List<Integer> distributeIds = list.stream()
                .map(ChemicalIon::getDistributeId)
                .distinct()
                .collect(Collectors.toList());
        List<StationPointDistribute> stations = stationPointDistributeMapper.selectBatchIds(distributeIds);
        Map<Integer, String> stationMap = stations.stream()
                .collect(Collectors.toMap(StationPointDistribute::getId, StationPointDistribute::getName));

        // 转换为Excel DTO
        return list.stream().map(item -> {
            ChemicalIonExcelDto dto = new ChemicalIonExcelDto();
            dto.setId(String.valueOf(item.getId()));
            dto.setDistributeName(stationMap.get(item.getDistributeId()));
            dto.setSampleLayer(item.getSampleLayer() == 1 ? "底层" : "表层");
            dto.setActivePhosphate(item.getActivePhosphate() != null ? item.getActivePhosphate().doubleValue() : null);
            dto.setNitriteNitrogen(item.getNitriteNitrogen() != null ? item.getNitriteNitrogen().doubleValue() : null);
            dto.setNitrateNitrogen(item.getNitrateNitrogen() != null ? item.getNitrateNitrogen().doubleValue() : null);
            dto.setAmmoniaHydrogen(item.getAmmoniaHydrogen() != null ? item.getAmmoniaHydrogen().doubleValue() : null);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importData(List<ChemicalIonExcelDto> dataList) {
        if (dataList.isEmpty()) {
            return;
        }

        // 获取站点信息
        List<StationPointDistribute> stations = stationPointDistributeMapper.selectList(null);
        Map<String, Integer> stationMap = stations.stream()
                .collect(Collectors.toMap(StationPointDistribute::getName, StationPointDistribute::getId));

        // 转换并保存数据
        List<ChemicalIon> entities = dataList.stream()
                .map(dto -> {
                    ChemicalIon entity = new ChemicalIon();
                    entity.setDistributeId(stationMap.get(dto.getDistributeName()));
                    entity.setSampleLayer("底层".equals(dto.getSampleLayer()) ? 1 : 2);
                    entity.setActivePhosphate(dto.getActivePhosphate() != null ? BigDecimal.valueOf(dto.getActivePhosphate()) : null);
                    entity.setNitriteNitrogen(dto.getNitriteNitrogen() != null ? BigDecimal.valueOf(dto.getNitriteNitrogen()) : null);
                    entity.setNitrateNitrogen(dto.getNitrateNitrogen() != null ? BigDecimal.valueOf(dto.getNitrateNitrogen()) : null);
                    entity.setAmmoniaHydrogen(dto.getAmmoniaHydrogen() != null ? BigDecimal.valueOf(dto.getAmmoniaHydrogen()) : null);
                    return entity;
                })
                .collect(Collectors.toList());

        this.saveBatch(entities);
    }

    @Override
    public List<ChemicalIonDto> getByStationId(Integer stationId) {
        // 创建查询条件
        LambdaQueryWrapper<ChemicalIon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChemicalIon::getDistributeId, stationId);
        queryWrapper.orderByDesc(ChemicalIon::getCreateTime);

        // 查询数据
        List<ChemicalIon> list = chemicalIonMapper.selectList(queryWrapper);

        // 转换为DTO
        return list.stream().map(data -> {
            ChemicalIonDto dataDto = new ChemicalIonDto();
            BeanUtils.copyProperties(data, dataDto);

            // 获取站点信息
            if (data.getDistributeId() != null) {
                StationPointDistribute stationPointDistribute = stationPointDistributeMapper.selectById(data.getDistributeId());
                dataDto.setStationPointDistribute(stationPointDistribute);
            }

            return dataDto;
        }).collect(Collectors.toList());
    }
}


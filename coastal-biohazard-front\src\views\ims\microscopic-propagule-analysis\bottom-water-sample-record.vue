<!--------------------------------
-微观繁殖体分析
-1.表层水样
-createBy：isla
--------------------------------->
<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 导入Excel按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="add()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>
 <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>
    <MeCrud ref="$table" v-model:query-items="queryItems" :scroll-x="1200" :columns="columns" :get-data="api.read">
      <MeQueryItem label="站点" :label-width="70">
        <n-select
          v-model:value="queryItems.distributeId" label-field="name" value-field="id" clearable filterable
          :options="stationOption" placeholder="请选择站点"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" label-placement="left" label-align="left" :rules="formRules" :label-width="120"
        :model="modalForm" :disabled="modalAction === 'view'"
      >
        <n-form-item
          label="站点" path="distributeId" :rule="{
            required: true,
            message: '请选择站点',
            type: 'number',
            trigger: ['change'],
          }"
        >
          <!-- <n-input v-model:value="modalForm.algaeName" /> -->
          <n-select
            v-model:value="modalForm.distributeId" label-field="name" value-field="id" clearable
            filterable :options="stationOption" placeholder="请选择站点" @change="changeSelect"
          />
        </n-form-item>
        <Transition name="fade">
          <div v-show="coordination.longitude">
            <n-form-item label="经度">
              <n-input v-model:value="coordination.longitude" disabled placeholder="经度" />
            </n-form-item>
            <n-form-item label="纬度">
              <n-input v-model:value="coordination.latitude" disabled placeholder="纬度" />
            </n-form-item>
          </div>
        </Transition>
        <n-form-item label="样品类型">
          <n-input v-model:value="displaySampleType" disabled placeholder="样品类型" />
        </n-form-item>
        <!-- <n-form-item label="丰度" path="abundance" :rule="{
          required: true,
          message: '请输入丰度',
          // trigger: ['input', 'blur'],
        }">
          <n-input v-model:value="modalForm.abundance">
            <template #suffix>
              ind./50g
            </template>
          </n-input>
        </n-form-item> -->
        <!-- <n-form-item label="样品种类">
          <n-select @change="changeSampleType" v-model:value="displaySampleTypeList" :options="sampleTypeOption"
            label-field="name" value-field="id" clearable filterable multiple />
        </n-form-item>

        <div v-for="item in modalForm.sampleTypeList">
          <n-form-item  :label="`${item.name}生物量`" >
            <n-input-number style="width: 100%;" v-model:value="item.number" :placeholder="`请输入${item.name}生物量`">
              <template #suffix>
                ind./50g
              </template>
            </n-input-number>
          </n-form-item>
        </div> -->

        <n-form-item
          label="样品种类" path="sampleTypeList" :rule="{
            required: true,
            message: '请选择至少一个样品种类',
            type: 'array',
            trigger: ['input', 'blur'],
          }"
        >
          <n-select
            v-model:value="displaySampleTypeList" :options="sampleTypeOption" label-field="name"
            value-field="id" clearable filterable multiple @change="changeSampleType"
          />
        </n-form-item>

        <div v-for="(item, index) in modalForm.sampleTypeList" :key="item.id">
          <n-form-item
            :label="`${item.name}生物量`" :path="`sampleTypeList.${index}.number`"
            :rule="getValidationRule(item)"
          >
            <n-input-number v-model:value="item.number" style="width: 100%;" :placeholder="`请输入${item.name}生物量`">
              <template #suffix>
                ind./50g
              </template>
            </n-input-number>
          </n-form-item>
        </div>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { NButton, NTag } from 'naive-ui'
import api from './api'

const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})

const sampleTypeOption = ref([])

const sampleType = ref(1) // 复用改该字段即可

// let sampleTypeList = ref([])

const displaySampleType = computed(() => {
  switch (sampleType.value) {
    case 1:
      return '底层水样微观繁殖体'
    case 2:
      return '表层水样微观繁殖体'
    default:
      return '沉积物'
  }
})

const displaySampleTypeList = computed({
  get: () => modalForm.value.sampleTypeList?.map(item => item.id) || [],
  set: (selectedIds) => changeSampleType(selectedIds),
})


// 获取验证规则
function getValidationRule(item) {
  return {
    required: true,
    message: `请输入${item.name}生物量`,
    type: 'number',
    trigger: ['input', 'blur'],
  }
}

async function getSampleTypeList() {
  const { data } = await api.getListSampleTypes()
  sampleTypeOption.value = data
}

async function getStationList() {
  const { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}

function changeSampleType(selectedIds) {
  if (!selectedIds || !selectedIds.length) {
    modalForm.value.sampleTypeList = []
    return
  }

  const currentList = modalForm.value.sampleTypeList || []
  const existingItemsMap = new Map(currentList.map(item => [item.id, item]))

  modalForm.value.sampleTypeList = selectedIds.map(id => {
    const existingItem = existingItemsMap.get(id)
    if (existingItem) return existingItem // 保留已填写的 number

    const newItem = sampleTypeOption.value.find(option => option.id === id)
    return { ...newItem, number: null } // 新增的项，初始化 number
  })
}

async function changeSelect(row) {
  if (row != null) {
    stationOption.value.filter((item) => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
      }
    })
  }
  else {
    coordination.value = {}
  }
}

onMounted(() => {
  queryItems.value.sampleType = sampleType.value
  $table.value?.handleSearch()
  getStationList()
  getSampleTypeList()
  modalForm.value.sampleType = sampleType.value
  // console.log(modalForm.value);
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '生物量',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    width: 100,
    title: '站点',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'success' }, { default: () => row.stationPointDistribute.name })
    },
  },
  // {
  //   title: '经度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.stationPointDistribute.longitude)
  //   },
  // },
  // {
  //   title: '纬度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.stationPointDistribute.latitude)
  //   },
  // },
  {
    width: 200,
    title: '样品类型',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'primary' }, { default: () => displaySampleType.value })
    },
  },
  // {
  //   title: '丰度', ellipsis: { tooltip: true },
  //   render(row) {
  //     return h('span', row.abundance + " ind./50g")
  //   },
  // },
  {
    title: '种类及丰度',
    // width: 300,
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList)) {
        return h('span', '无数据')
      }
      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        return h(NTag, { key: index, type: 'info' }, { default: () => `${type.name} ${type.number}ind./L` })
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
  {
    width: 230,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            dashed: true,
            // secondary: true,
            onClick: () =>
              router.push({ path: `/ims/morphological-analysis`, query: { abundanceId: row.id, distributeName: row.stationPointDistribute.name } }),
          },
          {
            default: () => '形态分析',
            icon: () => h('i', { class: 'i-fe:trello text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleOpenUpdate(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function add() {
  coordination.value.longitude = null
  coordination.value.latitude = null
  handleOpen({
    action: 'add',
    title: '新增记录',
    // row: { id: row.id, username: row.username, roleIds },
    onOk: async () => {
      // console.log(modalForm.value);
      // console.log(sampleTypeList.value, "种类");
      await modalFormRef.value?.validate()
      await api.create({
        distributeId: modalForm.value.distributeId,
        sampleType: sampleType.value,
        abundance: modalForm.value.abundance,
        sampleTypes: modalForm.value.sampleTypeList,
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

function handleOpenUpdate(row) {
  console.log(row)

  coordination.value.longitude = row.stationPointDistribute.longitude
  coordination.value.latitude = row.stationPointDistribute.latitude
  // row.sampleTypeList=row.sampleTypeList.map(item=>item.id)
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row,
    onOk: updateSample,
  })
}

async function updateSample() {
  await modalFormRef.value?.validate()
  await api.update({
    id: modalForm.value.id,
    distributeId: modalForm.value.distributeId,
    sampleType: sampleType.value,
    abundance: modalForm.value.abundance,
    sampleTypes: modalForm.value.sampleTypeList,
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}
// 导出功能
async function handleExport() {
  try {
    const response = await api.exportBottomWaterSample(queryItems.value)

    if (!response || response.byteLength === 0) {
      throw new Error('响应数据为空')
    }

    // 创建Blob并下载
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `表层水样记录_${new Date().toISOString().slice(0, 10).replace(/-/g, '')}.xlsx`
    document.body.appendChild(link)
    link.click()

    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }, 100)

    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败: ${error.message}`)
  }
}

// 导入功能
const importLoading = ref(false)

async function handleImport({ file, onFinish, onError }) {
  if (importLoading.value)
    return // 阻止重复提交

  try {
    importLoading.value = true

    const formData = new FormData()
    formData.append('file', file.file || file)

    const { code, message: msg } = await api.importSurfaceWaterSample(formData)

    if (code === 0) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      message.error(msg || '导入失败')
    }
    onFinish()
  }
  catch (error) {
    message.error(`导入失败: ${error.message}`)
    onError()
  }
  finally {
    importLoading.value = false // 重置状态
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style>

package cn.dhbin.isme.ims.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import weka.core.Instances;
import weka.core.converters.ArffLoader;
import weka.core.converters.CSVLoader;
import weka.core.converters.ConverterUtils.DataSource;

import java.io.File;
import java.io.IOException;

@Service
public class DataLoaderService {

    public Instances loadData(String filePath) throws Exception {
        DataSource source = new DataSource(filePath);
        Instances data = source.getDataSet();

        // 设置分类目标列（假设最后一列是分类列）
        if (data.classIndex() == -1) {
            data.setClassIndex(data.numAttributes() - 1);
        }
        return data;
    }

    // 加载CSV文件数据
//    public Instances loadCsv(MultipartFile file) throws IOException {
//        CSVLoader loader = new CSVLoader();
//        // 将文件转换为临时文件来进行处理
//        File tempFile = convertMultipartFileToFile(file);
//        loader.setSource(tempFile);
//        Instances data = loader.getDataSet();
//        tempFile.delete(); // 处理完成后删除临时文件
//        return data;
//    }
    public Instances loadCsv(String filePath) throws IOException {
        CSVLoader loader = new CSVLoader();
        loader.setSource(new File(filePath));
        Instances data = loader.getDataSet();

        // 设置最后一列为类标签
        data.setClassIndex(data.numAttributes() - 1);

        return data;
    }


    // 加载ARFF文件数据
//    public Instances loadArff(MultipartFile file) throws IOException {
//        ArffLoader loader = new ArffLoader();
//        // 将文件转换为临时文件来进行处理
//        File tempFile = convertMultipartFileToFile(file);
//        loader.setSource(tempFile);
//        Instances data = loader.getDataSet();
//        tempFile.delete(); // 处理完成后删除临时文件
//        return data;
//    }
    public Instances loadArff(String filePath) throws IOException {
        ArffLoader loader = new ArffLoader();
        loader.setSource(new File(filePath));
        Instances data = loader.getDataSet();

        // 设置最后一列为类标签
        data.setClassIndex(data.numAttributes() - 1);

        return data;
    }


    // 将 MultipartFile 转换为普通 File
    private File convertMultipartFileToFile(MultipartFile file) throws IOException {
        File convFile = new File(file.getOriginalFilename());
        file.transferTo(convFile);
        return convFile;
    }
}

package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.StationPointScale;
import cn.dhbin.isme.ims.domain.entity.SurveyRouteTask;
import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 调查站位表(StationPointDistribute)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:38:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StationPointDistributeDto {
    private Integer id;

    private Integer scaleId;

    /**
     * 任务ID，关联survey_route_task表
     */
    private Integer taskId;
    
    /**
     * 调查次数ID，关联survey_times表
     */
    private Integer timesId;

    /**
     * 空间范围单点信息
     */
    private StationPointScale stationPointScale; //空间范围单点信息
    
    /**
     * 任务信息
     */
    private SurveyRouteTask surveyRouteTask; //任务信息

    private String name;

    private Double longitude;

    private Double latitude;

    private String description;
    
    /**
     * 水文气象采集活动：0-否，1-是
     */
    private Boolean wpActivities;
    
    /**
     * 化学样本采集活动：0-否，1-是
     */
    private Boolean ciActivities;
    
    /**
     * 成体生物量采集活动：0-否，1-是
     */
    private Boolean mbActivities;
    
    /**
     * 微观繁殖体采集活动：0-否，1-是
     */
    private Boolean mrActivities;
    
    /**
     * 站点类型：standard-标准站，reference-参考站，special-特殊站
     */
    private String stationType;
    
    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priority;
    
    /**
     * 可达性：0-不可达，1-可达
     */
    private Boolean accessibility;
    
    /**
     * 设备需求
     */
    private String equipmentRequirements;

    private Date beforeInvestigate;

    private Date afterInvestigate;

    private Date createTime;

    private Date updateTime;

    private List<Long> range;
public Serializable pkVal() {
          return null;
      }
}



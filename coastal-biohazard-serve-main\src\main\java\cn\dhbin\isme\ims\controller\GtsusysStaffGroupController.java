package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.excel.GtsusysStaffGroupExcelDto;
import cn.dhbin.isme.ims.domain.entity.GtsusysStaffGroup;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.GtsusysStaffGroupRequest;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import cn.dhbin.isme.ims.service.GtsusysStaffGroupService;
import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/gtsusys-staff-group")
@RequiredArgsConstructor
public class GtsusysStaffGroupController {
    private final GtsusysStaffGroupService gtsusysStaffGroupService;
    @GetMapping("/list")
    public R<List<?>> listStaffGroup() {
        List<GtsusysStaffGroup> groups = gtsusysStaffGroupService.list();
        return R.ok(groups);
    }

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<GtsusysStaffGroup>> selectAll(GtsusysStaffGroupRequest request) {
        Page<GtsusysStaffGroup> ret = gtsusysStaffGroupService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody GtsusysStaffGroup data) {
        gtsusysStaffGroupService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody GtsusysStaffGroup data) {
        gtsusysStaffGroupService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        gtsusysStaffGroupService.removeById(id);
        return R.ok();
    }
    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 读取Excel文件
            List<GtsusysStaffGroupExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(GtsusysStaffGroupExcelDto.class)
                    .sheet()
                    .doReadSync();

            // 转换DTO到实体类
            List<GtsusysStaffGroup> entities = list.stream()
                    .map(dto -> {
                        // 验证和设置必要字段
                        if (dto.getName() == null || dto.getName().trim().isEmpty()) {
                            throw new BizException(BizResponseCode.ERR_11013, "单位名称不能为空");
                        }

                        GtsusysStaffGroup entity = new GtsusysStaffGroup();
                        entity.setName(dto.getName().trim());
                        return entity;
                    })
                    .collect(Collectors.toList());

            if (entities.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11013, "没有有效数据需要导入"));
            }

            boolean result = gtsusysStaffGroupService.saveBatch(entities);
            return result ? R.ok("导入成功") : R.build(new BizException(BizResponseCode.ERR_11013, "导入失败"));
        } catch (BizException e) {
            return R.build(e);
        } catch (Exception e) {
            e.printStackTrace();
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    public void exportExcel(GtsusysStaffGroupRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<GtsusysStaffGroup> list = gtsusysStaffGroupService.queryList(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "人员单位数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, GtsusysStaffGroupExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("人员单位数据")
                    .doWrite(list.stream().map(data -> {
                        GtsusysStaffGroupExcelDto excelDto = new GtsusysStaffGroupExcelDto();
                        excelDto.setId(String.valueOf(data.getId()));
                        excelDto.setName(data.getName());
                        return excelDto;
                    }).collect(Collectors.toList()));

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }
}

package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbundanceLayerSpeciesDataRequest extends PageRequest {

    /**
     * 站点id
     **/
    private Integer distributeId;

    /**
     * 样品类型
     **/
    private Integer sampleType;


public Serializable pkVal() {
          return null;
      }
}



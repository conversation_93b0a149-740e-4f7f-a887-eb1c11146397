package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("analysis_sample_type")
public class AnalysisSampleType implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 名称
     **/
    private String name;

    @TableField(exist = false)
    private Double number;
public Serializable pkVal() {
          return null;
      }
}



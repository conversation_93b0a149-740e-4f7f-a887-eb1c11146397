package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.entity.Profile;
import cn.dhbin.isme.pms.domain.request.UpdateProfileRequest;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:06:05+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UpdateProfileRequestToProfileImpl implements UpdateProfileRequestToProfile {

    @Override
    public Profile to(UpdateProfileRequest arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Profile profile = new Profile();

        profile.setAddress( arg0.getAddress() );
        profile.setEmail( arg0.getEmail() );
        profile.setGender( arg0.getGender() );
        profile.setId( arg0.getId() );
        profile.setNickName( arg0.getNickName() );

        return profile;
    }
}

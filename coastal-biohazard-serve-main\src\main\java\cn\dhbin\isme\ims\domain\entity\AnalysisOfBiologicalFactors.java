package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("analysis_of_biological_factors")
public class AnalysisOfBiologicalFactors implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 站点id
     **/
    private Integer distributeId;
    
    /**
     * 样品类型
     **/
    private Integer sampleType;
    
    /**
     * 丰富度 ind./50g
     **/
    private Integer abundance;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 报告内容
     **/
    private String report;
    
    /**
     * 报告时间
     **/
    private Date reportTime;
    
public Serializable pkVal() {
          return null;
      }
}



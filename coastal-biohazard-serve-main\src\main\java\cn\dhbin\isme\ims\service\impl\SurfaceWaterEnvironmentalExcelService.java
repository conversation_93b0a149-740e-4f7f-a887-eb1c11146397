package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.mapper.WaterPhWeatherDataMapper;
import cn.dhbin.isme.ims.service.WaterPhWeatherDataService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;

@Service
@RequiredArgsConstructor
@Slf4j
public class SurfaceWaterEnvironmentalExcelService {
    private final WaterPhWeatherDataService waterPhWeatherDataService;
    private final WaterPhWeatherDataMapper waterPhWeatherDataMapper;
    private final StationPointDistributeMapper stationPointDistributeMapper;
    
    private static final int SURFACE_WATER_LAYER = 2; // 表层水样

    public byte[] exportToExcel(Integer sampleLayer) throws IOException {
        // 查询表层海水水域数据
        LambdaQueryWrapper<WaterPhWeatherData> queryWrapper = new LambdaQueryWrapper<>();
        
        if (sampleLayer != null) {
            queryWrapper.eq(WaterPhWeatherData::getSampleLayer, sampleLayer);
        } else {
            queryWrapper.eq(WaterPhWeatherData::getSampleLayer, SURFACE_WATER_LAYER);
        }
        
        List<WaterPhWeatherData> dataList = waterPhWeatherDataMapper.selectList(queryWrapper);
        log.info("表层海水水域数据查询结果: sampleLayer={}, 查询到{}条记录", sampleLayer, dataList.size());
        
        if (dataList.isEmpty()) {
            log.warn("表层海水水域数据为空，sampleLayer={}", sampleLayer);
        }

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("表层海水水域数据");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {"站点", "采样层次", "天气现象", "风向", "盐度", "PH值", "气温(℃)", "水温(℃)", "透明度(m)", "创建时间", "更新时间"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // 填充数据行
            int rowNum = 1;
            for (WaterPhWeatherData data : dataList) {
                Row row = sheet.createRow(rowNum++);
                
                // 站点名称
                StationPointDistribute stationPoint = stationPointDistributeMapper.selectById(data.getDistributeId());
                String stationName = stationPoint != null ? stationPoint.getName() : "未知站点";
                row.createCell(0).setCellValue(stationName);
                
                // 采样层次
                String sampleLayerName = data.getSampleLayer() == 1 ? "底层" : "表层";
                row.createCell(1).setCellValue(sampleLayerName);
                
                // 天气现象
                row.createCell(2).setCellValue(data.getWeather() != null ? data.getWeather() : "");
                
                // 风向
                row.createCell(3).setCellValue(data.getWindDirection() != null ? data.getWindDirection() : "");
                
                // 盐度
                row.createCell(4).setCellValue(data.getSaltExtent() != null ? data.getSaltExtent().doubleValue() : 0);
                
                // PH值
                row.createCell(5).setCellValue(data.getPhExtent() != null ? data.getPhExtent().doubleValue() : 0);
                
                // 气温
                row.createCell(6).setCellValue(data.getAirTemperature() != null ? data.getAirTemperature().doubleValue() : 0);
                
                // 水温
                row.createCell(7).setCellValue(data.getWaterTemperature() != null ? data.getWaterTemperature().doubleValue() : 0);
                
                // 透明度
                row.createCell(8).setCellValue(data.getTransparentExtent() != null ? data.getTransparentExtent().doubleValue() : 0);
                
                // 创建时间
                row.createCell(9).setCellValue(data.getCreateTime() != null ? 
                        data.getCreateTime().toString() : "");
                
                // 更新时间
                row.createCell(10).setCellValue(data.getUpdateTime() != null ? 
                        data.getUpdateTime().toString() : "");
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }

    public void importFromExcel(MultipartFile file, Integer sampleLayer) throws IOException {
        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            List<WaterPhWeatherData> dataList = new ArrayList<>();

            // 跳过表头行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                WaterPhWeatherData data = new WaterPhWeatherData();
                
                // 设置采样层次
                data.setSampleLayer(sampleLayer != null ? sampleLayer : SURFACE_WATER_LAYER);
                
                // 要求选择站点ID
                Cell stationCell = row.getCell(0);
                if (stationCell != null) {
                    // 注意：Excel中填写的是站点名称，需要根据名称查找对应的ID
                    // 这里简化处理，实际应用中可能需要更复杂的逻辑
                    String stationName = stationCell.getStringCellValue();
                    LambdaQueryWrapper<StationPointDistribute> stationQuery = new LambdaQueryWrapper<>();
                    stationQuery.eq(StationPointDistribute::getName, stationName);
                    StationPointDistribute station = stationPointDistributeMapper.selectOne(stationQuery);
                    if (station != null) {
                        data.setDistributeId(station.getId());
                    } else {
                        // 如果站点不存在，跳过该行
                        continue;
                    }
                } else {
                    continue; // 跳过站点为空的行
                }
                
                // 天气现象
                Cell weatherCell = row.getCell(2);
                if (weatherCell != null && weatherCell.getCellType() == CellType.STRING) {
                    data.setWeather(weatherCell.getStringCellValue());
                }
                
                // 风向
                Cell windDirectionCell = row.getCell(3);
                if (windDirectionCell != null && windDirectionCell.getCellType() == CellType.STRING) {
                    data.setWindDirection(windDirectionCell.getStringCellValue());
                }
                
                // 盐度
                Cell saltExtentCell = row.getCell(4);
                if (saltExtentCell != null && saltExtentCell.getCellType() == CellType.NUMERIC) {
                    data.setSaltExtent(BigDecimal.valueOf(saltExtentCell.getNumericCellValue()));
                }
                
                // PH值
                Cell phExtentCell = row.getCell(5);
                if (phExtentCell != null && phExtentCell.getCellType() == CellType.NUMERIC) {
                    data.setPhExtent(BigDecimal.valueOf(phExtentCell.getNumericCellValue()));
                }
                
                // 气温
                Cell airTemperatureCell = row.getCell(6);
                if (airTemperatureCell != null && airTemperatureCell.getCellType() == CellType.NUMERIC) {
                    data.setAirTemperature(BigDecimal.valueOf(airTemperatureCell.getNumericCellValue()));
                }
                
                // 水温
                Cell waterTemperatureCell = row.getCell(7);
                if (waterTemperatureCell != null && waterTemperatureCell.getCellType() == CellType.NUMERIC) {
                    data.setWaterTemperature(BigDecimal.valueOf(waterTemperatureCell.getNumericCellValue()));
                }
                
                // 透明度
                Cell transparentExtentCell = row.getCell(8);
                if (transparentExtentCell != null && transparentExtentCell.getCellType() == CellType.NUMERIC) {
                    data.setTransparentExtent(BigDecimal.valueOf(transparentExtentCell.getNumericCellValue()));
                }

                dataList.add(data);
            }

            // 保存数据
            if (!dataList.isEmpty()) {
                waterPhWeatherDataService.saveBatch(dataList);
                log.info("成功导入{}条表层海水水域数据", dataList.size());
            } else {
                log.warn("没有有效的表层海水水域数据可导入");
            }
        }
    }
} 
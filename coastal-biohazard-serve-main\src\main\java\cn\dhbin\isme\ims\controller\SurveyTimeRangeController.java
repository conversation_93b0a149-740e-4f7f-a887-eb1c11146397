package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.excel.SurveyTimeRangeExcelDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import cn.dhbin.isme.ims.domain.request.MorphologicalAnalysisDataRequest;
import cn.dhbin.isme.ims.domain.request.SurveyTimeRangeRequest;
import cn.dhbin.isme.ims.service.SurveyTimeRangeService;
import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/survey-time-range")
@RequiredArgsConstructor
public class SurveyTimeRangeController {
    private final SurveyTimeRangeService surveyTimeRangeService;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<SurveyTimeRange>> selectAll(SurveyTimeRangeRequest request) {
        Page<SurveyTimeRange> ret = surveyTimeRangeService.queryPage(request);
        return R.ok(ret);
    }

    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody SurveyTimeRange data) {
        if (data.getRange() != null && data.getRange().size() == 2) {
            long startTime = data.getRange().get(0);
            long endTime = data.getRange().get(1);

            // 转换时间戳为日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date beforeInvestigate = new Date(startTime);
            Date afterInvestigate = new Date(endTime);

            // 设置 beforeInvestigate 和 afterInvestigate
            data.setBeforeInvestigate(beforeInvestigate);
            data.setAfterInvestigate(afterInvestigate);
        }

        surveyTimeRangeService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody SurveyTimeRange data) {
        // 处理 range 字段，转换为正常的时间格式
        if (data.getRange() != null && data.getRange().size() == 2) {
            long startTime = data.getRange().get(0);
            long endTime = data.getRange().get(1);

            // 转换时间戳为日期
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date beforeInvestigate = new Date(startTime);
            Date afterInvestigate = new Date(endTime);

            // 设置 beforeInvestigate 和 afterInvestigate
            data.setBeforeInvestigate(beforeInvestigate);
            data.setAfterInvestigate(afterInvestigate);
        }

        surveyTimeRangeService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        surveyTimeRangeService.removeById(id);
        return R.ok();
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    public void exportExcel(SurveyTimeRangeRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<SurveyTimeRangeExcelDto> list = surveyTimeRangeService.queryListForExport(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "调查时间范围数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, SurveyTimeRangeExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("调查时间范围")
                    .doWrite(list);

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 读取Excel文件
            List<SurveyTimeRangeExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(SurveyTimeRangeExcelDto.class)
                    .sheet()
                    .doReadSync();

            surveyTimeRangeService.importData(list);
            return R.ok("导入成功");
        } catch (BizException e) {
            return R.build(e);
        } catch (Exception e) {
            e.printStackTrace();
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }
}

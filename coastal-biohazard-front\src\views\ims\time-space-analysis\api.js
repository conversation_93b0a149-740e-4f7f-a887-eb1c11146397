/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-11-27 14:53:24
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-17 14:06:35
 * @Description: 请填写简介
 */
import { request } from '@/utils'

export default {
  createManage: data => request.post('/gtsusys-staff-manage', data),
  readManage: (params = {}) => request.get('/gtsusys-staff-manage', { params }),
  updateManage: data => request.patch(`/gtsusys-staff-manage`, data),
  deleteManage: id => request.delete(`/gtsusys-staff-manage/${id}`),
  batchInsert: data => request.post('/gtsusys-staff-manage/batch-insert', data),

  createScale: data => request.post('/station-point-scale', data),
  readScale: (params = {}) => request.get('/station-point-scale', { params }),
  updateScale: data => request.patch(`/station-point-scale`, data),
  deleteScale: id => request.delete(`/station-point-scale/${id}`),

  createDistribute: data => request.post('/station-point-distribute', data),
  readDistribute: (params = {}) => request.get('/station-point-distribute', { params }),
  updateDistribute: data => request.patch(`/station-point-distribute`, data),
  deleteDistribute: id => request.delete(`/station-point-distribute/${id}`),
  getStationPoints: () => request.get('/station-point-scale/list'),

  // 旧版本接口：只根据调查中心ID获取数据（兼容性保留）
  getPointDistributesByScaleId: id => request.get(`/station-point-scale/pointsList/${id}`),

  getGroupList: () => request.get('/gtsusys-staff-group/list'),
  createGtsusysStaffGroup: data => request.post('/gtsusys-staff-group', data),
  readGtsusysStaffGroup: (params = {}) => request.get('/gtsusys-staff-group', { params }),
  updateGtsusysStaffGroup: data => request.patch(`/gtsusys-staff-group`, data),
  deleteGtsusysStaffGroup: id => request.delete(`/gtsusys-staff-group/${id}`),

  createSurveyTimeRange: data => request.post('/survey-time-range', data),
  readSurveyTimeRange: (params = {}) => request.get('/survey-time-range', { params }),
  updateSurveyTimeRange: data => request.patch(`/survey-time-range`, data),
  deleteSurveyTimeRange: id => request.delete(`/survey-time-range/${id}`),

  // 导出明确调查空间范围
  exportScale: (params = {}) => request.get('/station-point-scale/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入明确调查空间范围
  importScale: data => request.post('/station-point-scale/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出调查站点分布
  exportDistribute: (params = {}) => request.get('/station-point-distribute/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入调查站点分布
  importDistribute: data => request.post('/station-point-distribute/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出作业时间范围
  exportSurveyTimeRange: (params = {}) => request.get('/survey-time-range/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入作业时间范围
  importSurveyTimeRange: data => request.post('/survey-time-range/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // 导出一线作业人员
  exportManage: (params = {}) => request.get('/gtsusys-staff-manage/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入一线作业人员
  importManage: data => request.post('/gtsusys-staff-manage/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  // 导出人员单位数据
  exportStaffGroup: (params = {}) => request.get('/gtsusys-staff-group/export', {
    params,
    responseType: 'arraybuffer',
    headers: {
      'Cache-Control': 'no-cache',
    },
  }),

  // 导入人员单位数据
  importStaffGroup: data => request.post('/gtsusys-staff-group/import', data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),

  // ========== 站点地图分布功能模块接口 ==========
  // 获取调查中心列表
  getScaleList: () => request.get('/station-map-distribution/scales'),
  
  // 根据调查中心ID获取航线列表
  getRoutesByScaleId: scaleId => request.get(`/station-map-distribution/routes/by-scale/${scaleId}`),
  
  // 根据任务ID获取调查次数列表
  getSurveyTimesByTaskId: taskId => request.get(`/station-map-distribution/survey-times/by-task/${taskId}`),
  
  // 新版本接口：支持三级筛选的地图数据聚合
  getMapDataWithFilter: (scaleId, taskId, timesId) => request.get(`/station-map-distribution/map-data`, {
    params: { scaleId, taskId, timesId }
  }),

  // 统一的站点分布数据获取接口（智能选择版本）
  getPointDistributes: (scaleId, taskId = null, timesId = null) => {
    // 如果只有调查中心ID，使用旧版本接口（数据更完整）
    if (!taskId && !timesId) {
      return request.get(`/station-point-scale/pointsList/${scaleId}`)
    }
    // 如果有筛选条件，使用新版本接口
    return request.get(`/station-map-distribution/map-data`, {
      params: { scaleId, taskId, timesId }
    })
  },
  
  // 水质预测接口
  predictWaterQuality: (longitude, latitude, scaleId, taskId, timesId) => request.post('/water-quality-prediction/predict', null, {
    params: { longitude, latitude, scaleId, taskId, timesId }
  }),
  
  // 获取水质预测分析流
  getWaterQualityAnalysisStream: (longitude, latitude, scaleId, taskId, timesId) => {
    const params = new URLSearchParams({
      longitude,
      latitude,
      scaleId,
      taskId,
      timesId
    })
    return `/api/water-quality-prediction/analysis-stream?${params}`
  },

  // ========== 航线任务管理接口 ==========
  createSurveyRouteTask: data => request.post('/survey-route-task', data),
  readSurveyRouteTask: (params = {}) => request.get('/survey-route-task', { params }),
  updateSurveyRouteTask: data => request.patch('/survey-route-task', data),
  deleteSurveyRouteTask: id => request.delete(`/survey-route-task/${id}`),
  
  // ========== 调查次数管理接口 ==========
  createSurveyTimes: data => request.post('/survey-times', data),
  readSurveyTimes: (params = {}) => request.get('/survey-times', { params }),
  updateSurveyTimes: data => request.patch('/survey-times', data),
  deleteSurveyTimes: id => request.delete(`/survey-times/${id}`),
  
  // ========== 站点路径管理接口 ==========
  createStationRoutePath: data => request.post('/station-route-path', data),
  readStationRoutePath: (params = {}) => request.get('/station-route-path', { params }),
  updateStationRoutePath: data => request.patch('/station-route-path', data),
  deleteStationRoutePath: id => request.delete(`/station-route-path/${id}`),
  
  // 获取任务的所有路径
  getRoutePathsByTaskId: taskId => request.get(`/station-route-path/by-task/${taskId}`),
  
  // ========== 地图配置管理接口 ==========
  getMapDisplayConfigs: (type = null) => request.get('/map-display-config', {
    params: type ? { config_type: type } : {}
  }),
  createMapDisplayConfig: data => request.post('/map-display-config', data),
  updateMapDisplayConfig: data => request.patch('/map-display-config', data),
  deleteMapDisplayConfig: id => request.delete(`/map-display-config/${id}`),
  
  // 获取默认地图配置
  getDefaultMapConfig: type => request.get(`/map-display-config/default/${type}`),

  // ========== 站点详细数据获取接口 ==========
  // 获取站点完整详细数据（聚合所有相关监测数据）
  getStationDetailData: stationId => request.get(`/station-point-distribute/${stationId}/detail`),
  
  // 获取站点水文气象数据
  getStationWaterPhWeatherData: stationId => request.get(`/water-ph-weather-data/by-station/${stationId}`),
  
  // 获取站点化学离子数据
  getStationChemicalIonData: stationId => request.get(`/chemical-ion/by-station/${stationId}`),
  
  // 获取站点金属离子数据
  getStationMetalIonData: stationId => request.get(`/metal-ion/by-station/${stationId}`),
  
  // 获取站点生物多样性数据
  getStationBiodiversityData: stationId => request.get(`/biodiversity/by-station/${stationId}`),
  
  // 获取站点微观繁殖体数据
  getStationAnalysisOfBiologicalFactorsData: stationId => request.get(`/analysis-of-biological-factors/by-station/${stationId}`),
  
  // 获取站点形态分析数据
  getStationMorphologicalAnalysisData: stationId => request.get(`/morphological-analysis-data/by-station/${stationId}`),
  
  // 获取站点沉积物数据
  getStationSedimentData: stationId => request.get(`/sediment/by-station/${stationId}`),
  
  // 获取站点水质预测数据
  getStationWaterQualityPredictionData: (stationId, longitude, latitude) => request.get(`/water-quality-prediction/by-station/${stationId}`, {
    params: { longitude, latitude }
  }),

}

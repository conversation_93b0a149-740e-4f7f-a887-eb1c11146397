package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.dto.RolePageDto;
import cn.dhbin.isme.pms.domain.entity.Role;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:06:05+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class RoleToRolePageDtoImpl implements RoleToRolePageDto {

    @Override
    public RolePageDto to(Role arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RolePageDto rolePageDto = new RolePageDto();

        rolePageDto.setCode( arg0.getCode() );
        rolePageDto.setEnable( arg0.getEnable() );
        rolePageDto.setId( arg0.getId() );
        rolePageDto.setName( arg0.getName() );

        return rolePageDto;
    }
}

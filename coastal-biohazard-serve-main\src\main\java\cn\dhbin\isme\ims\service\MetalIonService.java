package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.MetalIonDto;
import cn.dhbin.isme.ims.domain.request.BiodiversityRequest;
import cn.dhbin.isme.ims.domain.request.MetalIonRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.dhbin.isme.ims.domain.entity.MetalIon;

import java.util.List;

/**
 * 金属离子表(MetalIon)表服务接口
 *
 * <AUTHOR>
 * @since 2024-11-27 22:29:06
 */
public interface MetalIonService extends IService<MetalIon> {
    Page<MetalIonDto> queryPage(MetalIonRequest request);

    /**
     * 根据站点ID获取金属离子数据
     * @param stationId 站点ID
     * @return 金属离子数据列表
     */
    List<MetalIonDto> getByStationId(Integer stationId);
}


package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AbundanceLayerSpeciesDataDto;
import cn.dhbin.isme.ims.domain.dto.WaterPhWeatherDataDto;
import cn.dhbin.isme.ims.domain.entity.SurveyTimeRange;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.domain.request.AbundanceLayerSpeciesDataRequest;
import cn.dhbin.isme.ims.domain.request.WaterPhWeatherDataRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
public interface WaterPhWeatherDataService extends IService<WaterPhWeatherData> {
    Page<WaterPhWeatherDataDto> queryPage(WaterPhWeatherDataRequest request);

    List<WaterPhWeatherData> getWaterPhWeatherDataByDistributeIds(List<Integer> distributeIds);
    /**
     * 查询列表数据（用于导出Excel）
     * @param request 查询条件
     * @return 水文气象数据列表
     */
    List<WaterPhWeatherDataDto> queryList(WaterPhWeatherDataRequest request);

    /**
     * 根据站点ID获取水文气象数据
     * @param stationId 站点ID
     * @return 水文气象数据列表
     */
    List<WaterPhWeatherDataDto> getByStationId(Integer stationId);

}


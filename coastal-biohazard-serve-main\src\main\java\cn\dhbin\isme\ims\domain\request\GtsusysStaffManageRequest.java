package cn.dhbin.isme.ims.domain.request;


import cn.dhbin.isme.common.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 一线作业人员表(GtsusysStaffManage)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:36:04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GtsusysStaffManageRequest extends PageRequest {

    private String name;

    private Integer gender;

    private Integer groupId; // 单位id

    
public Serializable pkVal() {
          return null;
      }
}



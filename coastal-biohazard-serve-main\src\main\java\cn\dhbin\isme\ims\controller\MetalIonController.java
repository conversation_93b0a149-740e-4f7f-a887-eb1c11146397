package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.BiodiversityDto;
import cn.dhbin.isme.ims.domain.dto.MetalIonDto;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.domain.entity.MetalIon;
import cn.dhbin.isme.ims.domain.request.BiodiversityRequest;
import cn.dhbin.isme.ims.domain.request.MetalIonRequest;
import cn.dhbin.isme.ims.service.MetalIonService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/metal-ion")
@RequiredArgsConstructor
public class MetalIonController {

    private final MetalIonService metalIonService;
    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<MetalIonDto>> selectAll(MetalIonRequest request) {
        Page<MetalIonDto> ret = metalIonService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody MetalIon data) {
        metalIonService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody MetalIon data) {
        metalIonService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        metalIonService.removeById(id);
        return R.ok();
    }

    /**
     * 根据站点ID获取金属离子数据
     * @param stationId 站点ID
     * @return 金属离子数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<MetalIonDto>> getByStationId(@PathVariable Integer stationId) {
        List<MetalIonDto> list = metalIonService.getByStationId(stationId);
        return R.ok(list);
    }
}

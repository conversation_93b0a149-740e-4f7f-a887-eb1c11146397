# 海岸带生物灾害监测系统架构规范

## 项目概述

海岸带生物灾害监测系统是一个前后端分离的全栈项目，用于海洋环境监测、生物多样性分析、时空数据分析等科研场景。

## 技术栈

### 前端技术栈
- **框架**: Vue 3.5+ (Composition API)
- **构建工具**: Vite 5.4+
- **UI组件库**: Naive UI 2.39+
- **状态管理**: Pinia 2.2+ (支持持久化)
- **路由**: Vue Router 4.4+ (动态路由)
- **样式**: UnoCSS + SCSS
- **图表**: ECharts 5.5+ + Vue-ECharts
- **地图**: 高德地图 AMap
- **HTTP**: Axios 1.7+
- **工具库**: Lodash-ES、Day.js
- **开发工具**: ESLint、Vite DevTools

### 后端技术栈
- **框架**: Spring Boot 3.2.5
- **JDK版本**: Java 21
- **数据库**: MySQL 8.0+ / H2 (开发环境)
- **ORM**: MyBatis Plus 3.5.6
- **权限认证**: SaToken 1.37.0 + JWT
- **API文档**: Knife4j 4.5.0
- **对象映射**: MapStruct 1.5.5
- **工具库**: Hutool 5.8.27
- **代码生成**: Lombok 1.18.30
- **数据处理**: Apache Commons CSV、Weka、EasyExcel

## 项目结构规范

### 前端目录结构
```
coastal-biohazard-front/
├── src/
│   ├── api/                 # API接口定义
│   ├── assets/             # 静态资源
│   │   ├── icons/          # 图标资源
│   │   └── images/         # 图片资源
│   ├── components/         # 通用组件
│   │   ├── common/         # 基础通用组件
│   │   └── me/             # 业务组件
│   ├── composables/        # 组合式函数
│   ├── layouts/            # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── styles/             # 全局样式
│   ├── utils/              # 工具函数
│   └── views/              # 页面组件
│       ├── ims/            # 调查管理系统模块
│       └── pms/            # 权限管理系统模块
```

### 后端目录结构
```
coastal-biohazard-serve-main/
├── src/main/java/cn/dhbin/isme/
│   ├── common/             # 通用模块
│   │   ├── auth/           # 认证授权
│   │   ├── exception/      # 异常处理
│   │   ├── mapstruct/      # 对象映射配置
│   │   ├── mybatis/        # 数据库配置
│   │   └── response/       # 响应封装
│   ├── ims/                # 调查管理系统模块
│   │   ├── controller/     # 控制层
│   │   ├── domain/         # 领域对象
│   │   │   ├── dto/        # 数据传输对象
│   │   │   ├── entity/     # 实体对象
│   │   │   └── request/    # 请求对象
│   │   ├── mapper/         # 数据访问层
│   │   ├── service/        # 业务逻辑层
│   │   └── util/           # 工具类
│   └── pms/                # 权限管理系统模块
```

## 编码规范

### 前端编码规范

#### 1. 文件命名规范
- **组件文件**: 使用 PascalCase，如 `CommonPage.vue`
- **工具文件**: 使用 camelCase，如 `useAuth.js`
- **页面文件**: 使用 kebab-case，如 `time-space-analysis.vue`
- **API文件**: 使用 camelCase，如 `api.js`

#### 2. Vue组件规范
```vue
<template>
  <!-- 模板内容，使用2空格缩进 -->
</template>

<script setup>
// 1. 导入依赖（外部库在前，内部模块在后）
import { ref, reactive, onMounted } from 'vue'
import { NButton, NCard } from 'naive-ui'
import { useRouter } from 'vue-router'
import api from './api'

// 2. defineProps/defineEmits/defineOptions
defineOptions({ name: 'ComponentName' })
const props = defineProps({...})
const emit = defineEmits([...])

// 3. 响应式数据
const loading = ref(false)
const formData = reactive({...})

// 4. 计算属性
const computedValue = computed(() => ...)

// 5. 方法定义
function handleSubmit() {...}

// 6. 生命周期
onMounted(() => {...})
</script>

<style scoped>
/* 使用scoped样式，避免全局污染 */
</style>
```

#### 3. API接口规范
```javascript
// api.js
import { request } from '@/utils'

export default {
  // CRUD操作使用统一命名
  create: data => request.post('/endpoint', data),
  read: params => request.get('/endpoint', { params }),
  update: data => request.patch('/endpoint', data),
  delete: id => request.delete(`/endpoint/${id}`),
  
  // 导入导出
  export: params => request.get('/endpoint/export', {
    params,
    responseType: 'arraybuffer'
  }),
  import: data => request.post('/endpoint/import', data, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
}
```

#### 4. 组件设计原则
- **单一职责**: 每个组件只负责一个功能
- **可复用性**: 通过props和events实现组件通信
- **可维护性**: 保持组件简洁，逻辑清晰
- **命名规范**: 使用语义化的命名

### 后端编码规范

#### 1. 包命名规范
- 控制层: `*.controller`
- 服务层: `*.service`
- 数据访问层: `*.mapper`
- 实体类: `*.domain.entity`
- DTO类: `*.domain.dto`
- 请求类: `*.domain.request`

#### 2. 类命名规范
- **Controller**: 以`Controller`结尾，如 `UserController`
- **Service**: 以`Service`结尾，如 `UserService`
- **Mapper**: 以`Mapper`结尾，如 `UserMapper`
- **Entity**: 业务名称，如 `User`
- **DTO**: 以`Dto`结尾，如 `UserDto`
- **Request**: 以`Request`结尾，如 `CreateUserRequest`

#### 3. 方法命名规范
- **查询**: `get*`、`find*`、`query*`
- **新增**: `create*`、`add*`、`save*`
- **修改**: `update*`、`modify*`
- **删除**: `delete*`、`remove*`
- **判断**: `is*`、`has*`、`exists*`

#### 4. 注解使用规范
```java
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    @GetMapping
    public R<Page<UserDto>> getUsers(@Valid UserQueryRequest request) {
        // 实现逻辑
    }
    
    @PostMapping
    public R<Void> createUser(@Valid @RequestBody CreateUserRequest request) {
        // 实现逻辑
    }
}
```

## 数据库设计规范

### 1. 表命名规范
- 使用小写字母和下划线
- 表名具有业务含义
- 关联表使用`_`连接，如 `user_role`

### 2. 字段命名规范
- 使用小写字母和下划线
- 主键统一使用`id`
- 创建时间: `create_time`
- 更新时间: `update_time`
- 逻辑删除: `deleted`

### 3. 索引规范
- 主键索引: `pk_table_name`
- 唯一索引: `uk_table_name_column`
- 普通索引: `idx_table_name_column`

## 模块开发规范

### 1. 新增功能模块流程

#### 前端模块开发
1. 在 `views` 目录下创建模块文件夹
2. 创建页面组件和API文件
3. 在路由中配置页面路径
4. 在权限系统中配置菜单权限

#### 后端模块开发
1. 创建领域对象 (Entity、DTO、Request)
2. 创建Mapper接口和XML映射文件
3. 实现Service业务逻辑
4. 创建Controller提供API接口
5. 编写单元测试

### 2. 代码提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具变动

### 3. 分支管理规范
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于功能集成
- `feature/*`: 功能分支，开发新功能
- `hotfix/*`: 热修复分支，修复紧急问题

## 部署和运维规范

### 1. 环境配置
- **开发环境**: H2内存数据库，热重载
- **测试环境**: MySQL数据库，模拟生产环境
- **生产环境**: MySQL数据库，性能优化配置

### 2. 日志规范
- 使用SLF4J + Logback进行日志记录
- 区分不同级别：ERROR、WARN、INFO、DEBUG
- 敏感信息不记录日志
- 生产环境不输出DEBUG日志

### 3. 安全规范
- 使用HTTPS协议
- 实施CORS跨域策略
- 敏感接口需要权限验证
- 定期更新依赖版本

## 性能优化规范

### 1. 前端性能
- 使用懒加载和代码分割
- 图片资源压缩和格式优化
- 使用CDN加速静态资源
- 合理使用缓存策略

### 2. 后端性能
- 数据库查询优化，避免N+1问题
- 使用连接池管理数据库连接
- 合理使用缓存 (Redis)
- API接口分页处理

## 测试规范

### 1. 前端测试
- 单元测试: Vitest
- 组件测试: Vue Testing Library
- E2E测试: Playwright (可选)

### 2. 后端测试
- 单元测试: JUnit 5 + Mockito
- 集成测试: Spring Boot Test
- API测试: 使用Postman或自动化测试

## 代码质量保障

### 1. 代码检查
- 前端: ESLint + Prettier
- 后端: Checkstyle (Google规范)

### 2. 代码审查
- 新功能必须经过代码审查
- 关注代码质量、性能和安全性
- 确保符合项目规范

## 文档要求

### 1. API文档
- 使用Knife4j自动生成API文档
- 接口需要详细的参数说明和示例

### 2. 代码注释
- 类和方法需要JavaDoc注释
- 复杂逻辑需要行内注释
- Vue组件需要说明注释

## 注意事项

1. **兼容性**: 确保前后端版本兼容
2. **扩展性**: 预留接口扩展能力
3. **可维护性**: 代码结构清晰，便于维护
4. **安全性**: 严格权限控制，防止数据泄露
5. **用户体验**: 友好的错误提示和加载状态

## 工具推荐

### 开发工具
- **IDE**: IntelliJ IDEA / VS Code
- **API测试**: Postman / Apifox
- **数据库工具**: Navicat / DBeaver
- **版本控制**: Git + GitLab/GitHub

### 监控工具
- **性能监控**: Spring Boot Actuator
- **日志监控**: ELK Stack (可选)
- **系统监控**: Prometheus + Grafana (可选)

此规范为海岸带生物灾害监测系统的开发指导，所有开发人员应严格遵循，确保项目质量和开发效率。 
package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.ims.domain.dto.WaterQualityPredictionDto;
import cn.dhbin.isme.ims.domain.entity.WaterQualityPrediction;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 水质预测服务接口
 */
public interface WaterQualityPredictionService extends IService<WaterQualityPrediction> {
    
    /**
     * 执行水质预测
     * @param longitude 经度
     * @param latitude 纬度
     * @param scaleId 调查中心ID
     * @param taskId 任务ID
     * @param timesId 调查次数ID
     * @return 预测结果
     */
    WaterQualityPredictionDto predictWaterQuality(BigDecimal longitude, BigDecimal latitude, 
                                                  Integer scaleId, Integer taskId, Integer timesId);
    
    /**
     * 获取水质预测分析的SSE流
     * @param longitude 经度
     * @param latitude 纬度
     * @param scaleId 调查中心ID
     * @param taskId 任务ID
     * @param timesId 调查次数ID
     * @return SSE发射器
     */
    SseEmitter getAnalysisStream(BigDecimal longitude, BigDecimal latitude,
                                Integer scaleId, Integer taskId, Integer timesId);

    /**
     * 根据站点ID获取水质预测数据
     * @param stationId 站点ID
     * @param longitude 经度（可选）
     * @param latitude 纬度（可选）
     * @return 水质预测数据列表
     */
    List<WaterQualityPredictionDto> getByStationId(Integer stationId, BigDecimal longitude, BigDecimal latitude);
} 
package cn.dhbin.isme.ims.domain.dto;


import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 微观繁殖体详情表(AbundanceLayerSpeciesData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:09:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AbundanceLayerSpeciesDataDto{
    private Integer id;

    /**
     * 站点id
     **/
    private Integer distributeId;

    private StationPointDistribute stationPointDistribute; // 站点详情信息

    private List<SampleType> sampleTypeList; // 样品种类集合

    /**
     * 样品类型
     **/
    private Integer sampleType;

    /**
     * 丰富度 ind./50g
     **/
    private Integer abundance;

    private Date createTime;

    private Date updateTime;
    
public Serializable pkVal() {
          return null;
      }
}



package cn.dhbin.isme.ims.domain.entity;

import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("sediment")
public class Sediment implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 沉积物图片
     **/
    private String sedimentUrl;

    /**
     * 沉积物培养图片
     */
    private String cultureUrl;

    public Serializable pkVal() {
        return null;
    }
}

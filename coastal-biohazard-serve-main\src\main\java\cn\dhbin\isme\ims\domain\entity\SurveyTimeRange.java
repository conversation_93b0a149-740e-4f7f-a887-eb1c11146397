package cn.dhbin.isme.ims.domain.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 调查时间范围表(SurveyTimeRange)表实体类
 *
 * <AUTHOR>
 * @since 2024-11-05 08:18:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SurveyTimeRange implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 站位id
     **/
    private Integer distributeId;

    @TableField(select = false)
    private String distributeName;
    
    /**
     * 调查开始时间
     **/
    private Date beforeInvestigate;
    
    /**
     * 调查结束时间
     **/
    private Date afterInvestigate;
    
    /**
     * 描述
     **/
    private String description;

    @TableField(exist = false)
    private List<Long> range;
    
public Serializable pkVal() {
          return null;
      }
}



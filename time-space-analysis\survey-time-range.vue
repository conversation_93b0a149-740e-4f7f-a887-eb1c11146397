<!--------------------------------
-现场调查时空分析模块
-作业时间管理
-createBy：isla
--------------------------------->

<template>
  <CommonPage back>
    <template #title-suffix>
      <NTag class="ml-12" type="warning">
        站点 {{ route.query.distributeName }}
      </NTag>
    </template>
    <template #action>
      <NButton type="primary" @click="createNewSurvey">
        <i class="i-material-symbols:add mr-4 text-18" />
        新增调查次数
      </NButton>
    </template>

    <div class="flex items-center gap-4">
      <!-- 筛选输入框 -->
      <span>调查次数：</span>
      <NInput
        v-model:value="searchForm.times"
        type="number"
        min="1"
        placeholder="输入调查次数"
        style="width: 150px"
      />

      <!-- 搜索按钮 -->
      <NButton type="primary" @click="handleSearch">
        搜索
      </NButton>

      <!-- 重置按钮 -->
      <NButton @click="handleReset">
        重置
      </NButton>
    </div>

    <NDataTable
      ref="$table"
      :columns="columns"
      :data="surveyData"
      :row-key="rowKey"
      :pagination="pagination"
      default-expand-all
    />

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef" :rules="rules" label-placement="left" label-align="left" :label-width="80"
        :model="modalForm" :disabled="modalAction === 'view'"
      >
        <n-form-item
          label="作业人员" path="staffId" :rule="{
            required: true,
            message: '请选择作业人员',
          }"
        >
          <n-select
            v-model:value="modalForm.staffId" label-field="name" value-field="id"
            clearable filterable :options="manageList" placeholder="请选择作业人员"
          />
        </n-form-item>
        <n-form-item
          label="数据来源" path="source" :rule="{
            required: true,
            message: '请输入数据来源',
          }"
        >
          <n-input v-model:value="modalForm.source" placeholder="请输入数据来源" />
        </n-form-item>
        <n-form-item label="作业时间" rule-path="range" path="range">
          <n-date-picker v-model:value="modalForm.range" type="datetimerange" clearable />
        </n-form-item>
        <n-form-item label="描述" path="description">
          <n-input v-model:value="modalForm.description" placeholder="请输入描述" />
        </n-form-item>
        <n-form-item
          label="佐证材料"
          path="evidenceFiles"
          :rule="{ required: false }"
        >
          <n-upload
            :custom-request="handleUpload"
            :default-file-list="defaultFileList"
            list-type="image-card"
            :max="3"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            @preview="handlePreview"
            @remove="handleRemove"
          >
            <NButton>点击上传</NButton>
          </n-upload>
          <n-modal v-model:show="showModal" preset="card" style="width: 600px">
            <img :src="previewImageUrl" style="width: 100%">
          </n-modal>
        </n-form-item>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeModal } from '@/components'
import { CommonPage } from '@/components/index.js'
import { useCrud } from '@/composables'
import { formatDateTime } from '@/utils'
import { NButton, NDataTable, NTag } from 'naive-ui'
import { h, onMounted, ref } from 'vue'
import api from './api'

const route = useRoute()
const router = useRouter()
const now = Date.now()
const baseUrl = ref(import.meta.env.VITE_AXIOS_BASE_URL)
const $table = ref(null)

// 搜索表单
const searchForm = ref({
  times: route.query.times || '',
})

const surveyData = ref([])
const pagination = ref({
  page: 1,
  pageSize: 10,
  itemCount: 0,
})

const rules = {
  range: { required: true, message: '请选择日期范围', type: 'array', trigger: ['change'] },
}

// 获取调查数据
async function fetchSurveyData() {
  try {
    const params = {
      distributeId: route.query.distributeId,
      times: searchForm.value.times ? Number(searchForm.value.times) : undefined,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }

    const response = await api.readSurveyTimeRange(params)
    surveyData.value = transformSurveyData(response.data.pageData)
    pagination.value.itemCount = response.data.total
  }
  catch (error) {
    console.error('获取调查数据失败:', error)
  }
}

// 搜索按钮点击事件
function handleSearch() {
  console.log(searchForm.value.times)
  // pagination.value.page = 1
  // const query = {
  //   ...route.query,
  //   times: searchForm.value.times || undefined
  // }
  // router.replace({ query })
  fetchSurveyData()
}

// 重置按钮点击事件
function handleReset() {
  searchForm.value.times = ''
  pagination.value.page = 1
  // router.replace({ query: { ...route.query, times: undefined } })
  fetchSurveyData()
}

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: ' 作业时间',
  initForm: {
    range: [now - 30 * 24 * 60 * 60 * 1000, now],
    distributeId: Number(route.query.distributeId),
    evidenceFiles: [],
  },
  doCreate: api.createSurveyTimeRange,
  doDelete: api.deleteSurveyTimeRange,
  doUpdate: api.updateSurveyTimeRange,
  refresh: fetchSurveyData,
})

const defaultFileList = computed(() => {
  // 1. 如果 evidenceFiles 不存在，返回空数组
  if (!modalForm.value.evidenceFiles)
    return []

  // 2. 如果是字符串，尝试解析为数组
  const files = Array.isArray(modalForm.value.evidenceFiles)
    ? modalForm.value.evidenceFiles
    : JSON.parse(modalForm.value.evidenceFiles)

  // console.log(files,"22")
  // 3. 映射为文件列表
  return files.map(filename => ({
    name: filename,
    url: `${baseUrl.value}/upload/getImage?imageName=${filename}`,
    status: 'finished',
  }))
})

// 上传前校验逻辑
function beforeUpload(file) {
  const isValidType = ['image/jpeg', 'image/png', 'video/mp4'].includes(file.type)
  if (!isValidType) {
    $message.error('仅支持上传图片（jpg/png）或视频（mp4）')
    return false
  }
  if (modalForm.value.evidenceFiles.length >= 3) {
    $message.error('最多上传3个文件')
    return false
  }
  return true
}

// 自定义上传逻辑
async function handleUpload({ file, onFinish, onError }) {
  // 确保 evidenceFiles 是数组
  if (!Array.isArray(modalForm.value.evidenceFiles)) {
    try {
      // 如果是字符串，尝试解析为数组
      if (typeof modalForm.value.evidenceFiles === 'string') {
        modalForm.value.evidenceFiles = JSON.parse(modalForm.value.evidenceFiles)
      } else {
        // 如果是 null/undefined，初始化为空数组
        modalForm.value.evidenceFiles = []
      }
    } catch (error) {
      console.error('evidenceFiles 解析失败，使用空数组:', error)
      modalForm.value.evidenceFiles = []
    }
  }

  // 调用上传接口
  const res = await api.uploadImg(file)
  if (res.code === 0) {
    // 构造带域名的预览地址
    file.url = `${baseUrl.value}/upload/getImage?imageName=${res.data}`

    // 添加文件名到表单字段
    modalForm.value.evidenceFiles.push(res.data)

    $message.success('上传成功')
    onFinish()
  } else {
    $message.error('上传失败')
    onError()
  }
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  console.log(res)
  if (res.code === 0) {
    modalForm.value.evidenceFiles.push(res.data) // 存储文件名
    $message.success('上传成功')
  }
  else {
    $message.error('上传失败')
  }
}

const previewImageUrl = ref('') // 存储预览图片地址
const showModal = ref(false) // 控制模态框显示

function handlePreview(file) {
  previewImageUrl.value = file.url // 设置预览地址
  showModal.value = true // 打开模态框
}

function handleRemove(file) {
  // 1. 确保 evidenceFiles 是数组
  if (!Array.isArray(modalForm.value.evidenceFiles)) {
    modalForm.value.evidenceFiles = []
    return
  }

  // 2. 从 file 中提取文件名（兼容 file.name 或 file.fileList）
  const filename = file.name || file.fileList?.[0]?.name || ''

  // 3. 查找并删除匹配项
  const index = modalForm.value.evidenceFiles.findIndex(
    name => name === filename,
  )

  if (index > -1) {
    modalForm.value.evidenceFiles.splice(index, 1)
    $message.success('删除成功')
  } else {
    console.warn('未找到匹配的文件:', filename)
  }
}

const manageList = ref([])

function getManageList() {
  api.getManageList().then(({ data }) => {
    manageList.value = data
  })
}

const rowKey = row => row.times

// 转换数据结构：按调查次数分组
function transformSurveyData(rawData) {
  const groupedData = {}

  rawData.forEach((item) => {
    const times = item.times || 1

    if (!groupedData[times]) {
      groupedData[times] = {
        times,
        activities: [],
      }
    }

    groupedData[times].activities.push(item)
  })

  return Object.values(groupedData)
}


onMounted(() => {
  if (route.query.times) {
    searchForm.value.times = route.query.times
  }
  fetchSurveyData()
  getManageList()
})

const columns = [
  {
    type: 'expand',
    fixed: 'left',
    renderExpand: (rowData) => {
      console.log(rowData.activities)
      // 检查 activities 是否存在
      if (!rowData.activities || !Array.isArray(rowData.activities)) {
        return h('div', { style: { color: 'red' } }, '无活动数据')
      }

      return h(
        NDataTable,
        {
          columns: activityColumns,
          data: rowData.activities,
          pagination: false,
        },
      )
    },
  },
  {
    title: '调查次数',
    key: 'times',
    fixed: 'left',
    render(row) {
      return h('span', `第${row.times}次调查`)
    },
  },
  {
    title: '活动数量',
    key: 'activityCount',
    render(row) {
      return h('span', row.activities.length)
    },
  },
  // {
  //   title: '创建时间',
  //   key: 'createTime',
  //   render(row) {
  //     const createTime = row.activities[0]?.createTime
  //     return h('span', createTime ? formatDateTime(createTime) : '-')
  //   }
  // }
]

const activityColumns = [
  {
    title: '序号',
    key: 'index',
    width: 70,
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    title: '关联活动',
    key: 'activeIndex',
    width: 180,
    render(row) {
      const activityMap = {
        1: '水文气象采集活动',
        2: '化学样本采集活动',
        3: '成体生物量采集活动',
        4: '微观繁殖体采集活动',
      }

      // 根据 activeIndex 映射到不同的 tag 类型和颜色
      const tagTypeMap = {
        1: 'primary', // 蓝色
        2: 'success', // 绿色
        3: 'info', // 深蓝色
        4: 'warning', // 黄色
      }

      const tagType = tagTypeMap[row.activeIndex] || 'error' // 默认红色

      return h(
        NTag,
        {
          type: tagType,
          style: {
            borderRadius: '4px',
            padding: '4px 8px',
          },
        },
        activityMap[row.activeIndex] || '未知活动',
      )
    },
  },
  {
    title: '作业人员',
    key: 'staffName',
    render(row) {
      return h(NTag, {
        type: row.staffName ? 'primary' : 'error', // 条件判断 type
        style: { marginRight: '8px' }, // 可选样式
      }, row.staffName || '⚠ 未指派')
    },
  },
  {
    title: '数据来源',
    key: 'source',
    render(row) {
      return h(NTag, {
        type: row.source ? 'primary' : 'error', // 条件判断 type
        style: { marginRight: '8px' }, // 可选样式
      }, row.source || '⚠ 未录入')
    },
  },
  {
    title: '作业开始时间',
    key: 'beforeInvestigate',
    render(row) {
      return h('span', row.beforeInvestigate ? formatDateTime(row.beforeInvestigate) : '-')
    },
  },
  {
    title: '作业结束时间',
    key: 'afterInvestigate',
    render(row) {
      return h('span', row.afterInvestigate ? formatDateTime(row.afterInvestigate) : '-')
    },
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: { tooltip: true },
  },
  {
    width: 180,
    title: '操作',
    key: 'actions',
    align: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleOpenUpdate(row),
          },
          {
            default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function handleOpenUpdate(row) {
  // 设置时间范围
  row.range = row.beforeInvestigate && row.afterInvestigate
    ? [new Date(row.beforeInvestigate).getTime(), new Date(row.afterInvestigate).getTime()]
    : [now - 30 * 24 * 60 * 60 * 1000, now]

  handleOpen({
    action: 'edit',
    title: '更新记录',
    row,
    onOk: updateM,
  })
}

async function updateM() {
  await modalFormRef.value?.validate()

  // 处理 evidenceFiles 字段：确保是数组后再序列化
  let evidenceFiles = modalForm.value.evidenceFiles

  // 如果是字符串，尝试解析为数组
  if (typeof evidenceFiles === 'string') {
    try {
      evidenceFiles = JSON.parse(evidenceFiles)
    } catch (error) {
      console.error('evidenceFiles 解析失败，使用空数组:', error)
      evidenceFiles = []
    }
  }

  // 确保是数组后再序列化
  evidenceFiles = JSON.stringify(Array.isArray(evidenceFiles) ? evidenceFiles : [])

  await api.updateSurveyTimeRange({
    id: modalForm.value.id,
    range: modalForm.value.range,
    description: modalForm.value.description,
    staffId: modalForm.value.staffId,
    source: modalForm.value.source,
    evidenceFiles, // ✅ 已处理过的 JSON 字符串
  })

  $message.success('操作成功')
  fetchSurveyData()
}

// 创建新调查次数
function createNewSurvey() {
  $dialog.confirm({
    title: '创建新调查',
    content: '确定要创建新调查次数吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await api.createNewSurvey(route.query.distributeId)
        $message.success('新调查次数已创建')
        fetchSurveyData()
      }
      catch (error) {
        $message.error(`创建失败: ${error.message}`)
      }
    },
  })
}
</script>

<style scoped>
/* 主表格基础样式 */
.n-data-table {
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
}

/* 主表格行背景色（交替） */
:deep(.n-data-table tr:nth-child(even)) {
  background-color: #f9f9f9;
}

/* 展开区域样式 */
:deep(.n-data-table__expanded-cell) {
  padding: 12px;
  background-color: #fafafa; /* 与主表格区分 */
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
}

/* 子表格样式 */
:deep(.n-data-table.n-data-table--child) {
  background-color: #f0f4f7; /* 淡蓝色背景 */
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 子表格标题行字体颜色 */
:deep(.n-data-table--child .n-data-table-header) {
  background-color: #e6f0fb;
  color: #1a73e8;
  font-weight: 500;
}

/* 子表格行背景色（交替） */
:deep(.n-data-table--child tr:nth-child(even)) {
  background-color: #eef7ff;
}

/* 子表格单元格内边距 */
:deep(.n-data-table--child .n-data-table-td) {
  padding: 8px 12px;
  font-size: 13px;
}

/* 操作按钮样式 */
:deep(.n-data-table .n-button) {
  font-size: 12px;
  height: 24px;
  padding: 0 8px;
}

/* 过渡动画 */
:deep(.n-data-table__expand-toggle) {
  transition: all 0.3s ease;
}
</style>

package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.entity.DataPreparer;
import cn.dhbin.isme.ims.service.impl.AlgaeDetectionService;
import cn.dhbin.isme.ims.util.DateGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import weka.classifiers.trees.RandomForest;
import weka.core.Instance;
import weka.core.Instances;
import weka.core.SerializationHelper;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/algae")
public class AlgaeDetectionController {
    @Autowired
    private AlgaeDetectionService algaeDetectionService;

    /**
     * 数据清洗及模型载入
     * @return
     */
    @PostMapping("/clean-and-train")
    public R<?> cleanDataAndTrainModel() throws Exception {
        algaeDetectionService.cleanDataAndTrainModel();
        return R.ok("Data cleaning and model training completed.");
    }

    @GetMapping("/predict-week")
    public R<?> predictWeek() throws Exception {
        // 加载分类模型
        RandomForest classificationModel = (RandomForest) SerializationHelper.read("trained_random_forest_classification.model");

        // 加载回归模型
        RandomForest regressionModel = (RandomForest) SerializationHelper.read("trained_random_forest_regression.model");

        // 生成未来一个星期的日期
        LocalDate[] dates = DateGenerator.generateFutureDates(7);

        // 创建 Instances
        Instances data = DataPreparer.createInstances(dates);

        // 存储预测结果
        List<Map<String, String>> predictions = new ArrayList<>();

        // 进行预测
        for (int i = 0; i < data.numInstances(); i++) {
            Instance instance = data.instance(i);

            System.out.println("\n实例:  " + instance);

            // 设置类索引为 RiskLevel 以便进行分类预测
            data.setClassIndex(data.attribute("RiskLevel").index());

            // 输出 Instances 信息
            System.out.println("分类数据集信息:");
            System.out.println("数据集中属性的数量: " + data.numAttributes());
            System.out.println("类索引设置为: " + data.classIndex());
            System.out.println("类属性名称: " + data.classAttribute().name());

            // 执行分类预测
            double classificationPrediction = classificationModel.classifyInstance(instance);
            String predictedRiskLevel = data.classAttribute().value((int) classificationPrediction);

            // 重新设置类索引为 AlgaeDensity 以便进行回归预测
            data.setClassIndex(data.attribute("AlgaeDensity").index());

            // 输出 Instances 信息
            System.out.println("回归数据集信息:");
            System.out.println("数据集中属性的数量: " + data.numAttributes());
            System.out.println("类索引设置为: " + data.classIndex());
            System.out.println("类属性名称: " + data.classAttribute().name());

            // 执行回归预测
            double regressionPrediction = Double.NaN;
            try {
                regressionPrediction = regressionModel.classifyInstance(instance);
                System.out.println("回归预测结果 " + (i + 1) + ": " + regressionPrediction);
            } catch (Exception e) {
                // 如果发生异常，打印异常信息
                System.err.println("回归预测时出错: " + e.getMessage());
                e.printStackTrace();
            }

            // 格式化预测的藻类密度值，保留四位小数
            String formattedRegressionPrediction = Double.isNaN(regressionPrediction) ? "NaN" : String.format("%.4f", regressionPrediction);

            // 构造结果对象
            Map<String, String> result = Map.of(
                    "date", DateGenerator.format(dates[i]),
                    "predictedRiskLevel", predictedRiskLevel,
                    "predictedAlgaeDensity", formattedRegressionPrediction
            );

            // 添加到结果列表中
            predictions.add(result);
        }

        return R.ok(predictions);
    }
}

package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.entity.User;
import cn.dhbin.isme.pms.domain.request.RegisterUserRequest;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:06:05+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class RegisterUserRequestToUserImpl implements RegisterUserRequestToUser {

    @Override
    public User to(RegisterUserRequest arg0) {
        if ( arg0 == null ) {
            return null;
        }

        User user = new User();

        user.setEnable( arg0.getEnable() );
        user.setPassword( arg0.getPassword() );
        user.setUsername( arg0.getUsername() );

        return user;
    }
}

/*
 * @Author: Cursor
 * @Date: 2025-04-15 10:00:00
 * @LastEditors: Cursor
 * @LastEditTime: 2025-04-15 10:00:00
 * @Description: 调查时间范围表Excel导入导出DTO
 */
package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 调查时间范围表 Excel 导入导出DTO
 */
@Data
public class SurveyTimeRangeExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点ID")
    @ColumnWidth(15)
    private Integer distributeId;

    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;

    @ExcelProperty("作业开始时间")
    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private String beforeInvestigate;

    @ExcelProperty("作业结束时间")
    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private String afterInvestigate;

    @ExcelProperty("时间范围描述")
    @ColumnWidth(30)
    private String description;
}

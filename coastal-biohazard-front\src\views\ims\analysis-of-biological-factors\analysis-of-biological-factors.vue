<template>
  <CommonPage>
    <template #action>
      <div style="display: flex; gap: 24px">
        <!-- 导出/导入功能 -->
        <NButton type="warning" @click="handleExport">
          <i class="i-material-symbols:download mr-4 text-18" />
          导出Excel
        </NButton>

        <!-- 新增导入按钮 -->
        <NUpload
          :show-file-list="false"
          :custom-request="handleImport"
          accept=".xlsx,.xls"
          :disabled="importLoading"
        >
          <NButton
            type="success"
            :loading="importLoading"
            :disabled="importLoading"
          >
            <i class="i-material-symbols:upload mr-4 text-18" />
            {{ importLoading ? "正在导入..." : "导入Excel" }}
          </NButton>
        </NUpload>

        <NButton type="primary" @click="add()">
          <i class="i-material-symbols:add mr-4 text-18" />
          创建新记录
        </NButton>
      </div>
    </template>

    <MeCrud
      ref="$table"
      v-model:query-items="queryItems"
      :scroll-x="1200"
      :columns="columns"
      :get-data="api.read"
    >
      <MeQueryItem label="站点" :label-width="70">
        <n-select
          v-model:value="queryItems.distributeId"
          label-field="name"
          value-field="id"
          clearable
          filterable
          :options="stationOption"
          placeholder="请选择站点"
        />
      </MeQueryItem>
    </MeCrud>

    <MeModal ref="modalRef" width="520px">
      <n-form
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :rules="formRules"
        :label-width="120"
        :model="modalForm"
        :disabled="modalAction === 'view'"
      >
        <n-form-item
          label="站点"
          path="distributeId"
          :rule="{
            required: true,
            message: '请选择站点',
            type: 'number',
            trigger: ['change'],
          }"
        >
          <n-select
            v-model:value="modalForm.distributeId"
            label-field="name"
            value-field="id"
            clearable
            filterable
            :options="stationOption"
            placeholder="请选择站点"
            @change="changeSelect"
          />
        </n-form-item>
        <n-form-item
          label="样品种类"
          path="sampleTypeList"
          :rule="{
            required: true,
            message: '请选择至少一个样品种类',
            type: 'array',
            trigger: ['input', 'blur'],
          }"
        >
          <n-select
            v-model:value="displaySampleTypeList"
            :options="sampleTypeOption"
            label-field="name"
            value-field="id"
            clearable
            filterable
            multiple
            @change="changeSampleType"
          />
        </n-form-item>

        <div v-for="(item, index) in modalForm.sampleTypeList" :key="item.id">
          <n-form-item
            :label="`${item.name}生物量`"
            :path="`sampleTypeList.${index}.number`"
            :rule="getValidationRule(item)"
          >
            <n-input-number
              v-model:value="item.number"
              style="width: 100%"
              :placeholder="`请输入${item.name}生物量`"
            >
              <template #suffix>
                <!-- 根据样品种类设置不同的单位 -->
                <span v-if="item.name === '浒苔'">ind./L</span>
                <span v-else-if="item.name === '微观繁殖体'">株/L</span>
                <span v-else-if="item.name === '微生物'">个/mL</span>
                <span v-else-if="item.name === '病毒'">个/mL</span>
                <span v-else>ind./50g</span>
              </template>
            </n-input-number>
          </n-form-item>
        </div>
      </n-form>
    </MeModal>
  </CommonPage>
</template>

<script setup>
import { MeCrud, MeModal, MeQueryItem } from '@/components'
import { useCrud } from '@/composables'
import { createDiscreteApi, NButton, NTag, NUpload } from 'naive-ui'
import api from './api'

const router = useRouter()
// defineOptions({ name: 'UserMgt' })

const $table = ref(null)
/** QueryBar筛选参数（可选） */
const queryItems = ref({})

const stationOption = ref([])

const coordination = ref({})

const sampleTypeOption = ref([])

const sampleType = ref(2) // 复用改该字段即可

// let sampleTypeList = ref([])

const displaySampleType = computed(() => {
  switch (sampleType.value) {
    case 1:
      return '底层水样微观繁殖体'
    case 2:
      return '表层水样微观繁殖体'
    default:
      return '沉积物'
  }
})

const displaySampleTypeList = computed({
  get: () => modalForm.value.sampleTypeList?.map(item => item.id) || [],
  set: selectedIds => changeSampleType(selectedIds),
})

// 获取验证规则
function getValidationRule(item) {
  return {
    required: true,
    message: `请输入${item.name}生物量`,
    type: 'number',
    trigger: ['input', 'blur'],
  }
}

async function getSampleTypeList() {
  const { data } = await api.getListSampleTypes()
  sampleTypeOption.value = data
}

async function getStationList() {
  const { data } = await api.getListStationPoints(0)
  // console.log(data);
  stationOption.value = data
}

function changeSampleType(selectedIds) {
  if (!selectedIds || !selectedIds.length) {
    modalForm.value.sampleTypeList = []
    return
  }

  const currentList = modalForm.value.sampleTypeList || []
  const existingItemsMap = new Map(currentList.map(item => [item.id, item]))

  modalForm.value.sampleTypeList = selectedIds.map((id) => {
    const existingItem = existingItemsMap.get(id)
    if (existingItem)
      return existingItem // 保留已填写的 number

    const newItem = sampleTypeOption.value.find(option => option.id === id)
    return { ...newItem, number: null } // 新增的项，初始化 number
  })
}

async function changeSelect(row) {
  if (row != null) {
    stationOption.value.filter((item) => {
      if (item.id == row) {
        coordination.value.longitude = item.longitude
        coordination.value.latitude = item.latitude
      }
    })
  }
  else {
    coordination.value = {}
  }
}

onMounted(() => {
  // queryItems.value.sampleType = sampleType.value
  $table.value?.handleSearch()
  getStationList()
  getSampleTypeList()
  modalForm.value.sampleType = sampleType.value
  // console.log(modalForm.value);
})

const {
  modalRef,
  modalFormRef,
  modalForm,
  modalAction,
  handleAdd,
  handleDelete,
  handleOpen,
  handleSave,
  handleEdit,
} = useCrud({
  name: '生物量',
  initForm: { enable: true },
  doCreate: api.create,
  doDelete: api.delete,
  doUpdate: api.update,
  refresh: (_, keepCurrentPage) => $table.value?.handleSearch(keepCurrentPage),
})

const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    fixed: 'left',
    render(row, index) {
      return h('span', index + 1)
    },
  },
  {
    width: 100,
    title: '站点',
    ellipsis: { tooltip: true },
    render(row) {
      return h(NTag, { type: 'success' }, { default: () => row.stationPointDistribute.name })
    },
  },
  // {
  //   title: '生物门类及丰度',
  //   // width: 300,
  //   render(row) {
  //     if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList)) {
  //       return h('span', '无数据')
  //     }
  //     console.log(row.sampleTypeList)
  //     const tagsWithGap = row.sampleTypeList.map((type, index) => {
  //       return h(NTag, { key: index, type: 'info' }, { default: () => `${type.name} ${type.number}ind./L` })
  //     })
  //
  //     return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
  //   },
  // },
  {
    title: '生物门类及丰度',
    // width: 300,
    render(row) {
      if (!row.sampleTypeList || !Array.isArray(row.sampleTypeList)) {
        return h('span', '无数据')
      }
      console.log(row.sampleTypeList)
      const tagsWithGap = row.sampleTypeList.map((type, index) => {
        // 根据生物门类名称设置不同的单位
        let unit = ''
        switch (type.name) {
          case '浒苔':
            unit = 'ind./L'
            break
          case '微观繁殖体':
            unit = '株/L'
            break
          case '微生物':
            unit = '个/mL'
            break
          case '病毒':
            unit = '个/mL'
            break
          default:
            unit = '未知单位'
        }
        return h(NTag, { key: index, type: 'info' }, { default: () => `${type.name} ${type.number}${unit}` })
      })

      return h('div', { style: { display: 'flex', flexWrap: 'wrap', gap: '8px' } }, tagsWithGap)
    },
  },
  {
    width: 230,
    title: '操作',
    key: 'actions',
    align: 'right',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            style: 'margin-left: 12px;',
            onClick: () => handleOpenUpdate(row),
          },
          {
            // default: () => '修改',
            icon: () => h('i', { class: 'i-fe:edit text-14' }),
          },
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            style: 'margin-left: 12px;',
            onClick: () => handleDelete(row.id),
          },
          {
            // default: () => '删除',
            icon: () => h('i', { class: 'i-material-symbols:delete-outline text-14' }),
          },
        ),
      ]
    },
  },
]

function add() {
  coordination.value.longitude = null
  coordination.value.latitude = null
  handleOpen({
    action: 'add',
    title: '新增记录',
    // row: { id: row.id, username: row.username, roleIds },
    onOk: async () => {
      // console.log(modalForm.value);
      // console.log(sampleTypeList.value, "种类");
      await modalFormRef.value?.validate()
      await api.create({
        distributeId: modalForm.value.distributeId,
        sampleType: sampleType.value,
        abundance: modalForm.value.abundance,
        sampleTypes: modalForm.value.sampleTypeList,
      })
      $message.success('操作成功')
      $table.value?.handleSearch()
    },
  })
}

function handleOpenUpdate(row) {
  coordination.value.longitude = row.stationPointDistribute.longitude
  coordination.value.latitude = row.stationPointDistribute.latitude
  // row.sampleTypeList=row.sampleTypeList.map(item=>item.id)
  handleOpen({
    action: 'edit',
    title: '更新记录',
    row,
    onOk: updateSample,
  })
}

async function updateSample() {
  await modalFormRef.value?.validate()
  await api.update({
    id: modalForm.value.id,
    distributeId: modalForm.value.distributeId,
    sampleType: sampleType.value,
    abundance: modalForm.value.abundance,
    sampleTypes: modalForm.value.sampleTypeList,
  })
  $message.success('操作成功')
  $table.value?.handleSearch()
}

// 使用全局消息API
const { message } = createDiscreteApi(['message'])

// 导出功能
async function handleExport() {
  try {
    const res = await api.exportSurfaceWaterExcel(queryItems.value)
    if (!res) {
      message.error('导出失败：返回数据为空')
      return
    }
    const blob = new Blob([res], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = '生物要素分析记录.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(link.href)
    message.success('导出成功')
  }
  catch (error) {
    console.error('导出错误:', error)
    message.error(`导出失败：${error.message || '未知错误'}`)
  }
}

// 导入功能
const importLoading = ref(false)
async function handleImport({ file }) {
  if (!file || !file.file) {
    message.error('请选择要导入的文件')
    return
  }

  try {
    importLoading.value = true
    const formData = new FormData()
    formData.append('file', file.file)

    const res = await api.importSurfaceWaterExcel(formData)
    if (res && res.code === 200) {
      message.success('导入成功')
      $table.value?.handleSearch()
    }
    else {
      throw new Error(res?.message || '导入失败')
    }
  }
  catch (error) {
    console.error('导入错误:', error)
    message.error(`导入失败：${error.message || '未知错误'}`)
  }
  finally {
    importLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
/* 定义过渡动画效果 */
.fade-enter-active {
  transition: opacity 0.8s ease;
}

/* 进入前的状态 */
.fade-enter-from {
  opacity: 0;
}

/* 离开后状态 */
.fade-leave-to {
  opacity: 0;
}
</style>

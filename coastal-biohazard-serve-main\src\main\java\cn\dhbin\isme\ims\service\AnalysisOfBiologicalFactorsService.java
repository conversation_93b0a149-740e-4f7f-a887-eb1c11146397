package cn.dhbin.isme.ims.service;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.AnalysisOfBiologicalFactorsDto;
import cn.dhbin.isme.ims.domain.dto.excel.AnalysisOfBiologicalFactorsExcelDto;
import cn.dhbin.isme.ims.domain.entity.AnalysisOfBiologicalFactors;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.request.AnalysisOfBiologicalFactorsRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface AnalysisOfBiologicalFactorsService extends IService<AnalysisOfBiologicalFactors> {
    Page<AnalysisOfBiologicalFactorsDto> queryPage(AnalysisOfBiologicalFactorsRequest request);

    void addAbundance(Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes, String report);

    void updateAbundance(Integer id, Integer distributeId, Integer sampleType, Integer abundance, List<SampleType> sampleTypes,String report);

    List<AnalysisOfBiologicalFactorsDto> queryList(Integer distributeId);

    void removeById(Integer id);
    
    /**
     * 查询列表数据（用于导出Excel）
     * @param request 查询条件
     * @return 丰富度数据列表
     */
    List<AnalysisOfBiologicalFactorsExcelDto> queryListForExport(AnalysisOfBiologicalFactorsRequest request);
    
    /**
     * 导入Excel数据
     * @param dataList Excel数据列表
     */
    void importData(List<AnalysisOfBiologicalFactorsExcelDto> dataList);
}


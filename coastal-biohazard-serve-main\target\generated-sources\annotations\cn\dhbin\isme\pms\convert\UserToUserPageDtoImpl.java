package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.dto.UserPageDto;
import cn.dhbin.isme.pms.domain.entity.User;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:01:57+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class UserToUserPageDtoImpl implements UserToUserPageDto {

    @Override
    public UserPageDto to(User arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserPageDto userPageDto = new UserPageDto();

        userPageDto.setCreateTime( arg0.getCreateTime() );
        userPageDto.setEnable( arg0.getEnable() );
        userPageDto.setId( arg0.getId() );
        userPageDto.setUpdateTime( arg0.getUpdateTime() );
        userPageDto.setUsername( arg0.getUsername() );

        return userPageDto;
    }
}

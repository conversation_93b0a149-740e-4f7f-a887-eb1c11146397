package cn.dhbin.isme.pms.convert;

import cn.dhbin.isme.pms.domain.dto.PermissionDto;
import cn.dhbin.isme.pms.domain.entity.Permission;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-08T13:01:57+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250628-1110, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class PermissionToPermissionDtoImpl implements PermissionToPermissionDto {

    @Override
    public PermissionDto to(Permission arg0) {
        if ( arg0 == null ) {
            return null;
        }

        PermissionDto permissionDto = new PermissionDto();

        permissionDto.setCode( arg0.getCode() );
        permissionDto.setComponent( arg0.getComponent() );
        permissionDto.setDescription( arg0.getDescription() );
        permissionDto.setEnable( arg0.getEnable() );
        permissionDto.setIcon( arg0.getIcon() );
        permissionDto.setId( arg0.getId() );
        permissionDto.setKeepAlive( arg0.getKeepAlive() );
        permissionDto.setLayout( arg0.getLayout() );
        permissionDto.setMethod( arg0.getMethod() );
        permissionDto.setName( arg0.getName() );
        permissionDto.setOrder( arg0.getOrder() );
        permissionDto.setParentId( arg0.getParentId() );
        permissionDto.setPath( arg0.getPath() );
        permissionDto.setRedirect( arg0.getRedirect() );
        permissionDto.setShow( arg0.getShow() );
        permissionDto.setType( arg0.getType() );

        return permissionDto;
    }
}

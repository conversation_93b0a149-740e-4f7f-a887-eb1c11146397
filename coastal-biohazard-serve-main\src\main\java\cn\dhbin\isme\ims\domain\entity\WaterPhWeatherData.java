package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微观藻体水文特征表(WaterPhWeatherData)表实体类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:42:31
 */
@Data
@TableName("water_ph_weather_data")
public class WaterPhWeatherData implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 站点id
     **/
    private Integer distributeId;
    
    /**
     * 采样层次(1底层 2表层)
     **/
    private Integer sampleLayer;
    
    /**
     * 天气现象
     **/
    private String weather;
    
    /**
     * 风向
     **/
    private String windDirection;
    
    /**
     * 盐度 - decimal(5,2)
     **/
    private BigDecimal saltExtent;
    
    /**
     * PH值 - decimal(4,2)
     **/
    private BigDecimal phExtent;
    
    /**
     * 气温,单位℃ - decimal(3,1)
     **/
    private BigDecimal airTemperature;
    
    /**
     * 水温,单位℃ - decimal(4,2)
     **/
    private BigDecimal waterTemperature;
    
    /**
     * 透明度,单位m - decimal(4,2)
     **/
    private BigDecimal transparentExtent;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(exist = false)
    private String distributeName;
    
public Serializable pkVal() {
          return null;
      }
}



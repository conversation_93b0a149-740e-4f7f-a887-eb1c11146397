package cn.dhbin.isme.ims.controller;

import cn.dhbin.isme.common.exception.BizException;
import cn.dhbin.isme.common.response.BizResponseCode;
import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.common.response.R;
import cn.dhbin.isme.ims.domain.dto.excel.WaterPhWeatherDataExcelDto;
import cn.dhbin.isme.ims.domain.dto.WaterPhWeatherDataDto;
import cn.dhbin.isme.ims.domain.dto.excel.AirEnvironmentalDataExcelDto;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.entity.WaterPhWeatherData;
import cn.dhbin.isme.ims.domain.request.WaterPhWeatherDataRequest;
import cn.dhbin.isme.ims.mapper.StationPointDistributeMapper;
import cn.dhbin.isme.ims.service.StationPointDistributeService;
import cn.dhbin.isme.ims.service.WaterPhWeatherDataService;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/water-ph-weather-data")
@RequiredArgsConstructor
public class WaterPhWeatherDataController {
    private final WaterPhWeatherDataService waterPhWeatherDataService;
    private final StationPointDistributeService stationPointDistributeService;
    private final StationPointDistributeMapper stationPointDistributeMapper;

    /**
     * 查询
     * @param request
     * @return
     */
    @GetMapping
    public R<Page<WaterPhWeatherDataDto>> selectAll(WaterPhWeatherDataRequest request) {
        Page<WaterPhWeatherDataDto> ret = waterPhWeatherDataService.queryPage(request);
        return R.ok(ret);
    }


    /**
     * 修改
     * @param data
     * @return
     */
    @PatchMapping
    public R<Void> update(@RequestBody WaterPhWeatherData data) {
        waterPhWeatherDataService.updateById(data);
        return R.ok();
    }

    /**
     * 新增
     * @param data
     * @return
     */
    @PostMapping
    public R<Void> insert(@RequestBody WaterPhWeatherData data) {
        waterPhWeatherDataService.save(data);
        return R.ok();
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @DeleteMapping("{id}")
    public R<Void> deleteById(@PathVariable Integer id) {
        waterPhWeatherDataService.removeById(id);
        return R.ok();
    }

    /**
     * 根据站点ID获取水文气象数据
     * @param stationId 站点ID
     * @return 水文气象数据列表
     */
    @GetMapping("/by-station/{stationId}")
    public R<List<WaterPhWeatherDataDto>> getByStationId(@PathVariable Integer stationId) {
        List<WaterPhWeatherDataDto> list = waterPhWeatherDataService.getByStationId(stationId);
        return R.ok(list);
    }
    /**
     * 导入Excel
     */
    @PostMapping("/import")
    public R<String> importExcel(@RequestParam("file") MultipartFile file) {
        try {
            // 读取Excel文件
            List<cn.dhbin.isme.ims.domain.dto.excel.WaterPhWeatherDataExcelDto> list = EasyExcel.read(file.getInputStream())
                    .head(cn.dhbin.isme.ims.domain.dto.excel.WaterPhWeatherDataExcelDto.class)
                    .sheet()
                    .doReadSync();

            // 转换DTO到实体类
            List<WaterPhWeatherData> entities = list.stream()
                    .map(dto -> {
                        // 验证和设置必要字段
                        WaterPhWeatherData entity = new WaterPhWeatherData();

                        // 处理站点ID
                        Integer distributeId = dto.getDistributeId();

                        // 如果没有提供站点ID，尝试通过名称查找
                        if (distributeId == null && dto.getDistributeName() != null && !dto.getDistributeName().trim().isEmpty()) {
                            LambdaQueryWrapper<StationPointDistribute> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(StationPointDistribute::getName, dto.getDistributeName().trim());
                            StationPointDistribute distribute = stationPointDistributeMapper.selectOne(queryWrapper);
                            if (distribute != null) {
                                distributeId = distribute.getId();
                            }
                        }

                        if (distributeId == null) {
                            throw new BizException(BizResponseCode.ERR_11013, "无法确定站点ID，请提供站点ID或有效的站点名称");
                        }

                        entity.setDistributeId(distributeId);

                        // 处理采样层次
                        String sampleLayerStr = dto.getSampleLayer();
                        if (sampleLayerStr != null) {
                            if ("底层".equals(sampleLayerStr)) {
                                entity.setSampleLayer(1);
                            } else if ("表层".equals(sampleLayerStr)) {
                                entity.setSampleLayer(2);
                            } else {
                                throw new BizException(BizResponseCode.ERR_11013, "采样层次必须是'底层'或'表层'");
                            }
                        }

                        // 设置其他字段
                        entity.setSaltExtent(dto.getSaltExtent() != null ? BigDecimal.valueOf(dto.getSaltExtent()) : null);
                        entity.setPhExtent(dto.getPhExtent() != null ? BigDecimal.valueOf(dto.getPhExtent()) : null);
                        entity.setTransparentExtent(dto.getTransparentExtent() != null ? BigDecimal.valueOf(dto.getTransparentExtent()) : null);
                        entity.setWaterTemperature(dto.getWaterTemperature() != null ? BigDecimal.valueOf(dto.getWaterTemperature()) : null);
                        entity.setAirTemperature(dto.getAirTemperature() != null ? BigDecimal.valueOf(dto.getAirTemperature()) : null);
                        entity.setWeather(dto.getWeather());
                        entity.setWindDirection(dto.getWindDirection());
                        return entity;
                    })
                    .collect(Collectors.toList());

            if (entities.isEmpty()) {
                return R.build(new BizException(BizResponseCode.ERR_11013, "没有有效数据需要导入"));
            }

            boolean result = waterPhWeatherDataService.saveBatch(entities);
            return result ? R.ok("导入成功") : R.build(new BizException(BizResponseCode.ERR_11013, "导入失败"));
        } catch (BizException e) {
            return R.build(e);
        } catch (Exception e) {
            e.printStackTrace();
            return R.build(new BizException(BizResponseCode.ERR_11013, "导入失败: " + e.getMessage()));
        }
    }

    /**
     * 导出Excel
     */
    @GetMapping("/export")
    public void exportExcel(WaterPhWeatherDataRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<WaterPhWeatherDataDto> list = waterPhWeatherDataService.queryList(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "水环境监测数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, WaterPhWeatherDataExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("水环境监测数据")
                    .doWrite(list.stream().map(dto -> {
                        WaterPhWeatherDataExcelDto excelDto = new WaterPhWeatherDataExcelDto();
                        excelDto.setId(String.valueOf(dto.getId()));
                        excelDto.setDistributeId(dto.getDistributeId());

                        // 设置站点名称
                        if (dto.getStationPointDistribute() != null) {
                            excelDto.setDistributeName(dto.getStationPointDistribute().getName());
                        }

                        // 设置采样层次
                        excelDto.setSampleLayer(dto.getSampleLayer() == 1 ? "底层" : "表层");

                        // 设置天气相关字段
                        excelDto.setWeather(dto.getWeather());
                        excelDto.setWindDirection(dto.getWindDirection());

                        // 设置盐度和PH值
                        excelDto.setSaltExtent(dto.getSaltExtent() != null ? dto.getSaltExtent().doubleValue() : null);
                        excelDto.setPhExtent(dto.getPhExtent() != null ? dto.getPhExtent().doubleValue() : null);

                        // 设置温度
                        excelDto.setAirTemperature(dto.getAirTemperature() != null ? dto.getAirTemperature().doubleValue() : null);
                        excelDto.setWaterTemperature(dto.getWaterTemperature() != null ? dto.getWaterTemperature().doubleValue() : null);

                        // 设置透明度
                        excelDto.setTransparentExtent(dto.getTransparentExtent() != null ? dto.getTransparentExtent().doubleValue() : null);

                        return excelDto;
                    }).collect(Collectors.toList()));

        } catch (Exception e) {
            // 异常处理：返回500状态码
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("{\"code\":500,\"msg\":\"导出失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 导出大气环境数据Excel
     */
    @GetMapping("/export/air")
    public void exportAirExcel(WaterPhWeatherDataRequest request, HttpServletResponse response) throws IOException {
        try (OutputStream out = response.getOutputStream()) {
            List<WaterPhWeatherDataDto> list = waterPhWeatherDataService.queryList(request);

            // 设置响应头
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = "大气环境监测数据_" + LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment; filename*=UTF-8''" + encodedFileName + ".xlsx");

            // 显式设置200状态码
            response.setStatus(HttpServletResponse.SC_OK);

            // 写入Excel
            EasyExcel.write(out, AirEnvironmentalDataExcelDto.class)
                    .autoCloseStream(true)
                    .sheet("大气环境监测数据")
                    .doWrite(list.stream().map(dto -> {
                        AirEnvironmentalDataExcelDto excelDto = new AirEnvironmentalDataExcelDto();
                        excelDto.setId(String.valueOf(dto.getId()));

                        // 设置站点名称
                        if (dto.getStationPointDistribute() != null) {
                            excelDto.setDistributeName(dto.getStationPointDistribute().getName());
                        }

                        // 设置采样层次
                        excelDto.setSampleLayer(dto.getSampleLayer() == 1 ? "底层" : "表层");

                        // 设置气象数据
                        excelDto.setAirTemperature(dto.getAirTemperature() != null ? dto.getAirTemperature().doubleValue() : null);
                        excelDto.setWeather(dto.getWeather());
                        excelDto.setWindDirection(dto.getWindDirection());

                        return excelDto;
                    }).collect(Collectors.toList()));
        }
    }


}

package cn.dhbin.isme.ims.service.impl;

import cn.dhbin.isme.common.response.Page;
import cn.dhbin.isme.ims.domain.dto.SelectOptionDto;
import cn.dhbin.isme.ims.domain.entity.MorphologicalAnalysisData;
import cn.dhbin.isme.ims.domain.entity.SampleType;
import cn.dhbin.isme.ims.domain.entity.StationPointDistribute;
import cn.dhbin.isme.ims.domain.request.SampleTypeRequest;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.dhbin.isme.ims.mapper.SampleTypeMapper;
import cn.dhbin.isme.ims.service.SampleTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 样品种类表(SampleType)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-27 16:37:24
 */
@Service("sampleTypeService")
public class SampleTypeServiceImpl extends ServiceImpl<SampleTypeMapper, SampleType> implements SampleTypeService {

    @Autowired
    private SampleTypeMapper sampleTypeMapper;

    @Override
    public List<SampleType> listSampleTypes() {
        List<SampleType> stationPoints = sampleTypeMapper.selectList(null);
        return stationPoints;
    }

    @Override
    public Page<SampleType> queryPage(SampleTypeRequest request) {
        IPage<SampleType> qp = request.toPage();
        LambdaQueryWrapper<SampleType> queryWrapper = new LambdaQueryWrapper<>();

        if (request.getName() != null) {
            queryWrapper.like(SampleType::getName, request.getName());
        }

        IPage<SampleType> ret = sampleTypeMapper.selectPage(qp, queryWrapper);


        return Page.convert(ret);
    }
}


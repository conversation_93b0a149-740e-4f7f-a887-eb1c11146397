package cn.dhbin.isme.ims.domain.entity;


import cn.dhbin.mapstruct.helper.core.Convert;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("analysis_sample")
public class AnalysisSample implements Convert {
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 微观繁殖体id
     **/
    private Integer abundanceId;
    
    /**
     * 种类id
     **/
    private Integer sampleId;

    private Double number;
    
public Serializable pkVal() {
          return null;
      }
}



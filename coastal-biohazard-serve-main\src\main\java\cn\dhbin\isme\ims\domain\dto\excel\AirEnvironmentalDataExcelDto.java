package cn.dhbin.isme.ims.domain.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 大气环境数据 Excel 导入导出DTO
 */
@Data
public class AirEnvironmentalDataExcelDto {
    @ExcelProperty(value = "序号", index = 0)
    @ColumnWidth(10)
    private String id;

    @ExcelProperty("站点名称")
    @ColumnWidth(20)
    private String distributeName;
    
    @ExcelProperty("采样层次")
    @ColumnWidth(15)
    private String sampleLayer;
    
    @ExcelProperty("气温(℃)")
    @ColumnWidth(15)
    private Double airTemperature;
    
    @ExcelProperty("天气现象")
    @ColumnWidth(20)
    private String weather;
    
    @ExcelProperty("风向")
    @ColumnWidth(15)
    private String windDirection;
} 
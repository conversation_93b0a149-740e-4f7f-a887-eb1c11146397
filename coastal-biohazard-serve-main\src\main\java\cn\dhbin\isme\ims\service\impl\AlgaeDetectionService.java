package cn.dhbin.isme.ims.service.impl;


import cn.dhbin.isme.ims.domain.entity.AlgaeDetectionRecord;
import cn.dhbin.isme.ims.domain.entity.Biodiversity;
import cn.dhbin.isme.ims.mapper.AlgaeDetectionRecordsMapper;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import weka.classifiers.Classifier;
import weka.classifiers.trees.J48;
import weka.classifiers.trees.RandomForest;
import weka.core.Instances;
import weka.core.converters.ConverterUtils.DataSource;
import weka.core.SerializationHelper;
import weka.core.converters.ConverterUtils;
import weka.filters.Filter;
import weka.filters.unsupervised.attribute.StringToWordVector;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AlgaeDetectionService {

    @Autowired
    private AlgaeDetectionRecordsMapper algaeDetectionRecordMapper;

    //    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    public void cleanDataAndTrainModel() throws Exception {

        List<AlgaeDetectionRecord> originalData = algaeDetectionRecordMapper.getAllRecords();

        // 1. 去除异常值
        List<AlgaeDetectionRecord> noOutliers = removeOutliersByIQR(originalData, "algaeDensity");
        noOutliers = removeOutliersByZScore(noOutliers, "waterTemperature", 3.0);

        // 2. 数据标准化/归一化
        normalizeMinMax(noOutliers, "salinity");

        // 3. 去重
        List<AlgaeDetectionRecord> uniqueData = removeDuplicates(noOutliers, "date", "location", "algaeDensity", "waterTemperature", "salinity", "phValue", "windSpeed", "rainfall", "lightIntensity");

        // 4. 保存为 CSV 文件
        saveToCsv(uniqueData, "cleaned_data_" + LocalDate.now().format(DateTimeFormatter.ISO_DATE) + ".csv");

        // 5. 使用 Weka 进行建模
        trainWekaModel("cleaned_data_" + LocalDate.now().format(DateTimeFormatter.ISO_DATE) + ".csv");
    }

    // 四分位数计算数据中离群点，移除异常值
    private List<AlgaeDetectionRecord> removeOutliersByIQR(List<AlgaeDetectionRecord> data, String columnName) {
        // 创建一个Double类型的列表，用于存放指定列的值
        List<Double> values = data.stream()
                .map(entity -> getValue(entity, columnName)) // 使用流式API映射每个记录的指定列的值
                .collect(Collectors.toList()); // 收集所有值到一个列表中

        // 计算第一四分位数（Q1）
        double q1 = getQuantile(values, 0.25);
        // 计算第三四分位数（Q3）
        double q3 = getQuantile(values, 0.75);
        // 计算四分位距（IQR = Q3 - Q1）
        double iqr = q3 - q1;
        // 计算IQR的下界，用于确定异常值的最小值
        double lowerBound = q1 - 1.5 * iqr;
        // 计算IQR的上界，用于确定异常值的最大值
        double upperBound = q3 + 1.5 * iqr;

        // 使用流式API过滤出指定列的值位于[lowerBound, upperBound]区间内的记录
        return data.stream()
                .filter(entity -> getValue(entity, columnName) >= lowerBound && getValue(entity, columnName) <= upperBound)
                .collect(Collectors.toList()); // 收集过滤后的记录到新的列表中并返回
    }
    private List<AlgaeDetectionRecord> removeOutliersByZScore(List<AlgaeDetectionRecord> data, String columnName, double threshold) {
        List<Double> values = data.stream()
                .map(entity -> getValue(entity, columnName))
                .collect(Collectors.toList());

        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double stdDev = calculateStdDev(values, mean);

        return data.stream()
                .filter(entity -> {
                    double value = getValue(entity, columnName);
                    double zScore = Math.abs((value - mean) / stdDev);
                    return zScore <= threshold;
                })
                .collect(Collectors.toList());
    }

    private void normalizeMinMax(List<AlgaeDetectionRecord> data, String columnName) {
        List<Double> values = data.stream()
                .map(entity -> getValue(entity, columnName))
                .collect(Collectors.toList());

        double min = Collections.min(values);
        double max = Collections.max(values);

        for (AlgaeDetectionRecord entity : data) {
            setValue(entity, columnName, (getValue(entity, columnName) - min) / (max - min));
        }
    }

    private void standardizeZScore(List<AlgaeDetectionRecord> data, String columnName) {
        List<Double> values = data.stream()
                .map(entity -> getValue(entity, columnName))
                .collect(Collectors.toList());

        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0);
        double stdDev = calculateStdDev(values, mean);

        for (AlgaeDetectionRecord entity : data) {
            setValue(entity, columnName, (getValue(entity, columnName) - mean) / stdDev);
        }
    }

    private List<AlgaeDetectionRecord> removeDuplicates(List<AlgaeDetectionRecord> data, String... fields) {
        Set<List<Object>> seen = new HashSet<>();
        List<AlgaeDetectionRecord> uniqueData = new ArrayList<>();

        for (AlgaeDetectionRecord entity : data) {
            List<Object> key = Arrays.stream(fields)
                    .map(fieldName -> getValue(entity, fieldName))
                    .collect(Collectors.toList());

            if (!seen.contains(key)) {
                seen.add(key);
                uniqueData.add(entity);
            }
        }

        return uniqueData;
    }

    private void saveToCsv(List<AlgaeDetectionRecord> data, String filePath) {
        try (FileWriter writer = new FileWriter(filePath);
             CSVPrinter printer = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                     "ID", "Date", "Location", "AlgaeDensity", "WaterTemperature", "Salinity", "PhValue", "WindSpeed", "Rainfall", "LightIntensity", "RiskLevel"))) { // 添加 RiskLevel 到 header

            for (AlgaeDetectionRecord entity : data) {
                printer.printRecord(entity.getId(), entity.getDate(), entity.getLocation(), entity.getAlgaeDensity(), entity.getWaterTemperature(), entity.getSalinity(), entity.getPhValue(), entity.getWindSpeed(), entity.getRainfall(), entity.getLightIntensity(), entity.getRiskLevel()); // 添加 RiskLevel 到记录
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void trainWekaModel(String filePath) throws Exception {
        // 创建 DataSource 实例
        DataSource source = new DataSource(filePath);

        // 读取数据集
        Instances data = source.getDataSet();

        // 确保设置类索引
//

        // 分类模型训练
        trainClassificationModel(data);

        // 回归模型训练
        trainRegressionModel(data);
    }

    private void trainClassificationModel(Instances data) throws Exception {
        data.setClassIndex(data.attribute("RiskLevel").index()); // 假设类标签是 RiskLevel
        System.out.println(data.classIndex() + " classIndex1\n");

        // 输出分类数据集信息
        System.out.println("Classification dataset information:");
        System.out.println("Number of instances: " + data.numInstances());
        System.out.println("Number of attributes: " + data.numAttributes());
        System.out.println("Class attribute name: " + data.classAttribute().name());

        // 创建 RandomForest 分类器实例
        RandomForest classifier = new RandomForest();

        // 设置 RandomForest 的参数
        String[] options = {
                "-I", "10",       // 设置树的数量为 10
                "-M", "2",        // 设置每个叶子节点至少包含的对象数量为 2
                "-S", "1"         // 设置随机种子为 1
        };

        // 使用 setOptions 方法来设置参数
        classifier.setOptions(options);

        // 构建分类器模型
        classifier.buildClassifier(data);

        // 保存模型
        SerializationHelper.write("trained_random_forest_classification.model", classifier);
    }

    private void trainRegressionModel(Instances data) throws Exception {
        // 需要将类索引设置为 AlgaeDensity 的索引以进行回归预测
        data.setClassIndex(data.attribute("AlgaeDensity").index());
        System.out.println(data.classIndex() + " classIndex2\n");

        // 输出回归数据集信息
        System.out.println("Regression dataset information:");
        System.out.println("Number of instances: " + data.numInstances());
        System.out.println("Number of attributes: " + data.numAttributes());
        System.out.println("Class attribute name: " + data.classAttribute().name());

        // 创建 RandomForest 回归器实例
        RandomForest regressor = new RandomForest();

        // 设置 RandomForest 的参数
        String[] options = {
                "-I", "10",       // 设置树的数量为 10
                "-M", "2",        // 设置每个叶子节点至少包含的对象数量为 2
                "-S", "1"         // 设置随机种子为 1
        };

        // 使用 setOptions 方法来设置参数
        regressor.setOptions(options);

        // 构建回归器模型
        regressor.buildClassifier(data);

        // 保存模型
        SerializationHelper.write("trained_random_forest_regression.model", regressor);
    }

    private double getValue(AlgaeDetectionRecord entity, String columnName) {
        switch (columnName) {
            case "date":
                return entity.getDate().getTime(); // Convert Date to long timestamp
            case "location":
                return entity.getLocation().hashCode(); // Convert String to hash code
            case "algaeDensity":
                return entity.getAlgaeDensity();
            case "waterTemperature":
                return entity.getWaterTemperature();
            case "salinity":
                return entity.getSalinity();
            case "phValue":
                return entity.getPhValue();
            case "windSpeed":
                return entity.getWindSpeed();
            case "rainfall":
                return entity.getRainfall();
            case "lightIntensity":
                return entity.getLightIntensity();
            case "riskLevel":
                return entity.getRiskLevel().hashCode(); // 返回名义型属性值
            default:
                throw new IllegalArgumentException("Unknown column name: " + columnName);
        }
    }

    private void setValue(AlgaeDetectionRecord entity, String columnName, double value) {
        switch (columnName) {
            case "date":
                entity.setDate(new Date((long) value));
                break;
            case "location":
                entity.setLocation(String.valueOf(value));
                break;
            case "algaeDensity":
                entity.setAlgaeDensity(value);
                break;
            case "waterTemperature":
                entity.setWaterTemperature(value);
                break;
            case "salinity":
                entity.setSalinity(value);
                break;
            case "phValue":
                entity.setPhValue(value);
                break;
            case "windSpeed":
                entity.setWindSpeed(value);
                break;
            case "rainfall":
                entity.setRainfall(value);
                break;
            case "lightIntensity":
                entity.setLightIntensity(value);
                break;
            case "riskLevel":
                entity.setRiskLevel(String.valueOf(value));
                break;
            default:
                throw new IllegalArgumentException("未知的字段" + columnName);
        }
    }

    private static double getQuantile(List<Double> values, double quantile) {
        Collections.sort(values);
        int index = (int) Math.ceil(quantile * values.size());
        return values.get(index - 1);
    }

    private static double calculateStdDev(List<Double> values, double mean) {
        double sum = values.stream()
                .mapToDouble(v -> Math.pow(v - mean, 2))
                .sum();
        return Math.sqrt(sum / values.size());
    }
}